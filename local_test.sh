
# Set hardcoded settings for script parameters
MODE="single"
TYPE="lender"
FILE_NAME="ECM_PROD000020251775"
DATE_ID=$(date +%Y%m%d)  # Adjust based on your locale settings
CSV_PATH="./de_results/l${TYPE}_output_${DATE_ID}.csv"
TO_EXTRACT_FIELD=""
CLASSIFICATION_REQUIRED="1"  # 1 represents true, 0 represents false

# Print the configuration to confirm settings
echo "Running document extraction with the following settings:"
echo "Mode: $MODE"
echo "Type: $TYPE"
echo "File Name: $FILE_NAME"
echo "Date ID: $DATE_ID"
echo "CSV Path: $CSV_PATH"
echo "To Extract Field: $TO_EXTRACT_FIELD"
echo "Classification Required: $CLASSIFICATION_REQUIRED"

# Execute the Python script with the specified parameters
python local_test.py \
  --mode "$MODE" \
  --type "$TYPE" \
  --file_name "$FILE_NAME" \
  --date_id "$DATE_ID" \
  --csv_path "$CSV_PATH" \
  --to_extract_field "$TO_EXTRACT_FIELD" \
  --classification_required "$CLASSIFICATION_REQUIRED"
