# Comprehensive Multi-Variant Document Classification Guide

## Overview

This guide provides a complete solution for classifying and extracting data from your 6 document variants using the existing `Classify.py` system.

## Document Variants Identified

Based on your OCR samples, I've identified these document types:

1. **1003 Application Form** - Uniform Residential Loan Application
2. **ACH Authorization Form** - Autopay setup forms (Spring EQ)
3. **Closing Disclosure** - Final loan terms and closing costs
4. **Appraisal Report** - Property valuation documents
5. **Loan Estimate** - Initial loan terms estimate  
6. **Deed of Trust** - Security instrument documents

## Files Created

### 1. Main Classification Configuration
- `docvu_de_core/de_config/multi_variant_classify_config.json` - Master classification rules

### 2. Data Extraction Configurations
- `docvu_de_core/de_config/1003_application_de.json` - 1003 form extraction rules
- `docvu_de_core/de_config/ach_authorization_de.json` - ACH form extraction rules
- `docvu_de_core/de_config/closing_disclosure_de.json` - Closing disclosure extraction rules
- `docvu_de_core/de_config/appraisal_report_de.json` - Appraisal report extraction rules
- `docvu_de_core/de_config/loan_estimate_de.json` - Loan estimate extraction rules
- `docvu_de_core/de_config/deed_of_trust_de.json` - Deed of trust extraction rules

## How to Implement

### Step 1: Test the Configuration

```python
# Test the new classification system
from docvu_de_core.policy_classifier.Classify import DocumentClassifier

# Initialize with new configuration
classifier = DocumentClassifier('docvu_de_core/de_config/multi_variant_classify_config.json')

# Test with your sample files
test_files = [
    'ocr_output/1003_Application_(100)_textract_ocr/combined.json',
    'ocr_output/ACH_Authorization_Form_(1)_textract_ocr/combined.json', 
    'ocr_output/Closing_Disclosure_(1)_textract_ocr/combined.json'
]

for test_file in test_files:
    result = classifier.classify_document(test_file)
    print(f"File: {test_file}")
    print(f"Classification Result: {result}")
    print("---")
```

### Step 2: Validate Classifications

Expected results:
- **1003 Application files** → `1003_application_de.json`
- **ACH Authorization files** → `ach_authorization_de.json`
- **Closing Disclosure files** → `closing_disclosure_de.json`

### Step 3: Fine-tune if Needed

If classifications are incorrect:

1. **Enable debug mode** in the config:
```json
{
    "debug": true,
    ...
}
```

2. **Check what text patterns are found** in the debug output

3. **Adjust include/exclude strings** based on actual OCR content

## Key Classification Features

### 1003 Application Form
- **Identifies by**: "1003", "Application", "UNIFORM RESIDENTIAL LOAN APPLICATION"
- **Extracts**: Borrower info, property details, loan information
- **Excludes**: Closing documents, ACH forms

### ACH Authorization Form  
- **Identifies by**: "Sign Up For Autopay", "ACH Authorization", "Spring EQ"
- **Extracts**: Banking info, payment details, contact information
- **Excludes**: Loan applications, closing documents

### Closing Disclosure
- **Identifies by**: "Closing Disclosure", "final loan terms", "Compare this document"
- **Extracts**: Loan terms, payment details, closing costs
- **Excludes**: Loan estimates, applications

## Customization Options

### Adding Your Specific Variants

If you have additional variants, add them to the configuration:

```json
"your_variant_name": {
    "return": "your_variant_de.json",
    "header": {
        "include_strings": ["UNIQUE TEXT IN YOUR VARIANT"],
        "exclude_strings": ["TEXT FROM OTHER VARIANTS"]
    },
    "body": {
        "include_strings": ["BODY CONTENT SPECIFIC TO VARIANT"],
        "exclude_strings": []
    }
}
```

### Modifying Existing Classifications

Update the patterns in `multi_variant_classify_config.json`:

```json
"1003_application_form": {
    "header": {
        "include_strings": [
            "1003",
            "Application", 
            "YOUR SPECIFIC TEXT"  // Add your specific patterns
        ],
        "exclude_strings": [
            "Closing Disclosure",
            "YOUR EXCLUSION TEXT"  // Add exclusions
        ]
    }
}
```

## Troubleshooting

### Common Issues

1. **Wrong classification**
   - Check OCR output for actual text content
   - Add more specific include_strings
   - Add exclude_strings to prevent false matches

2. **No classification (returns default)**
   - Verify include_strings match OCR text exactly
   - Check for typos in configuration
   - Enable debug mode to see what's being matched

3. **Multiple documents matching same type**
   - Add more specific exclude_strings
   - Make include_strings more unique
   - Reorder document types (most specific first)

### Debug Process

1. Enable debug mode: `"debug": true`
2. Run classification on problem document
3. Check debug output for:
   - Text found in header/body/big_font sections
   - Which include_strings matched
   - Which exclude_strings triggered
4. Adjust patterns accordingly

## Testing Checklist

- [ ] 1003 Application forms classify correctly
- [ ] ACH Authorization forms classify correctly  
- [ ] Closing Disclosure documents classify correctly
- [ ] No false positives between document types
- [ ] Default fallback works for unknown documents
- [ ] Debug mode provides useful information
- [ ] All extraction configurations are valid JSON

## Next Steps

1. **Test with your documents** using the provided test script
2. **Review classification results** and note any errors
3. **Fine-tune patterns** based on your specific document content
4. **Add any missing document variants** you need
5. **Deploy to your production environment**
6. **Monitor classification accuracy** over time

## Support

If you need help:
1. Enable debug mode and share the output
2. Provide sample OCR text from misclassified documents
3. Specify which document types are being confused
4. Share any unique text patterns in your variants

The system is designed to be flexible and easily customizable for your specific document variants.
