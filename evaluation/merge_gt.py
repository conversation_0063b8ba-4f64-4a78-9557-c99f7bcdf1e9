import os
import pandas as pd

def search_and_merge_excel_files(folder_path, output_file):
    all_data = []
    expected_columns = None
    
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.startswith("transformed_gt") and file.endswith(".xlsx"):
                file_path = os.path.join(root, file)
                try:
                    df = pd.read_excel(file_path)
                    
                    if expected_columns is None:
                        expected_columns = df.columns.tolist()
                    elif df.columns.tolist() != expected_columns:
                        print(f"Column mismatch in {file_path}, skipping file.")
                        continue
                    
                    all_data.append(df)
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    if all_data:
        merged_df = pd.concat(all_data, ignore_index=True)
        merged_df.to_excel(output_file, index=False)
        print(f"Merged data saved to {output_file}")
    else:
        print("No matching Excel files found or valid for merging.")

if __name__ == "__main__":
    folder_path = "/Users/<USER>/Downloads/post closing/Mapping Team/Annotation and GT/GT/9002148 - Identification Verification (Patriot Act)/variant"
    output_file = folder_path + "/merged_results.xlsx"
    search_and_merge_excel_files(folder_path, output_file)
