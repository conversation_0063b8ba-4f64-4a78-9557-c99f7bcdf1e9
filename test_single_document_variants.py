#!/usr/bin/env python3
"""
Test script for single document with multiple variants (5-6 variants)
Tests the enhanced Classify.py for handling variants of the same document type
"""

import json
import os
from docvu_de_core.policy_classifier.Classify import Classify


def test_single_document_variants():
    """Test the enhanced classifier with multiple variants of a single document type."""
    
    print("=" * 70)
    print("TESTING SINGLE DOCUMENT WITH MULTIPLE VARIANTS")
    print("=" * 70)
    print("Testing 1003 Form with 6 different variants:")
    print("1. Original 1003 Form")
    print("2. Revised 1003 Form") 
    print("3. Spanish/Bilingual 1003 Form")
    print("4. Short/Abbreviated 1003 Form")
    print("5. Digital/Electronic 1003 Form")
    print("6. State-Specific 1003 Form")
    print("=" * 70)
    
    # Load the configuration for single document variants
    config_path = 'docvu_de_core/de_config/single_document_multi_variant_config.json'
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        return False
    
    # Initialize classifier
    classifier = Classify(config_path)
    
    # Test files - these should be different variants of 1003 forms
    test_files = [
        {
            'path': 'ocr_output/1003_Application_(100)_textract_ocr/combined.json',
            'description': '1003 Application Variant 1',
            'expected_variant': '1003_variant_1_original'
        },
        {
            'path': 'ocr_output/1003_Application_(101)_textract_ocr/combined.json', 
            'description': '1003 Application Variant 2',
            'expected_variant': '1003_variant_2_revised'
        }
    ]
    
    results = []
    
    for i, test_file in enumerate(test_files, 1):
        print(f"\n--- Test {i}: {test_file['description']} ---")
        print(f"📁 File: {test_file['path']}")
        print(f"🎯 Expected: {test_file['expected_variant']}")
        
        if not os.path.exists(test_file['path']):
            print(f"❌ File not found: {test_file['path']}")
            results.append({
                'test': i,
                'file': test_file['path'],
                'expected': test_file['expected_variant'],
                'actual': 'FILE_NOT_FOUND',
                'confidence': 0.0,
                'success': False
            })
            continue
        
        try:
            # Run classification
            result = classifier.process_form(test_file['path'])
            
            # Extract results
            variant_detected = result.get('variant_detected', 'unknown')
            confidence_score = result.get('confidence_score', 0.0)
            document_type = result.get('document_type', 'unknown')
            classification_method = result.get('classification_method', 'unknown')
            
            # Check success
            success = variant_detected == test_file['expected_variant']
            
            print(f"✅ Variant detected: {variant_detected}")
            print(f"📊 Confidence score: {confidence_score:.2f}")
            print(f"📄 Document type: {document_type}")
            print(f"🔧 Method: {classification_method}")
            print(f"🎯 Success: {'YES' if success else 'NO'}")
            
            # Show all variant scores
            if result.get('all_variant_scores'):
                print("📈 All variant scores:")
                sorted_scores = sorted(result['all_variant_scores'].items(), 
                                     key=lambda x: x[1], reverse=True)
                for variant, score in sorted_scores:
                    indicator = "🏆" if variant == variant_detected else "  "
                    print(f"   {indicator} {variant}: {score:.2f}")
            
            # Show detailed breakdown if available
            if result.get('variant_details') and result['variant_details'].get('section_scores'):
                print("🔍 Section breakdown:")
                for section, score in result['variant_details']['section_scores'].items():
                    print(f"   {section}: {score:.2f}")
            
            results.append({
                'test': i,
                'file': test_file['path'],
                'expected': test_file['expected_variant'],
                'actual': variant_detected,
                'confidence': confidence_score,
                'success': success,
                'method': classification_method
            })
            
        except Exception as e:
            print(f"❌ Error processing file: {e}")
            results.append({
                'test': i,
                'file': test_file['path'],
                'expected': test_file['expected_variant'],
                'actual': 'ERROR',
                'confidence': 0.0,
                'success': False,
                'error': str(e)
            })
    
    # Print summary
    print("\n" + "=" * 70)
    print("VARIANT DETECTION SUMMARY")
    print("=" * 70)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['success'])
    
    print(f"📊 Total tests: {total_tests}")
    print(f"✅ Successful: {successful_tests}")
    print(f"❌ Failed: {total_tests - successful_tests}")
    print(f"📈 Success rate: {(successful_tests/total_tests)*100:.1f}%" if total_tests > 0 else "No tests run")
    
    print("\n📋 Detailed Results:")
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"{result['test']}. {status} {os.path.basename(result['file'])}")
        print(f"   Expected: {result['expected']}")
        print(f"   Actual: {result['actual']}")
        print(f"   Confidence: {result['confidence']:.2f}")
        if 'error' in result:
            print(f"   Error: {result['error']}")
        if 'method' in result:
            print(f"   Method: {result['method']}")
    
    return results


def create_sample_variant_data():
    """Create sample data to demonstrate how different variants would look."""
    
    print("\n" + "=" * 70)
    print("SAMPLE VARIANT CHARACTERISTICS")
    print("=" * 70)
    
    variants = {
        "1003_variant_1_original": {
            "description": "Original 1003 Form",
            "key_identifiers": [
                "1003",
                "UNIFORM RESIDENTIAL LOAN APPLICATION", 
                "SECTION I",
                "BORROWER INFORMATION"
            ],
            "distinguishing_features": [
                "Standard layout",
                "Single borrower focus",
                "Traditional sections"
            ]
        },
        "1003_variant_2_revised": {
            "description": "Revised 1003 Form",
            "key_identifiers": [
                "1003",
                "REVISED",
                "NEW VERSION",
                "UPDATED FORM"
            ],
            "distinguishing_features": [
                "Co-borrower sections",
                "Joint credit options",
                "Updated field layouts"
            ]
        },
        "1003_variant_3_spanish": {
            "description": "Spanish/Bilingual 1003 Form",
            "key_identifiers": [
                "1003",
                "SPANISH",
                "SOLICITUD",
                "PRESTAMO",
                "BILINGUAL"
            ],
            "distinguishing_features": [
                "Spanish text",
                "Bilingual sections",
                "Translated field labels"
            ]
        },
        "1003_variant_4_short_form": {
            "description": "Short/Abbreviated 1003 Form",
            "key_identifiers": [
                "1003",
                "SHORT FORM",
                "ABBREVIATED",
                "SIMPLIFIED"
            ],
            "distinguishing_features": [
                "Fewer sections",
                "Essential fields only",
                "Streamlined layout"
            ]
        },
        "1003_variant_5_digital": {
            "description": "Digital/Electronic 1003 Form",
            "key_identifiers": [
                "1003",
                "DIGITAL",
                "ELECTRONIC",
                "E-FORM"
            ],
            "distinguishing_features": [
                "Digital signature fields",
                "Electronic consent",
                "Online submission markers"
            ]
        },
        "1003_variant_6_state_specific": {
            "description": "State-Specific 1003 Form",
            "key_identifiers": [
                "1003",
                "STATE SPECIFIC",
                "CALIFORNIA",
                "STATE REQUIREMENTS"
            ],
            "distinguishing_features": [
                "State-specific disclosures",
                "Local regulation compliance",
                "Additional state fields"
            ]
        }
    }
    
    for variant_id, info in variants.items():
        print(f"\n🔹 {info['description']} ({variant_id})")
        print(f"   Key Identifiers: {', '.join(info['key_identifiers'])}")
        print(f"   Features: {', '.join(info['distinguishing_features'])}")
    
    print(f"\n💡 To add your own variants:")
    print(f"   1. Identify unique text patterns in each variant")
    print(f"   2. Add them to the configuration file")
    print(f"   3. Test with sample documents")
    print(f"   4. Adjust confidence threshold as needed")


if __name__ == '__main__':
    # Run the variant detection test
    test_results = test_single_document_variants()
    
    # Show sample variant characteristics
    create_sample_variant_data()
    
    print(f"\n" + "=" * 70)
    print("NEXT STEPS")
    print("=" * 70)
    print("1. 📝 Update the configuration with your specific variant patterns")
    print("2. 🧪 Test with your actual document samples")
    print("3. ⚙️ Adjust confidence threshold based on results")
    print("4. 🔧 Fine-tune include/exclude patterns for better accuracy")
    print("5. 📊 Monitor classification performance over time")
    
    print(f"\n📁 Configuration file: docvu_de_core/de_config/single_document_multi_variant_config.json")
    print(f"🔧 Enhanced classifier: docvu_de_core/policy_classifier/Classify.py")
    print(f"🧪 Test script: {__file__}")
    
    print(f"\n🚀 Your enhanced classifier is ready for 5-6 document variants!")
