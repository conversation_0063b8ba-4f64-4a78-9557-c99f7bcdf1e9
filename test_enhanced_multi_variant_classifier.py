#!/usr/bin/env python3
"""
Test script for the enhanced multi-variant document classifier.
Demonstrates classification of 5-6 document variants with confidence scoring.
"""

import json
import os
from docvu_de_core.policy_classifier.Classify import Classify


def create_sample_test_data():
    """Create sample test data for different document variants."""
    
    # Sample 1003 Application Form V1 data
    sample_1003_v1 = {
        1: ["1003", "UNIFORM RESIDENTIAL LOAN APPLICATION", "SECTION I", "BORROWER INFORMATION", "Property Address"],
        2: ["Borrower", "Co-Borrower", "Loan Amount", "Purpose of Loan"]
    }
    
    # Sample 1003 Application Form V2 data (revised version)
    sample_1003_v2 = {
        1: ["1003", "REVISED", "UNIFORM RESIDENTIAL LOAN APPLICATION", "NEW VERSION", "Joint Credit"],
        2: ["Co-Borrower Information", "Employment Information", "Monthly Income"]
    }
    
    # Sample ACH Authorization Form data
    sample_ach_auth = {
        1: ["ACH Authorization", "Spring EQ", "Sign Up For Autopay", "Electronic Payment"],
        2: ["Bank Account", "Routing Number", "Account Number", "Authorization"]
    }
    
    # Sample Closing Disclosure data
    sample_closing_disclosure = {
        1: ["Closing Disclosure", "Final Terms", "Loan Terms", "Projected Payments"],
        2: ["Loan Amount", "Interest Rate", "Monthly Principal", "Closing Costs"]
    }
    
    # Sample Appraisal Report data
    sample_appraisal = {
        1: ["Appraisal Report", "Property Valuation", "Market Value", "URAR"],
        2: ["Subject Property", "Comparable Sales", "Market Analysis", "Appraised Value"]
    }
    
    # Sample Loan Estimate data
    sample_loan_estimate = {
        1: ["Loan Estimate", "Estimated Terms", "Projected Payments", "Loan Details"],
        2: ["Estimated Monthly Payment", "Estimated Closing Costs", "Estimated Cash to Close"]
    }
    
    return {
        "1003_v1": sample_1003_v1,
        "1003_v2": sample_1003_v2,
        "ach_auth": sample_ach_auth,
        "closing_disclosure": sample_closing_disclosure,
        "appraisal": sample_appraisal,
        "loan_estimate": sample_loan_estimate
    }


def create_sample_height_data(text_data):
    """Create corresponding height data for text data."""
    height_data = {}
    for page, texts in text_data.items():
        height_data[page] = [12 + (i % 3) * 2 for i in range(len(texts))]  # Varying heights
    return height_data


def test_enhanced_classifier():
    """Test the enhanced multi-variant classifier."""
    
    print("=" * 80)
    print("ENHANCED MULTI-VARIANT DOCUMENT CLASSIFIER TEST")
    print("=" * 80)
    
    # Load configuration
    config_path = 'docvu_de_core/de_config/multi_variant_config.json'
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        print("Please ensure the multi_variant_config.json file exists.")
        return False
    
    # Initialize classifier
    classifier = Classify(config_path)
    
    # Get sample test data
    test_samples = create_sample_test_data()
    
    print(f"🔧 Classifier Configuration:")
    print(f"   • Multi-variant enabled: {classifier.enable_multi_variant}")
    print(f"   • Confidence scoring: {classifier.enable_confidence_scoring}")
    print(f"   • Confidence threshold: {classifier.confidence_threshold}")
    print(f"   • Legacy mode: {classifier.use_legacy_mode}")
    print(f"   • Case sensitive: {classifier.case_sensitive_matching}")
    print(f"   • Partial matching: {classifier.partial_string_matching}")
    print()
    
    results = []
    
    for sample_name, text_data in test_samples.items():
        print(f"🧪 Testing: {sample_name}")
        print("-" * 50)
        
        # Create height data
        height_data = create_sample_height_data(text_data)
        
        # Classify document
        result = classifier.classify_document(text_data, height_data)
        
        # Display results
        print(f"📄 Document Type: {result.get('document_type', 'Unknown')}")
        print(f"🏷️  Variant Name: {result.get('variant_name', 'Unknown')}")
        print(f"📊 Confidence: {result.get('confidence', 0.0):.2f}")
        print(f"✅ Status: {result.get('classification_status', 'Unknown')}")
        
        if 'all_variant_scores' in result:
            print(f"📈 All Variant Scores:")
            for variant, score in result['all_variant_scores'].items():
                print(f"   • {variant}: {score:.2f}")
        
        if 'section_details' in result and result['section_details']:
            print(f"🔍 Section Analysis:")
            for section, details in result['section_details'].items():
                matched = details.get('matched', False)
                conf = details.get('confidence', 0.0)
                print(f"   • {section}: {'✅' if matched else '❌'} (confidence: {conf:.2f})")
        
        results.append({
            'sample': sample_name,
            'result': result
        })
        
        print()
    
    # Summary
    print("=" * 80)
    print("CLASSIFICATION SUMMARY")
    print("=" * 80)
    
    successful_classifications = [r for r in results if r['result'].get('classification_status') == 'success']
    
    print(f"✅ Successful Classifications: {len(successful_classifications)}/{len(results)}")
    print(f"📊 Average Confidence: {sum(r['result'].get('confidence', 0) for r in successful_classifications) / len(successful_classifications):.2f}")
    
    print("\n🎯 Classification Results:")
    for result in results:
        sample = result['sample']
        res = result['result']
        status = "✅" if res.get('classification_status') == 'success' else "❌"
        conf = res.get('confidence', 0.0)
        variant = res.get('variant_name', 'Unknown')
        print(f"   {status} {sample}: {variant} (confidence: {conf:.2f})")
    
    return True


def test_legacy_compatibility():
    """Test backward compatibility with legacy mode."""
    
    print("\n" + "=" * 80)
    print("LEGACY COMPATIBILITY TEST")
    print("=" * 80)
    
    # Create legacy configuration
    legacy_config = {
        "max_upper_block": 3,
        "use_upper_split_percentage": 0.45,
        "debug": True,
        "use_legacy_mode": True,  # Enable legacy mode
        "default_return": "docvu_de_core/de_config/default_de.json",
        "document_types": {
            "old_form": {
                "return": "docvu_de_core/de_config/9000001-1003_de.json",
                "header": {
                    "include_strings": ["1003", "UNIFORM RESIDENTIAL LOAN APPLICATION"],
                    "exclude_strings": [],
                    "length_comparison": False
                },
                "body": {
                    "include_strings": ["Borrower", "Property Address"],
                    "exclude_strings": [],
                    "length_comparison": False
                }
            }
        }
    }
    
    # Initialize classifier in legacy mode
    classifier = Classify(legacy_config)
    
    print(f"🔧 Legacy Mode Configuration:")
    print(f"   • Legacy mode: {classifier.use_legacy_mode}")
    print(f"   • Multi-variant: {classifier.enable_multi_variant}")
    
    # Test with sample data
    sample_data = {1: ["1003", "UNIFORM RESIDENTIAL LOAN APPLICATION", "Borrower", "Property Address"]}
    height_data = {1: [14, 12, 10, 10]}
    
    result = classifier.classify_document(sample_data, height_data)
    
    print(f"\n📄 Legacy Classification Result:")
    print(f"   • Document Type: {result.get('document_type')}")
    print(f"   • Section: {result.get('section')}")
    print(f"   • Starting Page: {result.get('starting_page')}")
    print(f"   • Total Pages: {result.get('total_pages')}")
    
    return True


if __name__ == '__main__':
    print("🚀 ENHANCED MULTI-VARIANT DOCUMENT CLASSIFIER")
    print("Testing enhanced classification with 5-6 document variants")
    print()
    
    # Test enhanced classifier
    success1 = test_enhanced_classifier()
    
    # Test legacy compatibility
    success2 = test_legacy_compatibility()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        print("\n📖 Usage Instructions:")
        print("1. Use the multi_variant_config.json for enhanced classification")
        print("2. Set 'use_legacy_mode': true for backward compatibility")
        print("3. Adjust confidence_threshold based on your requirements")
        print("4. Configure variant_priority_order for preferred document types")
    else:
        print("\n❌ Some tests failed. Please check the configuration and try again.")
