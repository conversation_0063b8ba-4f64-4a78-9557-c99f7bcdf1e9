# Enhanced Multi-Variant Document Classifier

## Overview

The enhanced `Classify.py` has been updated to handle **5-6 document variants** with advanced classification features while maintaining **100% backward compatibility** with existing code.

## 🚀 New Features

### 1. Multi-Variant Classification
- **Supports 5-6+ document variants** simultaneously
- **Confidence scoring** for each variant (0.0 to 1.0)
- **Enhanced pattern matching** with partial string support
- **Priority-based classification** with configurable variant ordering

### 2. Advanced Configuration Options
```json
{
    "enable_multi_variant": true,
    "confidence_threshold": 0.6,
    "enable_confidence_scoring": true,
    "case_sensitive_matching": false,
    "partial_string_matching": true,
    "use_legacy_mode": false,
    "variant_priority_order": ["variant1", "variant2", "variant3"]
}
```

### 3. Enhanced Return Format
```json
{
    "document_type": "path/to/config.json",
    "variant_name": "1003_application_form_v1",
    "confidence": 0.85,
    "classification_status": "success",
    "all_variant_scores": {
        "variant1": 0.85,
        "variant2": 0.42,
        "variant3": 0.15
    },
    "section_details": {
        "header": {"matched": true, "confidence": 0.9},
        "body": {"matched": true, "confidence": 0.8}
    },
    "starting_page": 1,
    "total_pages": 5
}
```

## 📋 Supported Document Variants

The system can classify these document types:

1. **1003 Application Form V1** - Original uniform residential loan application
2. **1003 Application Form V2** - Revised version with joint credit options
3. **ACH Authorization Form** - Electronic payment setup forms
4. **Closing Disclosure** - Final loan terms and closing costs
5. **Appraisal Report** - Property valuation documents
6. **Loan Estimate** - Initial loan terms estimate

## 🔧 Configuration Guide

### Basic Multi-Variant Setup
```json
{
    "enable_multi_variant": true,
    "confidence_threshold": 0.6,
    "document_types": {
        "variant_1": {
            "return": "config1.json",
            "header": {
                "include_strings": ["keyword1", "keyword2"],
                "exclude_strings": ["exclude1"]
            },
            "body": {
                "include_strings": ["body_keyword1"],
                "exclude_strings": []
            }
        },
        "variant_2": {
            "return": "config2.json",
            "header": {
                "include_strings": ["different_keyword"],
                "exclude_strings": []
            }
        }
    }
}
```

### Advanced Features
- **Priority Order**: Set `variant_priority_order` to prefer certain variants
- **Case Sensitivity**: Control with `case_sensitive_matching`
- **Partial Matching**: Enable with `partial_string_matching`
- **Debug Mode**: Set `debug: true` for detailed output

## 💻 Usage Examples

### Enhanced Classification
```python
from docvu_de_core.policy_classifier.Classify import Classify

# Load enhanced configuration
classifier = Classify('docvu_de_core/de_config/multi_variant_config.json')

# Process document
result = classifier.process_form('path/to/document.json')

# Access enhanced results
print(f"Document Type: {result['variant_name']}")
print(f"Confidence: {result['confidence']:.2f}")
print(f"Status: {result['classification_status']}")

# View all variant scores
for variant, score in result['all_variant_scores'].items():
    print(f"{variant}: {score:.2f}")
```

### Legacy Compatibility
```python
# Your existing code works unchanged!
classifier = Classify(existing_config)
result = classifier.process_form('document.json')

# Still returns the same format for backward compatibility
document_type = result['document_type']
starting_page = result['starting_page']
```

### Legacy Mode (Explicit)
```python
# Force legacy behavior
config = {
    "use_legacy_mode": True,
    # ... rest of your existing config
}
classifier = Classify(config)
```

## 🔄 Backward Compatibility

### Automatic Detection
- **Existing configs** automatically use legacy mode
- **New configs** with enhanced features use multi-variant mode
- **No code changes required** for existing implementations

### Legacy Return Format
When using legacy mode or existing configs:
```json
{
    "document_type": "path/to/config.json",
    "section": "header",
    "starting_page": 1,
    "total_pages": 5,
    "large_font_texts": ["text1", "text2"]
}
```

## 🧪 Testing

Run the test script to verify functionality:
```bash
python test_enhanced_multi_variant_classifier.py
```

This tests:
- ✅ Multi-variant classification
- ✅ Confidence scoring
- ✅ Legacy compatibility
- ✅ Enhanced pattern matching

## 📊 Performance Features

### Confidence Scoring Algorithm
1. **Pattern Matching**: Base confidence from matched patterns
2. **Multiple Matches**: Bonus for multiple pattern matches
3. **Priority Boost**: Additional confidence for prioritized variants
4. **Threshold Filtering**: Only return results above confidence threshold

### Enhanced Pattern Matching
- **Partial String Matching**: Find patterns within larger text
- **Case Insensitive**: Flexible text matching
- **Exclude Patterns**: Avoid false positives
- **Multiple Include Patterns**: OR logic for pattern matching

## 🛠️ Troubleshooting

### Low Confidence Scores
- Adjust `confidence_threshold` (default: 0.6)
- Review `include_strings` patterns
- Check for conflicting `exclude_strings`
- Enable `debug: true` for detailed analysis

### Legacy Mode Issues
- Set `use_legacy_mode: true` explicitly
- Ensure existing config structure is maintained
- Check that all required fields are present

### Pattern Matching Problems
- Verify `case_sensitive_matching` setting
- Check `partial_string_matching` configuration
- Review actual document text vs. configured patterns

## 📈 Migration Guide

### From Original Classify.py
1. **No changes required** - existing code continues to work
2. **Optional**: Add enhanced configuration for better results
3. **Gradual migration**: Test with new configs alongside existing ones

### Adding New Variants
1. Add new document type to `document_types`
2. Configure `include_strings` and `exclude_strings`
3. Set appropriate `return` value
4. Test with sample documents
5. Adjust `confidence_threshold` if needed

## 🎯 Best Practices

1. **Start with legacy mode** for existing systems
2. **Test thoroughly** before enabling multi-variant mode
3. **Use descriptive variant names** for easier debugging
4. **Set appropriate confidence thresholds** based on your data
5. **Monitor classification results** and adjust patterns as needed
6. **Use priority ordering** for business-critical document types
