class OCRUtils:
    
    @classmethod
    def sort_and_merge_ocr_data(cls, text_lines, vpos_tolerance = 7, merge_lines = True):
        # Extract the list of words from the 'STRING' key
        sorted_lines = text_lines
        
        if len(sorted_lines) == 0:
            return sorted_lines
        
        for idx in range(len(sorted_lines)):
            if not isinstance(sorted_lines[idx], dict):
                sorted_lines.pop(idx)
        
        # Sort the list of words by 'VPOS'
        sorted_lines.sort(key=lambda x: (int(x['VPOS']), int(x['HPOS'])))
        
        # Step 2: Group by lines considering VPOS tolerance
        lines_grouped = []
        current_line = [sorted_lines[0]]  # Start with the first word
        
        for current_text in sorted_lines[1:]:
            # If the current text is within VPOS tolerance, it's part of the current line
            if abs(int(current_text['VPOS']) - int(current_line[-1]['VPOS'])) <= vpos_tolerance:
                current_line.append(current_text)
            else:
                # Sort the current line by HPOS and add to the grouped lines
                current_line.sort(key=lambda x: int(x['HPOS']))
                lines_grouped.append(current_line)
                current_line = [current_text]  # Start a new line group
        
        # Don't forget to add the last line
        current_line.sort(key=lambda x: int(x['HPOS']))
        lines_grouped.append(current_line)
        
        if merge_lines:
            # Step 3: Flatten the grouped lines back into a single list
            sorted_and_grouped_lines = [item for group in lines_grouped for item in group]
            
            return sorted_and_grouped_lines
        else:
            return lines_grouped
    
    @classmethod
    def remove_substring_from_elements(cls, elements, substring):
        """
        Removes a substring from the 'text' field of the first element in a list of elements,
        or from a single element. If the first element's 'text' exactly matches the substring,
        it is removed from the list.

        :param elements: A single dictionary or a list of dictionaries representing elements.
                        Each element has a 'text' field among other properties.
        :param substring: The substring to remove from the first element's 'text'.
        :return: A list of modified elements.
        """
        modified_elements = []
        # Ensure elements is a list for easier processing
        if isinstance(elements, dict):
            elements = [elements]

        if not elements:
            return []

        # Process the first element separately
        first_element_text = elements[0]['text']
        if substring in first_element_text:
            if first_element_text.strip() == substring.strip():
                # If the entire text of the first element is the substring, skip this element
                if len(elements) > 1:
                    modified_elements = elements[1:]
                    modified_elements = OCRUtils.remove_substring_from_elements(elements[1:], substring)
            else:
                # Remove the substring from the first element's text
                modified_elements = elements
                modified_elements[0]['text'] = first_element_text.replace(substring, '').strip()
        else:
            # If the substring is not found in the first element, keep all elements as is
            modified_elements = elements

        return modified_elements