import re
from copy import deepcopy

def is_regex(pattern):
    try:
        re.compile(pattern)
        return True
    except re.error:
        return False

class ElementUtils:
    @staticmethod
    def remove_from_beginning(elements, substrings, case_sensitive=False):
        """
        Remove specified substrings from the beginning of the elements' text fields.

        :param elements: List of dictionaries containing text and bounding box data.
        :param substrings: List of substrings to remove from the beginning.
        :param case_sensitive: Boolean indicating if the removal should be case sensitive.
        :return: Modified list of elements with substrings removed.
        """
        elements = deepcopy(elements)
        not_return_list = False
        if isinstance(substrings, str):
            substrings = [substrings]
        if isinstance(elements, dict):
            elements = [elements]
            not_return_list = True

        for substring in substrings:
            if not case_sensitive:
                substring = substring.lower()
            substring_length = len(substring)

            concatenated_text = ''.join([element["text"] for element in elements])
            if not case_sensitive:
                concatenated_text_lower = concatenated_text.lower()
                start_idx = concatenated_text_lower.find(substring)
            else:
                start_idx = concatenated_text.find(substring)

            if start_idx != -1:
                end_idx = start_idx + substring_length
                elements = ElementUtils.filter_elements_by_string_indices(elements, end_idx, -1)
                break  # Break after finding and processing the first substring

        if not_return_list:
            if elements:
                elements = elements[0]
            else:
                elements = {}

        return elements

    @staticmethod
    def remove_from_end(elements, substrings, case_sensitive=False):
        """
        Remove specified substrings from the end of the elements' text fields.

        :param elements: List of dictionaries containing text and bounding box data.
        :param substrings: List of substrings to remove from the end.
        :param case_sensitive: Boolean indicating if the removal should be case sensitive.
        :return: Modified list of elements with substrings removed.
        """
        elements = deepcopy(elements)
        not_return_list = False
        if isinstance(substrings, str):
            substrings = [substrings]
        if isinstance(elements, dict):
            elements = [elements]
            not_return_list = True

        for substring in substrings:
            if not case_sensitive:
                substring = substring.lower()
            substring_length = len(substring)

            concatenated_text = ''.join([element["text"] for element in elements])
            if not case_sensitive:
                concatenated_text_lower = concatenated_text.lower()
                end_idx = concatenated_text_lower.rfind(substring)
            else:
                end_idx = concatenated_text.rfind(substring)

            if end_idx != -1:
                start_idx = end_idx + substring_length
                elements = ElementUtils.filter_elements_by_string_indices(elements, 0, end_idx)
                break  # Break after finding and processing the first substring

        if not_return_list:
            if elements:
                elements = elements[0]
            else:
                elements = {}
        return elements

    @staticmethod
    def contains(elements, substrings, case_sensitive=False):
        """
        Check if elements contain any of the substrings or regex patterns and return the appropriate words and position info.

        :param elements: List of dictionaries containing text and bounding box data.
        :param substrings: List of substrings or regex patterns to search for.
        :param case_sensitive: Boolean indicating if the search should be case sensitive.
        :return: Filtered list of elements containing the substrings or regex patterns.
        """
        if isinstance(substrings, str):
            substrings = [substrings]
        concatenated_text = ' '.join([element["text"] for element in elements])
        if not case_sensitive:
            concatenated_text = concatenated_text.lower()

        for substring in substrings:
            if is_regex(substring):
                # Handle regex pattern search
                pattern = re.compile(substring) if case_sensitive else re.compile(substring, re.IGNORECASE)
                match = pattern.search(concatenated_text)
                if match:
                    start_idx = match.start()
                    end_idx = match.end()
                    filtered_elements = ElementUtils.filter_elements_by_string_indices(elements, start_idx, end_idx)
                    return filtered_elements, True
            else:
                # Handle plain string search
                if not case_sensitive:
                    substring = substring.lower()
                start_idx = concatenated_text.find(substring)
                if start_idx != -1:
                    end_idx = start_idx + len(substring)
                    filtered_elements = ElementUtils.filter_elements_by_string_indices(elements, start_idx, end_idx)
                    return filtered_elements, True

        return [], False

    @staticmethod
    def remove_spaces(elements):
        """
        Remove all spaces from the elements' text fields.

        :param elements: List of dictionaries containing text and bounding box data.
        :return: Modified list of elements with spaces removed.
        """
        for element in elements:
            # Remove spaces from the text of the element
            element["text"] = element["text"].replace(" ", "")

        element = {"text": "".join(e["text"] for e in elements)}
        element['HPOS'] = min(e["HPOS"] for e in elements)
        element['VPOS'] = min(e["VPOS"] for e in elements)
        element['END_HPOS'] = max(e["END_HPOS"] for e in elements)
        element['END_VPOS'] = max(e["END_VPOS"] for e in elements)
        element['WORDS'] = [element]
        # Filter out any empty elements
        elements = [element]
        return elements

    @staticmethod
    def remove_special_chars_from_beginning(elements):
        """
        Remove special characters from the beginning of the concatenated elements' text fields.

        :param elements: List of dictionaries containing text and bounding box data.
        :return: Modified list of elements with special characters removed from the beginning.
        """
        concatenated_text = ''.join([element["text"] for element in elements])
        modified_text = re.sub(r'^[^a-zA-Z0-9]+', '', concatenated_text)  # Remove non-alphanumeric characters from the beginning
        start_idx = 0
        end_idx = len(modified_text)
        return ElementUtils.filter_elements_by_string_indices(elements, start_idx, end_idx)

    @staticmethod
    def remove_special_chars_from_end(elements):
        """
        Remove special characters from the end of the concatenated elements' text fields.

        :param elements: List of dictionaries containing text and bounding box data.
        :return: Modified list of elements with special characters removed from the end.
        """
        concatenated_text = ''.join([element["text"] for element in elements])
        modified_text = re.sub(r'[^a-zA-Z0-9]+$', '', concatenated_text)  # Remove non-alphanumeric characters from the end
        start_idx = len(concatenated_text) - len(modified_text)
        end_idx = len(concatenated_text)
        return ElementUtils.filter_elements_by_string_indices(elements, start_idx, end_idx)

    @classmethod
    def filter_elements_by_string_indices(cls, elements, string_start_idx, string_end_idx):
        """
        Filter elements and their words that fall within the specified start and end indices.

        :param elements: List of dictionaries containing text and bounding box data.
        :param string_start_idx: The start index of the string range.
        :param string_end_idx: The end index of the string range.
        :return: Filtered list of elements within the specified range.
        """
        filtered_elements = []
        current_idx = 0
        capturing = False
        found_end = False
        not_return_list = False

        if string_start_idx is None and string_end_idx is None:
            return elements

        if isinstance(elements, dict):
            elements = [elements]
            not_return_list = True
        if string_start_idx < 0:
            return elements[0] if not_return_list else elements
        if string_end_idx == -1:
            string_end_idx = len(" ".join([e['text'] for e in elements]))
        if string_start_idx > string_end_idx:
            return elements[0] if not_return_list else elements
        if string_start_idx <= 0 and string_end_idx <= 0:
            return {} if not_return_list else []

        for element in elements:
            words = element['WORDS']
            filtered_words = []
            for word in words:
                if word['text'] is None:
                    continue
                word_text = word.get('text', '')  # Provide a default value if 'text' is None
                if word_text is None:
                    word_text = ''
                word_length = len(word_text)

                # Check if we are within the range
                if (current_idx + word_length) >= string_start_idx and (current_idx + word_length < string_end_idx):
                    capturing = True
                    # Trim the beginning of the word if it's the first word in the range
                    if current_idx <= string_start_idx:
                        word_text = word_text[string_start_idx - current_idx: min(string_end_idx, len(word_text))]
                        word['text'] = word_text
                elif (current_idx + word_length >= string_end_idx) and not capturing:
                    capturing = True
                    # Trim the beginning of the word if it's the first word in the range
                    if current_idx <= string_start_idx:
                        word_text = word_text[string_start_idx - current_idx: min(string_end_idx, len(word_text))]
                        word['text'] = word_text

                if capturing:
                    if len(word['text']) != 0:
                        filtered_words.append(word)

                if current_idx + word_length >= string_end_idx:
                    if filtered_words:
                        # Trim the end of the word if it's the last word in the range
                        if current_idx < string_end_idx:
                            word_text = word_text[:string_end_idx - current_idx]
                            word['text'] = word_text
                        elif current_idx == string_end_idx:
                            word_text = ''
                            word = {}
                        if word:
                            filtered_words[-1] = word
                        else:
                            filtered_words = filtered_words[:-1]
                    capturing = False
                    found_end = True
                    break

                current_idx += word_length + 1  # Including space after the word

            if filtered_words:
                new_element = element.copy()
                new_element['WORDS'] = filtered_words
                new_element['text'] = ' '.join(str(w.get('text', '')) for w in filtered_words)
                new_element['HPOS'] = int(min(w['HPOS'] for w in filtered_words))
                new_element['VPOS'] = int(min(w['VPOS'] for w in filtered_words))
                new_element['END_HPOS'] = int(max(w['HPOS'] + w['WIDTH'] for w in filtered_words))
                new_element['END_VPOS'] = int(max(w['VPOS'] + w['HEIGHT'] for w in filtered_words))
                new_element['WIDTH'] = new_element['END_HPOS'] - new_element['HPOS']
                new_element['HEIGHT'] = new_element['END_VPOS'] - new_element['VPOS']
                filtered_elements.append(new_element)
            if found_end:
                break

        return filtered_elements[0] if not_return_list and len(filtered_elements) > 0 else filtered_elements

    @classmethod
    def find_element_indices_from_string_indices(cls, elements, remaining_string_start_index, remaining_string_end_index):
        """
        Locate the start and end indices of text elements based on the provided start and end indices.

        Args:
            output (object): An object containing text elements. It should have an attribute 'elements',
                             which is a list of dictionaries with each dictionary having a 'text' key.
            remaining_string_start_index (int): The start index of the remaining string within the text.
            remaining_string_end_index (int): The end index of the remaining string within the text.

        Returns:
            tuple: A tuple containing the start and end indices of the text elements where the
                   remaining string starts and ends, respectively.

        Example:
            output = {
                "elements": [
                    {"text": "This is the first element."},
                    {"text": "This is the second element."},
                    {"text": "This is the third element."}
                ]
            }
            start_index, end_index = TextLocator.find_text_indices(output, 5, 50)
            print(start_index, end_index)  # Output: (0, 2)
        """
        if remaining_string_start_index > remaining_string_end_index:
            return 0, len(elements)
        explored_len = 0
        start_idx = end_idx = None
        found_start = False

        for idx, ntle in enumerate(elements):
            explored_len += len(ntle['text'])

            if explored_len > remaining_string_start_index and not found_start:
                start_idx = idx
                found_start = True

            if explored_len > remaining_string_end_index and found_start:
                end_idx = idx
                break

        if start_idx is not None and end_idx is not None:
            if start_idx > 0 and end_idx == 0:
                end_idx = len(elements)
            elif start_idx == 0 and end_idx == 0:
                end_idx = 1
        elif start_idx is not None and end_idx is None:
            end_idx = len(elements)
        else:
            start_idx = end_idx = 0

        return start_idx, end_idx

    @staticmethod
    def starts_with(elements, patterns, case_sensitive=False):
        """
        Check if the elements' text starts with any of the provided patterns (strings or regex).

        :param elements: List of dictionaries containing text and bounding box data.
        :param patterns: List of strings or regex patterns to check.
        :param case_sensitive: Boolean indicating if the check should be case sensitive.
        :return: Tuple (boolean indicating if a match is found, the matched pattern or None)
        """
        concatenated_text = ''.join([element["text"] for element in elements])
        if not case_sensitive:
            concatenated_text = concatenated_text.lower()

        for pattern in patterns:
            if not case_sensitive and isinstance(pattern, str):
                pattern = pattern.lower()

            if isinstance(pattern, str):
                if concatenated_text.startswith(pattern):
                    return True, pattern
            elif isinstance(pattern, re.Pattern):
                match = pattern.match(concatenated_text)
                if match:
                    return True, pattern

        return False, None

    @staticmethod
    def ends_with(elements, patterns, case_sensitive=False):
        """
        Check if the elements' text ends with any of the provided patterns (strings or regex).

        :param elements: List of dictionaries containing text and bounding box data.
        :param patterns: List of strings or regex patterns to check.
        :param case_sensitive: Boolean indicating if the check should be case sensitive.
        :return: Tuple (boolean indicating if a match is found, the matched pattern or None)
        """
        concatenated_text = ''.join([element["text"] for element in elements])
        if not case_sensitive:
            concatenated_text = concatenated_text.lower()

        for pattern in patterns:
            if not case_sensitive and isinstance(pattern, str):
                pattern = pattern.lower()

            if isinstance(pattern, str):
                if concatenated_text.endswith(pattern):
                    return True, pattern
            elif isinstance(pattern, re.Pattern):
                match = pattern.search(concatenated_text)
                if match and match.end() == len(concatenated_text):
                    return True, pattern

        return False, None

    @staticmethod
    def remove_till_from_beginning(elements, patterns, case_sensitive=False):
        """
        Remove text from the beginning of the elements' text fields until the first occurrence of any pattern is found.

        :param elements: List of dictionaries containing text and bounding box data.
        :param patterns: List of strings or regex patterns to find.
        :param case_sensitive: Boolean indicating if the check should be case sensitive.
        :return: Modified list of elements with text removed up to the first occurrence of any pattern.
        """
        not_return_list = False
        if isinstance(patterns, str):
            patterns = [patterns]
        if isinstance(elements, dict):
            elements = [elements]
            not_return_list = True

        concatenated_text = ''.join([element["text"] for element in elements])
        if not case_sensitive:
            concatenated_text_lower = concatenated_text.lower()

        start_idx = -1
        pattern_found = None
        for pattern in patterns:
            if not case_sensitive and isinstance(pattern, str):
                pattern = pattern.lower()

            if isinstance(pattern, str):
                idx = concatenated_text_lower.find(pattern) if not case_sensitive else concatenated_text.find(pattern)
                if idx != -1 and (start_idx == -1 or idx < start_idx):
                    start_idx = idx
                    pattern_found = pattern
            elif isinstance(pattern, re.Pattern):
                match = pattern.search(concatenated_text_lower) if not case_sensitive else pattern.search(
                    concatenated_text)
                if match and (start_idx == -1 or match.start() < start_idx):
                    start_idx = match.start()
                    pattern_found = pattern

        if start_idx != -1:
            end_idx = start_idx + (
                len(pattern_found) if isinstance(pattern_found, str) else match.end() - match.start())
            elements = ElementUtils.filter_elements_by_string_indices(elements, end_idx, -1)

        if not_return_list:
            if elements:
                elements = elements[0]
            else:
                elements = {}

        return elements

    @staticmethod
    def remove_till_from_end(elements, patterns, case_sensitive=False):
        """
        Remove text from the end of the elements' text fields until the first occurrence of any pattern is found.

        :param elements: List of dictionaries containing text and bounding box data.
        :param patterns: List of strings or regex patterns to find.
        :param case_sensitive: Boolean indicating if the check should be case sensitive.
        :return: Modified list of elements with text removed up to the first occurrence of any pattern.
        """
        not_return_list = False
        if isinstance(patterns, str):
            patterns = [patterns]
        if isinstance(elements, dict):
            elements = [elements]
            not_return_list = True

        concatenated_text = ''.join([element["text"] for element in elements])
        if not case_sensitive:
            concatenated_text_lower = concatenated_text.lower()

        end_idx = -1
        pattern_found = None
        for pattern in patterns:
            if not case_sensitive and isinstance(pattern, str):
                pattern = pattern.lower()

            if isinstance(pattern, str):
                idx = concatenated_text_lower.rfind(pattern) if not case_sensitive else concatenated_text.rfind(pattern)
                if idx != -1 and (end_idx == -1 or idx > end_idx):
                    end_idx = idx
                    pattern_found = pattern
            elif isinstance(pattern, re.Pattern):
                match = None
                for m in pattern.finditer(concatenated_text_lower if not case_sensitive else concatenated_text):
                    if m and (end_idx == -1 or m.start() > end_idx):
                        match = m
                        end_idx = m.start()
                if match:
                    pattern_found = pattern

        if end_idx != -1:
            start_idx = end_idx + (
                len(pattern_found) if isinstance(pattern_found, str) else match.end() - match.start())
            elements = ElementUtils.filter_elements_by_string_indices(elements, 0, end_idx)

        if not_return_list:
            if elements:
                elements = elements[0]
            else:
                elements = {}

        return elements

    @staticmethod
    def remove_non_digit(elements):
        """
        Remove all non-digit characters from the elements' text fields.

        :param elements: List of dictionaries containing text and bounding box data.
        :return: Modified list of elements with non-digit characters removed.
        """
        not_return_list = False
        if isinstance(elements, dict):
            elements = [elements]
            not_return_list = True

        for element in elements:
            # Remove non-digit characters from the text of the element
            element["text"] = ''.join(filter(str.isdigit, element["text"]))

            # Remove non-digit characters from the text of each word in the WORDS list
            for word in element.get('WORDS', []):
                word["text"] = ''.join(filter(str.isdigit, word.get("text", "")))

        # Filter out any elements that have become empty
        elements = [e for e in elements if e["text"]]

        if not_return_list:
            if elements:
                elements = elements[0]
            else:
                elements = {}

        return elements

    @classmethod
    def get_bbox_and_line_number(cls, elements):
        """
        Calculate the bounding box and line number for the given elements.

        Args:
            elements (list): A list of dictionaries containing text and position info.

        Returns:
            tuple: A tuple containing the bounding box and the line number.
        """
        if not elements:
            return [0, 0, 0, 0], 0

        start_vpos = float('inf')
        start_hpos = float('inf')
        end_vpos = float('-inf')
        end_hpos = float('-inf')
        line_no = float('inf')

        for line in elements:
            start_vpos = min(start_vpos, line['VPOS'])
            start_hpos = min(start_hpos, line['HPOS'])
            end_vpos = max(end_vpos, line['END_VPOS'])
            end_hpos = max(end_hpos, line['END_HPOS'])
            line_no = min(line_no, line['LINE_NUMBER'])

        bbox = (start_hpos, start_vpos, end_hpos, end_vpos)
        line_number = line_no

        return bbox, line_number

# Example usage:
if __name__ == "__main__":
    structured_elements = [
        {'text': 'Policy No.: M-9302-00564056 2', 'HPOS': 1200, 'VPOS': 320,
         'WIDTH': 385, 'HEIGHT': 20, 'END_HPOS': 1585, 'END_VPOS': 340,
         'WORDS': [
             {'text': 'Policy', 'HPOS': 1200.6666666666667, 'VPOS': 320.0, 'WIDTH': 63.99999999999977, 'HEIGHT': 20.0},
             {'text': 'No.:', 'HPOS': 1290.0, 'VPOS': 320.0, 'WIDTH': 45.333333333333485, 'HEIGHT': 20.0},
             {'text': 'M-9302-00564056', 'HPOS': 1359.3333333333333, 'VPOS': 320.0, 'WIDTH': 210.0, 'HEIGHT': 20.0},
             {'text': '2', 'HPOS': 1585.3333333333333, 'VPOS': 320.0, 'WIDTH': 0.0, 'HEIGHT': 20.0}
         ], 'LINE_NUMBER': 5}
    ]

    remove_list_beginning = ["Policy No"]
    remove_list_end = ["00564056 2"]

    elements_after_beginning_removal = ElementUtils.remove_from_beginning(structured_elements, remove_list_beginning, case_sensitive=False)
    print("Elements after beginning removal:", elements_after_beginning_removal)
    print()

    elements_after_end_removal = ElementUtils.remove_from_end(structured_elements, remove_list_end, case_sensitive=False)
    print("Elements after end removal:", elements_after_end_removal)
