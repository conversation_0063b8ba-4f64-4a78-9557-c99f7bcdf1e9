from docvu_de_core.extraction_item import BaseFieldData 
from docvu_de_core.extraction_item import TableRow

class TableFormatter:
    def __init__(self):
        pass

    def table_formatter(self, item):
        value = item["value"]
        bbox = item["bbox"]
        field_key = item["field_key"] if hasattr(item, 'field_key') else None
        
        if value and isinstance(value[0], list):
            value = [item for sublist in value for item in sublist]
        
        if isinstance(value, str):
            value = [value]

        if value is None:
            value = []
        
        item["confidence_indicator"] = 0.98
        item["color_indicator"] = 1
        item["start_page"] = item["page_number"]
        item["end_page"] = item["page_number"]
        
        for idx, v in enumerate(value):
            coord_dict = {
                    "x": 0, 
                    "y": 0, 
                    "width": 0, 
                    "height": 0
                }
                
            if (
                bbox
                and isinstance(bbox, list)
                and isinstance(bbox[0], (list, tuple, dict))
                and idx < len(bbox)
                and bbox[idx]
            ):
                coord_dict = {
                    "x": bbox[idx][0], 
                    "y": bbox[idx][1],
                    "width": abs(bbox[idx][0] - bbox[idx][2]),
                    "height": abs(bbox[idx][1] - bbox[idx][3])
                }
                
            # if (len(line_numbers) > 0) and (len(line_numbers) > idx):
            #     ln = line_numbers[idx] if line_numbers[idx] else 0
            # else:
            #     ln = 0

            ln = 0
            id = item['field_id']
            name = item['field_name']
            key = field_key['text'] if field_key and field_key['text'] else ''
            pn = item["page_number"]
            found_on_page = pn
            columns = [
                BaseFieldData(
                    id=id,
                    field_name=name,
                    key=key,
                    value=v,
                    post_processing_value=v,
                    page_number=pn-1 if item.get('multi_page_value', False) else pn,
                    line_number=ln,
                    confidence_indicator=.95,
                    color_indicator=1,
                    field_name_coordinates={"x": 0, "y": 0, "width": 0, "height": 0},
                    field_value_coordinates=coord_dict
                )
            ]
            row = TableRow(row_number=item, page_number=found_on_page, line_number=ln, columns=columns)
            item.add_row(row)

        return item