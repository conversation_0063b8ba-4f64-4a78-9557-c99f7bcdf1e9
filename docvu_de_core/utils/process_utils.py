import numpy as np
from docvu_de_core.config import parser_config, valid_states
from docvu_de_core.utils.general_utils import *
from docvu_de_core.utils.distance_utils import *
from docvu_de_core.modules.NER import NER
from fuzzywuzzy import process


class ProcessUtils:
    def __init__(self):
        self.general_utils = GeneralUtils()
        self.dist_utils = DistanceUtils()
        self.ner_utils = NER()
        pass

    @staticmethod
    def remove_special_quote_start(text):
        # Regex to match any special quote at the start of the string
        text = re.sub(r'[“”"]', '', text)
        return text

    @staticmethod
    def pre_process_text(text):
        text = text.lower().replace(" ", "")
        text = "".join(c for c in text if (c.isalnum() or c in ["$", "\n"]))
        return text

    @staticmethod
    def remove_symbols(text):
        text = " ".join(c for c in text if c.isalnum())
        return text

    @staticmethod
    def get_remaining_text_using_fuzzy_match(present_str, key_str):
        key_str = [ks for ks in key_str if ks.strip()]
        present_str = present_str.strip()
        min_index = 1000
        for k in key_str:
            split_key_str = k.split(' ')
            for sp_str in split_key_str:
                index = present_str.find(sp_str)
                if index != -1:
                    index += len(sp_str)
                    min_index = min(min_index, index)
        if min_index == 1000:
            return -1
        return min_index

    def split_list_based_on_delimiter(self, elements, delimiter):
        # Create the final_values string from the list of dictionaries
        final_values = " ".join(v["text"] for v in elements)

        # Split final_values by the specified delimiter
        final_split = final_values.split(delimiter)

        # Dictionary to track the start index of each element in final_values
        current_position = 0
        element_positions = []
        for v in elements:
            start = current_position
            end = start + len(
                v["text"])  # assume single space as a separator, included in the length of the next element
            element_positions.append((start, end, v))
            current_position = end + 1  # +1 for the space after each element

        # Split the list according to final_split parts
        split_lists = [[] for _ in final_split]
        for i, part in enumerate(final_split):
            part_start_index = final_values.index(part)
            part_end_index = part_start_index + len(part)

            for start, end, element in element_positions:
                # Check if an element overlaps with the current part
                if (start < part_end_index and end >= part_start_index):
                    split_lists[i].append(element)

        return final_split, split_lists

    def get_bbox_and_line_number(self, elements):
        if not elements:
            return [0, 0, 0, 0], 0
        element_str = ''
        start_vpos = float('inf')
        start_hpos = float('inf')
        end_vpos = float('-inf')
        end_hpos = float('-inf')
        line_no = float('inf')
        for line in elements:
            if isinstance(line, dict) and all(
                    key in line for key in ['text', 'VPOS', 'HPOS', 'END_VPOS', 'END_HPOS', 'LINE_NUMBER']):
                element_str += ' ' + line['text']
                start_vpos = min(start_vpos, line['VPOS'])
                start_hpos = min(start_hpos, line['HPOS'])
                end_vpos = max(end_vpos, line['END_VPOS'])
                end_hpos = max(end_hpos, line['END_HPOS']) + 15
                line_no = min(line_no, line['LINE_NUMBER'])
            else:
                print("Skipping invalid line:", line)
        bbox = (start_hpos, start_vpos, end_hpos, end_vpos)
        line_number = line_no

        return bbox, line_number

    @staticmethod
    def get_match(identifier, text, get_fuzzy_match=False):
        if get_fuzzy_match:
            match_score = process.extractOne(text, identifier)[1]
            if match_score > parser_config["REF_FUZZY_MATCH_SCORE"]:
                return True
        else:
            for s in identifier:
                if s == "":
                    continue
                match_found = [s.lower() in text.lower()]
                if any(match_found):
                    return True
        return False

    @staticmethod
    def is_identifier_at_end(identifier, text, get_fuzzy_match=False):
        text_end = text.lower().strip()
        for s in identifier:
            if s == "":
                continue
            s_lower = s.lower()
            if get_fuzzy_match:
                match_score = process.extractOne(text_end, [s_lower])[1]
                if match_score > parser_config["REF_FUZZY_MATCH_SCORE"]:
                    return True
            else:
                if text_end.endswith(s_lower):
                    return True
        return False

    @staticmethod
    ## check whether identifier is individual
    def is_identifier_at_start(identifier, text):
        text_end = text.lower().strip()
        text_end = ProcessUtils.remove_special_quote_start(text_end)
        for s in identifier:
            if s == "":
                continue
            s_lower = s.lower()
            if text_end.startswith(s_lower):
                    return True
        return False

    @staticmethod
    ## Check whether end/start/key identifier is hpos specific
    def individual_hpos_aligned_identifier(text_string):
        text_hpos, text_vpos = int(text_string["HPOS"]), int(text_string["VPOS"])
        if text_hpos > 220:
            return True
        return False

    @staticmethod
    def similar(key, actual):
        f_ratio = 0
        for k in key:
            k = k.strip()
            split_a = k.split(' ')
            ratio = 0
            for sa in split_a:
                ratio += 1 if sa.lower() in actual.lower() else 0
            ratio = ratio / (len(split_a) + 0.004)
            if ratio > 0:
                f_ratio = ratio
        return round(f_ratio, 3)

    def pre_process_field_value_for_address_parsing(self, elements, extraction_item):
        field_value = self.get_string_from_elements(elements)
        fvalue_split = field_value.split('\n')
        if fvalue_split[-1].strip() == "1." or fvalue_split[-1].strip() == "1":
            field_value = " ".join(v for v in fvalue_split[:-1])
        fvalue_split = field_value.split('\n')
        if len(fvalue_split) > 2:
            field_value = " ,".join(v for v in fvalue_split if len(v) > 0)
        else:
            field_value = " ".join(v for v in fvalue_split if len(v) > 0)

        add_lines = field_value.split('\n')
        final_value = ','.join(add_lines)
        start_idx = 0
        end_idx = len(final_value)
        if "additional_info" in extraction_item.keys():
            if "search_after" in extraction_item["additional_info"].keys():
                search_after = extraction_item["additional_info"]["search_after"]
                field_value, start_idx, end_idx = self.get_remaining_text_from_key(field_value, search_after)
        add_lines = field_value.split('\n')
        final_value = ','.join(add_lines)
        return final_value, start_idx, end_idx

    def get_remaining_text_from_key(self, found_item, key):
        match_index = [found_item.lower().find(s.lower()) for s in key]
        rem = []
        for i, val in enumerate(match_index):
            if val != -1:
                key_len = len(key[i])
                rem.append([val, val + key_len])
        if rem:
            remaining_start_idx = rem[0][1]
            remaining_end_idx = len(found_item)
            remaining_text = found_item[remaining_start_idx:]
            return remaining_text, remaining_start_idx, remaining_end_idx
        else:
            return found_item, 0, len(found_item)

    @staticmethod
    def filter_valid_states(data):
        # Dictionary to keep track of the first occurrence and any valid state
        unique_entries = {}
        # List to keep the final result in order
        result = []

        for item in data:
            county, state = item
            if county not in unique_entries:
                # Add first occurrence to the dictionary and result list
                unique_entries[county] = item
                result.append(item)
            else:
                # If already seen, check for valid state
                existing_state = unique_entries[county][1]
                if existing_state not in valid_states and state in valid_states:
                    # Replace the item in the result list if the current state is valid
                    index = result.index(unique_entries[county])
                    result[index] = item
                    unique_entries[county] = item

        return result

    def get_remaining_text_from_start_identifier(self, found_item, field_info):
        total_string = found_item  # ["text"]

        main_cleaned = self.pre_process_text(total_string)
        sub_cleaned = [self.pre_process_text(k) for k in field_info["start_identifier"]]
        if not sub_cleaned:
            return total_string
        index = [main_cleaned.find(s) for s in sub_cleaned]

        count = index.count(-1)
        # Find the index of the substring in the main string after cleaning
        # if count != 0 and count <= len(index):
        if -1 not in index or index[0] == 0 or count > 0:
            f_index = [x[0] for x in enumerate(index) if x[1] != -1]
            if len(f_index) > 0:
                f_index = f_index[0]
            else:
                f_index = 0
            if index[f_index] != -1:
                remaining_chars = len(main_cleaned) - (index[f_index] + len(sub_cleaned[f_index]))
            else:
                remaining_chars = len(main_cleaned) - len(sub_cleaned[f_index])
            # In the case if both the sting matches exactly
            if remaining_chars == 0:
                return None
            # Travers from back of the actual string ignoring the symbols and spaces
            total_string_len = len(total_string)
            i = 0
            current_char = total_string_len - 1
            while i < remaining_chars:
                if total_string[current_char].isalnum():
                    i += 1
                current_char -= 1
            remaining_text = total_string[current_char:]
            return remaining_text
        return found_item

    def get_string_from_elements(self, elements):
        text = ''
        for e in elements:
            text += " " + e['text']
        return text.strip()

    @staticmethod
    def correct_mid_of_word_split(total_string, current_split):
        c = total_string.split(' ')
        d = [len(s) + 1 for s in c[:-1]]
        d.append(len(c[-1]))
        f = ''
        e = 0
        for index, d1 in enumerate(d):
            e += d1
            if e - 1 == current_split or e == current_split:
                f = total_string[current_split:]
                break
            elif e > current_split:
                if ',' in c[index] or '.' in c[index] or ':' in c[index]:
                    len_v1 = 0
                    if '\n' in total_string[e - d1:]:
                        line_split = total_string[e - d1:].split('\n')
                        l2_split = line_split[0].split(' ')
                        len_v1 = len(l2_split[0])
                    f = total_string[e - d1 + len_v1:]
                else:
                    f = total_string[e:]
                # f = total_string[e:]
                break
        return f

    @staticmethod
    def check_end_identifiers(text_string, end_identifiers, key_end_idx=0):
        """
        Check if any of the end identifiers are present in the text from a given index and return their start and end indices.

        Parameters:
        text_string (dict): A dictionary containing the text to be checked.
        end_identifiers (list): A list of end identifiers to be checked against the text.
        key_end_idx (int): The index from which to start the search in the text.

        Returns:
        bool: True if any end identifier is found, False otherwise.
        list: A list of tuples containing the start and end indices of each found identifier.
        """
        if not isinstance(text_string, dict):
            return False, []
        if 'text' not in text_string:
            return False, []

        text_lower = text_string["text"].lower()
        search_text = text_lower[key_end_idx:]
        found_indices = []

        for identifier in end_identifiers:
            if end_identifiers == '':
                continue
            identifier_lower = identifier.lower()
            start_idx = search_text.find(identifier_lower)

            if start_idx != -1:
                start_idx += key_end_idx  # Adjust start index relative to the original text
                end_idx = start_idx + len(identifier_lower)
                found_indices.append((start_idx, end_idx))

        return bool(found_indices), found_indices[0] if found_indices else found_indices

    def get_remaining_text_loan_info(self, found_item, field_info, use_start_identifier=False, use_end_identifier=True,
                                     use_min=False):
        total_string = found_item  # ["text"]
        main_cleaned = total_string.lower()
        sub_cleaned = field_info["key"][0].lower()
        index = main_cleaned.index(sub_cleaned)

        remaining_text = total_string[index:]

        # Truncate the value after end_identifier
        if field_info["end_identifier"] and use_end_identifier:
            f_index = self.find_index_using_identifiers(field_info["end_identifier"], remaining_text)
            if f_index == -1:
                remaining_text = remaining_text
                if field_info["type"] == "date":
                    if remaining_text[f_index:f_index + 2].lower() in ["am", "pm"]:
                        remaining_text = remaining_text[:f_index + 2]
            else:
                # remaining_text = remaining_text[:f_index]
                if field_info["type"] == "date":
                    if remaining_text[f_index:f_index + 2].lower() in ["am", "pm"]:
                        remaining_text = remaining_text[:f_index + 2]
                    else:
                        date_val = self.general_utils.get_dates_in_text(remaining_text)
                        if date_val is not None:
                            remaining_text = date_val
                elif field_info["type"] == "page_val":
                    remaining_text = remaining_text[:f_index]
                    remaining_text = remaining_text.strip()
                    split_text = remaining_text.split(' ')
                    for f in split_text:
                        if f == "es":
                            continue
                        else:
                            return f
                else:
                    remaining_text = remaining_text[:f_index]
                    remaining_text = ''.join([c for c in remaining_text if c not in parser_config["REMOVE_CHARS"]])

        if use_start_identifier:
            f_index = self.find_index_using_identifiers(field_info["start_identifier"], remaining_text, use_min=False)
            if f_index == -1:
                remaining_text = remaining_text
            else:
                remaining_text = remaining_text[f_index::]
                remaining_text = ''.join([c for c in remaining_text if c not in parser_config["REMOVE_CHARS"]])
        remaining_text = remaining_text.strip()
        if "use_first_word" in field_info.keys():
            if field_info["use_first_word"]:
                remaining_text = remaining_text.split(' ')[0]
        if remaining_text != "":
            return remaining_text
        else:
            return None

    ## TODO : return everything after start identifier
    def get_remaining_text(self, found_item, field_info,
                           use_start_identifier=False,
                           use_end_identifier=True,
                           use_key=True):
        total_string = found_item  # ["text"]
        main_cleaned = self.pre_process_text(total_string)
        sub_cleaned = [self.pre_process_text(k) for k in field_info["key"]] if use_key \
                        else [self.pre_process_text(k) for k in field_info["start_identifier"] \
                              if self.pre_process_text(k)]
        index = [main_cleaned.find(s) for s in sub_cleaned]
        remaining_string_start_index = 0
        remaining_string_end_index = len(total_string)

        count = index.count(-1)
        # Find the index of the substring in the main string after cleaning
        # if count != 0 and count <= len(index):
        if (index and (-1 not in index or index[0] == 0 or count > 0)) or not (sub_cleaned and use_key):
            f_index = [x[0] for x in enumerate(index) if x[1] != -1]
            if len(f_index) > 0:
                matched_sub_cleaned = [len(sub_cleaned[f]) for f in f_index]
                max_len_str_index = matched_sub_cleaned.index(max(matched_sub_cleaned))
                f_index = f_index[max_len_str_index]
            else:
                f_index = 0
            if index and index[f_index] != -1:
                remaining_chars = len(main_cleaned) - (index[f_index] + len(sub_cleaned[f_index]))
            else:
                remaining_chars = len(main_cleaned)  # - len(sub_cleaned[f_index])
            # In the case if both the sting matches exactly
            if remaining_chars == 0:
                return None, None, None
            # Travers from back of the actual string ignoring the symbols and spaces
            total_string = total_string.strip()
            total_string_len = len(total_string)
            i = 0
            current_char = total_string_len - 1
            while i < remaining_chars:
                if total_string[current_char].isalnum() or total_string[current_char] in ["$", "\n"]:
                    i += 1
                current_char -= 1
            if current_char != -1:
                remaining_text = self.correct_mid_of_word_split(total_string, current_char)
            else:
                remaining_text = total_string
            remaining_string_start_index = current_char
            # remaining_text = total_string.strip()[index + len(field_info["key"]):]
        else:
            if "use_match" in field_info.keys() and field_info["use_match"].lower() == "fuzzy":
                raw_key_found_similarity_ratio = self.similar(field_info["key"], total_string)
                index = self.get_remaining_text_using_fuzzy_match(total_string, field_info["key"] if use_key \
                                                                                    else field_info["start_identifier"])
                if index != -1:
                    remaining_text = total_string[index::]  # " ".join(v for v in total_string.split(':')[1:])
                    remaining_string_end_index = index
                else:
                    temp = [st for st in field_info["start_identifier"] if st.strip()]
                    if not use_key and not [st for st in field_info["start_identifier"] if st.strip()]:
                        remaining_text = total_string
                        remaining_string_start_index = 0
                    else:
                        remaining_text = ""
                        remaining_string_start_index = remaining_string_end_index
            else:
                remaining_text = total_string
                remaining_string_start_index = 0

        # Truncate the value after end_identifier
        if field_info["end_identifier"] and use_end_identifier:
            f_index = self.find_index_using_identifiers(field_info["end_identifier"], remaining_text)
            if f_index == -1:
                remaining_text = remaining_text
                if field_info["type"] == "date":
                    if remaining_text[f_index:f_index + 2].lower() in ["am", "pm"]:
                        remaining_text = remaining_text[:f_index + 2]
                        remaining_string_end_index = remaining_string_start_index + len(remaining_text)
            else:
                if field_info["type"] == "page_val":
                    remaining_text = remaining_text[:f_index]
                    remaining_text = remaining_text.strip()
                    split_text = remaining_text.split(' ')
                    remaining_string_end_index = remaining_string_start_index + len(remaining_text)
                    for f in split_text:
                        if f == "es":
                            continue
                        else:
                            return f, remaining_string_start_index, remaining_string_end_index
                else:
                    remaining_text = remaining_text[:f_index]
                    remaining_string_end_index = remaining_string_start_index + len(remaining_text)
                    remaining_text = ''.join([c for c in remaining_text if c not in parser_config["REMOVE_CHARS"]])

        if use_start_identifier and not use_key:
            f_index = self.find_index_using_identifiers(field_info["start_identifier"], remaining_text, use_min=False)
            if f_index == -1:
                remaining_text = remaining_text
            else:
                remaining_text = remaining_text[f_index::]
                remaining_string_start_index += f_index
                remaining_text = ''.join([c for c in remaining_text if c not in parser_config["REMOVE_CHARS"]])
        remaining_text = remaining_text.strip()
        if "use_first_word" in field_info.keys():
            if field_info["use_first_word"]:
                remaining_text = remaining_text.split(' ')[0]
                remaining_string_end_index = remaining_string_start_index + len(remaining_text)
        if remaining_text != "":
            return remaining_text, remaining_string_start_index, remaining_string_end_index
        else:
            return None, None, None

    def extract_lines_from_texts(self, texts):
        # Sort texts by VPOS and then by HPOS
        if texts is None:
            return ""
        texts_sorted = sorted(texts, key=lambda x: (x['VPOS'], x['HPOS']))

        lines = []
        current_line = []
        current_vpos = texts_sorted[0]['VPOS']

        for text in texts_sorted:
            if self.dist_utils.on_same_horizontal_line(text, {'VPOS': current_vpos}) or not current_line:
                current_line.append(text)
            else:
                # Sort the current line by HPOS before adding to lines
                current_line.sort(key=lambda x: x['HPOS'])
                line_text = " ".join(str['text'].strip() for item in current_line for txt in item["TEXT_LINE"] for str \
                                     in txt["STRING"])
                lines.append(line_text)
                current_line = [text]
            current_vpos = text['VPOS']

        # Don't forget to add the last line
        if current_line:
            current_line.sort(key=lambda x: x['HPOS'])
            line_text = " ".join(
                str['text'].strip() for item in current_line for txt in item["TEXT_LINE"] for str in txt["STRING"])

            lines.append(line_text)

        return lines

    def is_valid_value(self, value):
        if value == [] or value == {} or value is None:
            return False
        else:
            return False

    @staticmethod
    def find_index_using_identifiers(identifier, text, use_min=True):
        index = []
        for s in identifier:
            if s != '':
                index.append(text.lower().find(s.lower()))
        count = index.count(-1)
        if count == len(index):
            return -1
        elif count > 0:
            f_index = [x[0] for x in enumerate(index) if x[1] != -1]
            match = [index[i] for i in f_index]
            return min(match) if use_min else max(match)
        elif count == 0:
            f_index = min(index)
            return f_index
        else:
            return -1

    def is_required_key(self, page_block_list, field_info):
        page_block_text = " ".join(page_block_list)
        has_start_identifier = self.get_match(field_info["start_identifier"], page_block_text)
        if field_info["start_identifier"] and has_start_identifier:
            return True
        if "" in field_info["start_identifier"]:
            return True
        if "" in field_info["start_identifier"] and "" in field_info["end_identifier"]:
            return True
        return False

    @staticmethod
    def check_if_key_found_is_individual(present_str, key_str, return_found_key=False):
        pstr_idx = 0
        for pstr in present_str:
            pstr = ProcessUtils.remove_special_quote_start(pstr)
            for k in key_str:
                index = pstr.strip().lower().find(k.strip().lower())
                if index == 0 and (len(pstr) < 50 if k.strip().lower() == "schedule b" else True):  # and abs(len(k) - len(pstr.strip())) <= 5:
                    if return_found_key:
                        return True, index + len(k), k
                    else:
                        return True, index + len(k)
        if return_found_key:
            return False, 0, None
        else:
            return False, 0

    @staticmethod
    def check_if_key_found_is_present(present_str, key_str):
        pstr_idx = 0
        for pstr in present_str:
            for k in key_str:
                index = pstr.strip().lower().find(k.strip().lower())
                if index != -1:  # and abs(len(k) - len(pstr.strip())) <= 5:
                    return True, index + len(k)
        return False, 0

    def sort_text_line_string_by_vpos(self, blocks):
        all_vpos = []
        for index2, lines in enumerate(blocks["TEXT_LINE"]):
            all_vpos.append(int(lines["VPOS"]))
        out = self.get_minimum_dist_vpos(all_vpos, min_dist=5)
        if len(out) > 0:
            if len(np.unique(out[0])) > 1:
                newlist = (sorted(blocks["TEXT_LINE"], key=lambda d: d['HPOS']))
                return newlist
        return blocks

    def sort_block_string(self, page_json_data):
        for i, item in enumerate(page_json_data):
            for index, blocks in enumerate(item["TEXT_BLOCK"]):
                all_vpos = []
                for index2, lines in enumerate(blocks["TEXT_LINE"]):
                    all_vpos.append(int(lines["VPOS"]))
                out = self.get_minimum_dist_vpos(all_vpos, min_dist=5)
                if len(out) > 0:
                    if len(np.unique(out[0])) > 1:
                        newlist = (sorted(blocks["TEXT_LINE"], key=lambda d: d['HPOS']))
                        page_json_data[i]["TEXT_BLOCK"][index]["TEXT_LINE"] = newlist
        return page_json_data

    @staticmethod
    def sort_vpos_block_string(blocks):
        sorted_block = []
        for index, block in enumerate(blocks):
            for index2, lines in enumerate(block["TEXT_LINE"]):
                for index3, strings in enumerate(lines["STRING"]):
                    sorted_block.append(strings)
        newlist = (sorted(sorted_block, key=lambda d: d['VPOS']))
        sorted_list = []
        count = 1
        for index in range(len(newlist)):
            if index > 0:
                prev_hpos = newlist[index - 1]["HPOS"]
                curr_hpos = newlist[index]["HPOS"]
                if abs(curr_hpos - prev_hpos) <= 5:
                    sorted_list[index - count]["text"] += newlist[index]["text"]
                    sorted_list[index - count]["WIDTH"] += newlist[index]["WIDTH"]
                    count += 1
                else:
                    sorted_list.append(newlist[index])
            else:
                sorted_list.append(newlist[index])
        final_sorted_list = []
        count = 1
        for index in range(len(sorted_list)):
            if index > 0:
                prev_vpos = sorted_list[index - 1]["VPOS"]
                curr_vpos = sorted_list[index]["VPOS"]
                if abs(curr_vpos - prev_vpos) <= 5:
                    final_sorted_list[index - count]["text"] = (
                            final_sorted_list[index - count]["text"] + " -- " + sorted_list[index]["text"])
                    final_sorted_list[index - count]["WIDTH"] += (4 + sorted_list[index]["WIDTH"])  # since added " -- "
                    count += 1
                else:
                    final_sorted_list.append(sorted_list[index])
            else:
                final_sorted_list.append(sorted_list[index])
        return final_sorted_list

    @staticmethod
    def get_text_block_vpos(page_json_data):
        all_vpos = []
        all_indexes = []
        for i, item in enumerate(page_json_data):
            for index, blocks in enumerate(item["TEXT_BLOCK"]):
                if len(blocks) > 0:
                    all_vpos.append(int(blocks["VPOS"]))
                    all_indexes.append(f'{i}-{index}')
        return all_vpos, all_indexes

    @staticmethod
    def get_minimum_dist_vpos(vpos_list, min_dist, dont_match_equal=False):
        sorted_list = sorted(vpos_list)
        out = []
        inner = None
        for x, y in zip(sorted_list, sorted_list[1:]):
            if y - x <= min_dist:
                if dont_match_equal:
                    if x == y:
                        continue
                if inner:
                    inner.append(y)
                else:
                    inner = [x, y]
                    out.append(inner)
            else:
                inner = None
        return out

    @staticmethod
    def if_nearby_vpos(str1, str2, limit=4):
        if abs((int(str1["VPOS"]) + int(str1["HEIGHT"])) - int(str2["VPOS"])) <= limit:
            return True
        return False

    def rearrange_based_on_vpos_hpos(self, block_vpos):
        sorted_block_vpos = block_vpos.copy()
        all_vpos = [int(v["VPOS"]) for v in sorted_block_vpos]
        out = self.get_minimum_dist_vpos(all_vpos, min_dist=2, dont_match_equal=True)
        out_dict = {}
        for oo in out:
            ind = [all_vpos.index(o) for o in oo]
            out_dict[oo[0]] = ind
        for o, ind in out_dict.items():
            val = sorted_block_vpos[ind[0]:ind[-1] + 1]
            for index in range(len(val)):
                val[index]["VPOS"] = o
            newlist = (sorted(val, key=lambda d: d['HPOS']))
            for i in range(ind[0], ind[-1] + 1):
                sorted_block_vpos[i] = newlist[i - ind[0]]
        return sorted_block_vpos

    def get_county_value(self, text_line, field_info):
        split_text_by_space = text_line.split(' ')
        split_text_by_comma = text_line.split(',')
        key_to_find = field_info["key"]
        output_text = []
        additional_text = []
        two_word_states = [
            "New York",
            "New Jersey",
            "New Mexico",
            "North Carolina",
            "North Dakota",
            "Rhode Island",
            "South Carolina",
            "South Dakota",
            "West Virginia",
            "New Hampshire"
        ]
        two_word_states_first_word = [
            "new",
            "north",
            "rhode",
            "south",
            "west",
        ]
        for index, sts in enumerate(split_text_by_space):
            matched = self.get_match(key_to_find, sts)
            if matched:
                if sts.strip()[-1] in [",", "."]:
                    for idx, stc in enumerate(split_text_by_comma):
                        matched = self.is_identifier_at_end(key_to_find, stc)  # self.get_match(key_to_find, stc)
                        if matched:
                            org_entity, gpe_entity = self.ner_utils.get_entity(stc)
                            state = ''
                            if idx < len(split_text_by_comma) - 1:
                                possible_stale_val = split_text_by_comma[idx + 1].strip()
                                state = possible_stale_val.split(" ")[0]
                                if state.strip().lower() in two_word_states_first_word:
                                    state += ' ' + possible_stale_val.strip().split(" ")[1] if\
                                                    len(possible_stale_val.strip().split(" ")) > 1 else ''
                                if state.lower().strip() in ['state']:
                                    state = possible_stale_val.strip().split(" ")[2] \
                                        if len(possible_stale_val.strip().split(" ")) >= 3 else ''
                                    if state.lower().strip() in two_word_states_first_word:
                                        state += ' ' + possible_stale_val.strip().split(" ")[3] \
                                            if len(possible_stale_val.strip().split(" ")) >= 4 else ''


                            if org_entity:
                                output_text += [(oe, state) for oe in org_entity]
                                additional_text += [state] * len(org_entity)
                            else:
                                output_text += [(ge, state) for ge in gpe_entity]
                                additional_text += [state] * len(gpe_entity)
                else:
                    value = " ".join(split_text_by_space[index:])
                    value_split = value.split(',')
                    new_val = value_split[0]
                    org_entity, gpe_entity = self.ner_utils.get_entity(new_val)

                    state = ''
                    if len(value_split) > 1:
                        possible_stale_val = value_split[1]
                        if len(possible_stale_val) > 1:
                            state = possible_stale_val.strip().split(" ")[0]
                            if state.lower().strip() in two_word_states_first_word:
                                state += ' ' + possible_stale_val.strip().split(" ")[1] if\
                                                    len(possible_stale_val.strip().split(" ")) > 1 else ''
                            if state.lower().strip() in ['state']:
                                state = possible_stale_val.strip().split(" ")[2] \
                                                    if len(possible_stale_val.strip().split(" ")) >= 3 else ''
                                if state.lower().strip() in two_word_states_first_word:
                                    state += ' ' + possible_stale_val.strip().split(" ")[3] \
                                                    if len(possible_stale_val.strip().split(" ")) >= 4 else ''

                    if org_entity:
                        output_text += [(oe, state) for oe in org_entity]
                        additional_text += [state] * len(org_entity)
                    elif gpe_entity:
                        output_text += [(ge, state) for ge in gpe_entity]
                        additional_text += [state] * len(gpe_entity)
                    else:
                        output_text.append((new_val, state))
                        additional_text.append(state)
        if output_text:
            unique = self.filter_valid_states(output_text)
            if len(unique) > 1:
                un_values = []
                non_req_values = []
                for idx, un in enumerate(unique):
                    org_entity, gpe_entity = self.ner_utils.get_entity(str(un[0]))
                    for gpx, _ in enumerate(gpe_entity):

                        # parse for state. ignore states with less than 1 char long
                        # as they can be wrongly identified states
                        i = idx
                        state = unique[i][1]
                        while len(state) < 3:
                            i += 1
                            if i >= len(output_text[0]):
                                break
                            state = output_text[i][1]
                        if len(state) < 3:
                            state = ''


                        gpe_entity[gpx] += ', ' + state if state else ''
                    un_values += gpe_entity
                    non_req_values += org_entity
                if un_values:
                    return un_values[0]
                elif non_req_values:
                    return non_req_values[0]
                else:
                    output_county = output_text[0][0]

                    # parse for state. ignore states with less than 1 char long
                    # as they can be wrongly identified states
                    i = 1
                    state = output_text[0][i]
                    while len(state) < 3:
                        i += 1
                        if i >= len(output_text[0]):
                            break
                        state = output_text[0][i]
                    if len(state) < 3:
                        state = ''

                    output_county += ', ' + state if state else ''
                    return output_county
            else:
                output_county = output_text[0][0]

                # parse for state. ignore states with less than 1 char long
                # as they can be wrongly identified states
                i = 1
                state = output_text[0][i]
                while len(state) < 3:
                    i += 1
                    if i >= len(output_text[0]):
                        break
                    state = output_text[0][i]
                if len(state) < 3:
                    state = ''

                output_county += ', ' + output_text[0][1] if output_text[0][1] else ''
                return output_county
                # return output_text[0]
        return text_line

def is_valid_value(value):
    if value == [] or value == {} or value is None:
        return False
    else:
        return False

