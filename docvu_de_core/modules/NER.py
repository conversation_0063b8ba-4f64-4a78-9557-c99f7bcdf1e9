import spacy
import os


class NER:
    def __init__(self):
        try:
            self.name_entity_rec = spacy.load("en_core_web_sm")
        except IOError:
            print('os path::::',os.getcwd())
            model_path = "docvu_de_core/extra_files/en_core_web_sm/en_core_web_sm-3.7.1/"
            self.name_entity_rec = spacy.load(model_path)

    def get_all_entities(self, input_text):
        entities = []
        labels = []
        ner_text = self.name_entity_rec(str(input_text))
        for word in ner_text.ents:
            # print(word.text, word.label_)
            entities.append(word.text)
            labels.append(word.label_)
        return entities, labels

    def get_entity(self, input_text):
        gpe_entity = []
        org_entity = []

        ner_text = self.name_entity_rec(str(input_text))
        for word in ner_text.ents:
            print(word.text, word.label_)
            if word.text.isdigit():
                continue
            if word.label_ == "GPE":
                gpe_entity.append(word.text)
            elif word.label_ == "ORGANIZATION":
                org_entity.append(word.text)
            # else:
            #     org_entity.append(word.text)
        return org_entity, gpe_entity
