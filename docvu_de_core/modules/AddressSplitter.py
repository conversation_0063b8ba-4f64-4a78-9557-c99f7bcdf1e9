#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""

import re
from docvu_de_core.utils.element_utils import ElementUtils
from docvu_de_core.address_parser import USAddressParser


class AddressSplitter:
    def __init__(self, min_zip_code_len=5, debug=False):
        """
        Initializes the AddressSplitter with the minimum length for zip codes.
        :param min_zip_code_len: Minimum length of zip codes for regex matching.
        :param debug: If True, prints debug information.
        """
        self.min_zip_code_len = min_zip_code_len
        self.debug = debug
        self.usaddress_parser = USAddressParser()

    def get_regex(self):
        """
        Generates regex patterns for matching zip codes.
        :return: Tuple containing two regex patterns for matching zip codes.
        """
        # Adding word boundaries (\b) to ensure zip codes are not part of other words
        min_zip_code_len_less = str(self.min_zip_code_len - 1)
        re1 = r'\b\d{' + str(self.min_zip_code_len) + r',}' + r'\-?\d{' + min_zip_code_len_less + r',}\b'
        regex_in1 = r'{}'.format(re1)

        re2 = r'\b\d{' + str(self.min_zip_code_len) + r',}\b'
        regex_in2 = r'{}'.format(re2)
        return regex_in1, regex_in2

    def check_if_alphanumeric(self, field_str):
        """
        Checks if the string contains only alphanumeric characters (ignoring special characters).
        :param field_str: The string to check.
        :return: Boolean indicating if the string is alphanumeric.
        """
        s1 = re.sub('[^0-9a-zA-Z]+', '', field_str)
        return not s1.isdigit()

    def extract_address_components(self, field_value_or_elements):
        """
        Extracts the addresses and their components from the provided elements or string.
        :param field_value_or_elements: The string or list of OCR-extracted elements.
        :return: Processed addresses, zip codes, and indices as a tuple (addresses, indices, zip codes).
        """
        # If field_value_or_elements is a list of elements, convert it to a concatenated string
        if isinstance(field_value_or_elements, list):
            elements = field_value_or_elements
            concatenated_text = ' '.join([element["text"] for element in elements])
        else:
            concatenated_text = field_value_or_elements
            elements = []  # No elements to process if input is a string

        # Retrieve regex patterns
        regex_in1, regex_in2 = self.get_regex()

        temp_zip_code = ' 00000'
        zip_add = re.findall(regex_in1, concatenated_text)
        addresses = None

        # Check for zip codes with the first regex
        if not zip_add:
            zip_add = re.findall(regex_in2, concatenated_text)
            if zip_add:
                addresses, indices, zip_add = self.split_address_field_into_multiple_addresses(
                    elements or concatenated_text, regex_in2, zip_add)
            else:
                # Fallback to append temporary zip code and retry
                concatenated_text += temp_zip_code
                zip_add = re.findall(regex_in2, concatenated_text)
                if zip_add:
                    addresses, indices, zip_add = self.split_address_field_into_multiple_addresses(
                        elements or concatenated_text, regex_in2, zip_add)
        else:
            addresses, indices, zip_add = self.split_address_field_into_multiple_addresses(
                elements or concatenated_text, regex_in1, zip_add)

        return addresses

    def split_address_field_into_multiple_addresses(self, field_value_or_elements, regex_in, zip_codes):
        """
        Splits a string or list of elements into multiple addresses based on regex and zip codes.
        :param field_value_or_elements: The string or list of OCR-extracted elements.
        :param regex_in: The regex pattern to match the addresses.
        :param zip_codes: List of zip codes found in the addresses.
        :return: Tuple containing a list of addresses as elements, their indices, and corresponding zip codes.
        """
        # If field_value_or_elements is a list of elements, convert it to a concatenated string
        if isinstance(field_value_or_elements, list):
            elements = field_value_or_elements
            concatenated_text = ' '.join([element["text"] for element in elements])
        else:
            concatenated_text = field_value_or_elements
            elements = []  # No elements to process if input is a string

        address = []
        indices = [(m.start(0), m.end(0)) for m in re.finditer(regex_in, concatenated_text)]
        add_coeff = 0
        pop_idx = 0
        for i in range(len(indices)):
            end_idx = indices[0][1]
            if indices and indices[pop_idx] and indices[pop_idx][0] <= 3 + add_coeff:
                indices.pop(pop_idx)
            else:
                pop_idx += 1
            add_coeff = end_idx

        pop_idx = 0
        for i in range(len(indices)):
            end_idx = indices[pop_idx][1]
            probable_address = concatenated_text[:end_idx]
            parsed_address = self.usaddress_parser.parse_text(probable_address)
            if parsed_address['address_line_1'] is None:
                indices.pop(pop_idx)
            else:
                pop_idx += 1

        last = []
        final_indices = []
        final_zip_codes = []
        addresses_as_elements = []  # To store all addresses as lists of elements

        for index, (start, end) in enumerate(indices):
            temp = concatenated_text[0:end] if index == 0 else concatenated_text[last[index - 1]:end]
            last.append(end)

            m = re.search(regex_in, temp)
            if m:
                address.append(temp)
                final_zip_codes.append(zip_codes[index])
                final_indices.append((0 if index == 0 else last[index - 1], m.end()))

            # Now process the elements corresponding to this address
            if elements:
                address_elements = ElementUtils.filter_elements_by_string_indices(elements, 0 if index == 0 else last[index - 1], end)
                addresses_as_elements.append(address_elements)

        return addresses_as_elements, final_indices, final_zip_codes

    def process_each_address(self, addresses, indices, zip_codes):
        """
        Process each address and extract the relevant fields and components.
        :param addresses: List of addresses as elements.
        :param indices: List of start and end indices for each address.
        :param zip_codes: List of zip codes corresponding to each address.
        :return: Dictionary containing processed address components.
        """
        results = {}
        for idx, (address, (start, end), zip_code) in enumerate(zip(addresses, indices, zip_codes)):
            # You can customize this part to extract specific components like street, city, state
            address_str = ''.join([elem['text'] for elem in address])
            results[idx + 1] = {
                "address": address_str,
                "zip_code": zip_code,
                "start_idx": start,
                "end_idx": end,
                "elements": address  # Add the elements that correspond to this address
            }

        return results

# Example usage:
if __name__ == "__main__":
    address_parser = AddressSplitter()

    # Example structured elements (OCR results)
    structured_elements = [
        {'text': 'L 15 Villas @ Saxony, 13376 Dorster Street, Fishers, IN 46037', 'HPOS': 480, 'VPOS': 510,
         'WIDTH': 764, 'HEIGHT': 22, 'END_HPOS': 1244, 'END_VPOS': 532, 'WORDS': [
            {'text': 'L', 'HPOS': 480.0, 'VPOS': 511.3333333333333, 'WIDTH': 0.0, 'HEIGHT': 21.333333333333332},
            {'text': '15', 'HPOS': 503.99999999999994, 'VPOS': 511.3333333333333, 'WIDTH': 13.333333333333428,
             'HEIGHT': 21.333333333333332},
            # ... (Other words)
        ], 'LINE_NUMBER': 9}
    ]

    # Process the elements or string to extract address components
    addresses_as_elements, final_indices, final_zip_codes = address_parser.extract_address_components(
            structured_elements
    )

    print("Addresses as Elements:", addresses_as_elements)
    print("Final Indices:", final_indices)
    print("Final Zip Codes:", final_zip_codes)