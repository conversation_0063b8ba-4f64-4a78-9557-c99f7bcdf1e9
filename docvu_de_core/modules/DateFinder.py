import re
import datetime
from dateutil import parser
import logging

class DateFinder:
    def __init__(self, search_formats=None, return_format="%Y-%m-%d"):
        self.search_formats = search_formats or []
        self.return_format = return_format
        logging.basicConfig(level=logging.INFO)

    def get_dates_in_text(self, text, return_position_info=False, return_first=True):
        if text is None:
            logging.error("No text provided for date extraction.")
            return None

        potential_dates = re.findall(
            r'(?:'
            # Matches "March 5, 2020", "Mar 5th, 2020", etc.
            r'\b\d?'
            r'(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|'
            r'Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|'
            r'Dec(?:ember)?)\s*\d{1,2}(?:st|nd|rd|th)?[.,]*\s*\d{4},?\b|'

            # Matches "17/03/20", "03-18-2020", etc.
            r'\b\d{1,2}(?:st|nd|rd|th)?\s*[-/]\s*\d{1,2}\s*[-/]\s*\d{2,4},?\b|'

            # Matches "17th day of March 2020"
            r'\b\d{1,2}(?:st|nd|rd|th)?\s*(?:day\s*of\s*)?'
            r'(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|'
            r'Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|'
            r'Dec(?:ember)?)\s*[.,]?\s*\d{4},?\b|'

            # Matches "17th day of March 20 20" or similar variations
            r'\b\d{1,2}(?:st|nd|rd|th)?\s*day\s*of\s*'
            r'(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|'
            r'Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|'
            r'Dec(?:ember)?)\s+\d{2}\s+\d{2}\b|'

            # Matches "7th of October, 2022"
            r'\b\d{1,2}(?:st|nd|rd|th)?\s*of\s*'
            r'(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|'
            r'Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|'
            r'Dec(?:ember)?)\s*[.,]?\s*\d{4},?\b|'

            # Matches "11.13.2024", "4-12-2021"
            r'\b\d{1,2}\s*[-./]\s*\d{1,2}\s*[-./]\s*\d{4}\b|'

            # Matches "Start Date 12/12/2020"
            r'\b(?:Start\s*Date\s*)?\d{1,2}\s*/\s*\d{1,2}\s*/\s*\d{4}\b|'

            # Matches "2024-11-20"
            r'\b\d{4}-\d{2}-\d{2}\b|'

            # Matches "Sep. 24, 2027"
            r'\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep(?:t)?|Oct|Nov|Dec)\.\s*\d{1,2},\s*\d{4}\b|'

            # Matches "Sep. 22. 2021", optionally prefixed with backslash
            r'\\?\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep(?:t)?|Oct|Nov|Dec)\.\s*\d{1,2}\.\s*\d{4}\b|'

            # Matches "20 Dec 24", "12 Jan 21"
            r'\b\d{1,2}\s*(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|'
            r'Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\s*\d{2}\b|'

            # Matches "24 day of September A.D. 2021 and 25 day of. February 2025"
            r'\b\d{1,2}\s*day\s*of[\s\.]*'
            r'(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|'
            r'Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|'
            r'Dec(?:ember)?)'
            r'(?:\s*A\.?D\.?\s*\d{4}|\s*\d{4})\b|'

            # Matches "January 2065"
            r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s\d{4}\b|'

            # Matches February 1, 2023.
            # r"\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}(?=[\s.,])"
            r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}\b|'
            
            # Matches "10.20.21"
            r'\b\d{1,2}\.\d{1,2}\.\d{2}\b|'

            # Matches "29th day of Nov. in the year 2010","29th day of Nov, in the year 2010","29th day of Nov , in the year 2010"
            r'\b\d{1,2}(?:st|nd|rd|th)?\s*day\s+of\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Sept|Oct|Nov|Dec)\s*[.,]?\s+in\s+the\s+year\s+\d{4}\b|'

            # Matches "13/Jan/2023"
            r'\b\d{1,2}/(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)/\d{4}\b|'

            # Matches "Oct-12-2018"
            r"\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{2}-\d{4}\b|"

            # Matches "March 3 , 2017"
            r"\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2}\s*,\s*\d{4}\b|"

            #30-Nov-2022
            r"\b\d{1,2}-(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{4}\b|"

            #2022-11-16
            r"\b\d{4}-\d{2}-\d{2}\b|"

            #Nov 01, 2022
            r"\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s\d{2},\s\d{4}\b|"

            #OCT 26 2018 to OCT 26 2019
            r"\b(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|SEPT|OCT|NOV|DEC)\s+\d{1,2}\s+\d{4}\b|"

            #APR 28, 2022 to APR 28, 2023 
            r"\b(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)\s+\d{1,2},\s+\d{4}\b|"

            #MAR 2 1 2025
            r"\b(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|SEPT|OCT|NOV|DEC)\s+\d\s+\d\s+\d{4}\b|"

            #November 20.2013
            r"\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+[0-9]{1,2}[.,\s/-]*[0-9]{4}\b|"

            # Matches "12/28/2O22"
            r"\b\d{1,2}/\d{1,2}/[0-9O]{4}\b|"

            #16 Jun 1975
            r"\b\d{1,2}\s(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s\d{4}\b"
            


            r')',
            text, re.IGNORECASE 
        )

        dates = []

        if not potential_dates:
            return None
        
        for date_str in potential_dates:
            if re.match(r'\b(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|SEPT|OCT|NOV|DEC)\s+\d\s+\d\s+\d{4}\b', date_str, re.IGNORECASE):
                parts = date_str.split()
                if len(parts) == 4:
                    date_str = f"{parts[0]} {parts[1]}{parts[2]} {parts[3]}"
            try:
                # Try to parse each potential date string
                parsed_date = parser.parse(date_str, fuzzy=True, dayfirst=False)
                formatted_date = parsed_date.strftime(self.return_format)
                if return_position_info:
                    start = text.find(date_str)
                    end = start + len(date_str)
                    date_info = (formatted_date, start, end)
                    dates.append(date_info)
                    if return_first:
                        return date_info
                else:
                    dates.append(formatted_date)
                    if return_first:
                        return formatted_date
            except ValueError as e:
                logging.debug(f"Failed to parse date from string: {date_str}, Error: {e}")
                continue

        if not dates and return_first:
            logging.warning(f"No valid dates found in text: {text}")
        
        return dates if not return_first else None

    def remove_dates_in_text(self, text):
        if text is None:
            logging.error("No text provided for date removal.")
            return None
        
        # Enhanced regex to handle multiple commas, attached text, and ordinal dates
        potential_dates = re.findall(
            # Matches short month name or full month name date format like "Dec 23, 2021" or "December 23, 2021"
            r'(?:\b(?:Jan(?:uary)?|Feb(?:ruary|rury)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|'
            r'Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|'
            r'Dec(?:ember)?)\s*\d{1,2}(?:st|nd|rd|th)?[,]*\s*\d{4}\b|'
            # Matches numeric formats like "17/03/20" or "03-18-2020"
            r'\b\d{1,2}(?:st|nd|rd|th)?[-/]\d{1,2}[-/]\d{2,4}\b)',
            text, re.IGNORECASE)

        for date_str in potential_dates:
            try:
                parsed_date = parser.parse(date_str, fuzzy=True, dayfirst=False)
                text = text.replace(date_str, '')
            except ValueError as e:
                logging.debug(f"Failed to parse date from string: {date_str}, Error: {e}")
                continue

        # Clean up extra spaces left after date removal  
        text = re.sub(r'\s+',' ', text).strip()

        if not potential_dates:
            logging.warning(f"No valid dates found to remove in text: {text}")

        return text

if __name__ == '__main__':
    # Example usage:
    search_formats = ["%m/%d/%Y", "%m/%d/%y", "%d/%m/%y", "%d/%m/%Y", "%Y/%m/%d", "%y/%m/%d"]
    return_format = "%d/%m/%Y"
    df = DateFinder(return_format=return_format)

    test_cases = [
        "3/16/2020 @ 1:05 PM",
        "The event was previously held on 17/03/20 and will be next on 3/18/2020.",
        "16/03/2020",
        "recorded on September 23,2019 with Essex County Registry of Deeds Book 37866, Page 193. 5.",
        " March 5,, 2020 at 3:11 PM",
        "for record DECEMBER 23, 2021, at Instrument No. 202107047, Book 2021, page 7047, Miller County Recorders Office.",
        "onJanuary 5, 2020we had an event",
        "The meeting was held on Februry 30, 2020",
        "The deadline is 24th December, 2021",
        "Q3 earnings on October 15, 2021 500 shares were traded.",
        "US Independence Day is 07/04/2021 and World Health Day is 07/04/2021",
        "We will meet on Wednesday, March 15, 2020, at 3 PM",
        "From January 20, 2021 to February 2021",
        "Access the file at 2021/04/01/report.pdf",
        "MARCH 22, 2019 AT RECEPTION NO. 4475318.",
        "April 1.2010 at 2:15 pm in O.R. Book 10023, Pages 2995, Public Records of Orange County, Florida.",
        "11/1/2.",
        "20/11/1983",
        "4th  day  of  September  2020",
        "September 11, 2020 at",
        "August 31. 2021",
        "October 17. 2011",
        "22nd day of January . 2022.",
        "20th day of August, 2021",
        "7th of October, 2022",
        "11.13.2024",
        "2024-11-20",
        "Sep. 24, 2027",
        "0May 27, 2010",
        "20 dec 24",
        "January 2065.",
        "24 day of September A.D. 2021",
        "25 day of. February 2025",
        "00-63155-1 April 24 2004 My Appointment Expires",
        "Sep. 22. 2021",
        "20.10.21",
        "29th day of Nov. in the year 2010","29th day of Nov, in the year 2010","29th day of Nov , in the year 2010",
        "Signature (see instructions) Date Phone number of taxpayer on line 1a or 2a 13/Jan/2023 (*************",
        "Invoice Date: Oct-12-2018",
        "February 1, 2023.",
        "The document was signed on March 3 , 2017 and submitted later."
        "30-Nov-2022",
        "OCT 26 2018 to OCT 26 2019 ",
        "MAR 2 1 2025",
        "November 20.2013",
        "12/28/2O22",
        "16 Jun 1975"
    ]

    for text in test_cases:
        result = df.get_dates_in_text(text, return_position_info=False, return_first=True)
        print(f"Test: {text} || Result: {result}\n")
