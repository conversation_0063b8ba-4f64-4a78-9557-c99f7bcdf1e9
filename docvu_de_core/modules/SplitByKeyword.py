import re


class SplitByKeyword:
    def __init__(self, keywords, consider_numeric=True, consider_roman=True, only_keyword=False):
        """
        Initialize the splitter with options to control the splitting behavior.

        :param keywords: List of keywords to use for splitting text.
        :param consider_numeric: <PERSON><PERSON><PERSON> indicating whether to consider numeric identifiers (e.g., "1", "2").
        :param consider_roman: <PERSON><PERSON><PERSON> indicating whether to consider Roman numeral identifiers (e.g., "I", "II").
        :param only_keyword: <PERSON><PERSON><PERSON> indicating whether to split by keyword alone without any following identifier.
        """
        self.keywords = keywords
        self.consider_numeric = consider_numeric
        self.consider_roman = consider_roman
        self.only_keyword = only_keyword
        self.pattern = self.build_pattern()

    def build_pattern(self):
        """
        Build a regular expression pattern based on the initialization parameters.

        :return: A compiled regular expression pattern.
        """
        parts = []
        if self.only_keyword:
            parts.append(r'\b')
        if self.consider_numeric:
            parts.append(r'\s*\d+')
        if self.consider_roman:
            parts.append(r'\s*[IVXLCDM]+')
        suffix = '|'.join(parts) if parts else ''

        pattern = '|'.join(f"{re.escape(keyword)}{suffix}" for keyword in self.keywords)
        return re.compile(pattern, re.IGNORECASE)

    def split_text(self, input_data):
        """
        Split the text or structured data using the compiled regex pattern.

        :param input_data: The text or list of structured data elements to split.
        :return: A list of sections with heading, content, and optional bounding box.
        """
        if isinstance(input_data, str):
            return self.split_text_string(input_data)
        elif isinstance(input_data, list) and all(isinstance(elem, dict) for elem in input_data):
            return self.split_text_structured(input_data)
        else:
            raise ValueError("Input data must be a string or a list of dictionaries with bounding box data.")

    def split_text_string(self, text):
        """
        Split a plain text string into sections.

        :param text: The text to split.
        :return: A list of (heading, content) tuples.
        """
        splits = []
        prev_end = 0
        current_heading = None
        for match in self.pattern.finditer(text):
            start = match.start()
            if prev_end != start:
                content = text[prev_end:start].strip()
                if content and current_heading:
                    splits.append((current_heading, content))
            current_heading = match.group()
            prev_end = match.end()

        if prev_end < len(text):
            content = text[prev_end:].strip()
            if content:
                splits.append((current_heading, content))

        return splits

    def split_text_structured(self, elements):
        """
        Split text based on structured data elements that include position data.

        :param elements: List of dictionaries containing text and bounding box data.
        :return: A list of sections with heading, content, and bounding box data.
        """
        splits = []
        prev_end = 0
        current_heading = None
        current_bbox = None
        for element in elements:
            text = element['text']
            start = element.get('start', 0)
            if prev_end != start:
                content = text[prev_end:start].strip()
                if content and current_heading:
                    splits.append((current_heading, content, current_bbox))
            match = self.pattern.search(text)
            if match:
                current_heading = match.group()
                current_bbox = (
                element.get('HPOS', 0), element.get('VPOS', 0), element.get('WIDTH', 0), element.get('HEIGHT', 0))
                prev_end = match.end()

        if prev_end < len(text):
            content = text[prev_end:].strip()
            if content:
                splits.append((current_heading, content, current_bbox))

        return splits


# Example usage:
if __name__ == "__main__":
    splitter = SplitByKeyword(['Parcel'], consider_numeric=True, consider_roman=True, only_keyword=False)
    text = "your text here"
    structured_elements = [{"text": text, "HPOS": 100, "VPOS": 200, "WIDTH": 300, "HEIGHT": 100}]

    sections = splitter.split_text(structured_elements)
    for section in sections:
        print(f"Heading: {section[0]}\nContent: {section[1]}\nBounding Box: {section[2]}")
