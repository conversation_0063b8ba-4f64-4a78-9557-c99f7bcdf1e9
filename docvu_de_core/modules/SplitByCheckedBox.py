import os
from docvu_de_core.config import parser_config
from docvu_de_core.utils.TextPreprocessor import TextPreprocessor
from docvu_de_core.utils.OCRUtils import OCRUtils
from docvu_de_core.models import ModelManager
from docvu_de_core.io import OutputFormat
from copy import deepcopy

DEBUG = False


class SplitByCheckedBox:
    yolo_infer = None
    
    def __init__(self, logger):
        self.check_box_id = 'FOUND_OMR'
        self.checked_id = '1'
        self.unchecked_id = '0'
        self.checked_boxes = []
        self.distance_threshold = 40

    def clean_empty_stings(self, lines):
        temp_lines = [] # deepcopy(lines)
        for idx, line in enumerate(temp_lines):
            if line['text'].strip() != '':
                temp_lines.append(idx)
        return lines

    def clean_cb_chars(self, sorted_lines):
        temp_sorted_lines = deepcopy(sorted_lines)
        for idx, line in enumerate(temp_sorted_lines):
            if line[0]['text'] in [f'{self.check_box_id}_{self.checked_id}',  f'{self.check_box_id}_{self.unchecked_id}']:
                continue
            elif (len(line) > 1 and
                    line[1]['text'] in
                    [f'{self.check_box_id}_{self.checked_id}',  f'{self.check_box_id}_{self.unchecked_id}']):
                if len(line[0]['text']) <= 3:
                    sorted_lines[idx].pop(0)
                else:
                    temp = sorted_lines[idx][1]
                    sorted_lines[idx][1] = sorted_lines[idx][0]
                    sorted_lines[idx][0] = temp
                continue
        return sorted_lines
    
    def split_by_checked_boxes(self, lines, no_cb_found = False):
        if len(lines) == 0:
            return [], [], []
            
        if not no_cb_found:
            all_point_hpos = [phrase['HPOS'] for line in lines for phrase in line if not any(phrase['text'].strip().startswith(str(i)) for i in [f'{self.check_box_id}_{self.checked_id}',  f'{self.check_box_id}_{self.unchecked_id}'])]
            all_hpos = [phrase['HPOS'] for line in lines for phrase in line]
            min_hpos = min(all_point_hpos) if len(all_point_hpos) != 0 else min(all_hpos)
            max_hpos = max(all_hpos)
            
            bullet_points = {'checked' : [], 'unchecked' : []}
            current_bullet_point = []
            is_first_line = True            
            is_possible_next_bullet = True
            is_checked_line = False
            is_unchecked_line = False        
            
            for line in lines:
                if is_first_line:
                    is_first_line = False
                    if len(line) > 1:
                        # Check if the line starts with a number followed by a dot or whitespace
                        if not any(line[0]['text'].strip().startswith(str(i)) for i in [f'{self.check_box_id}_{self.checked_id}',  f'{self.check_box_id}_{self.unchecked_id}']):
                            continue
                if ((line[0]['HPOS'] + line[0]['WIDTH']) - min_hpos) > 50:
                    is_possible_next_bullet = False
                elif ((line[0]['HPOS'] + line[0]['WIDTH']) - min_hpos) < 0:
                    is_possible_next_bullet = True
                if line[0]['text'].strip().startswith(str(f'{self.check_box_id}_{self.checked_id}')):
                    # Remove the numeric prefix and the following dot or space
                    line_without_number = OCRUtils.remove_substring_from_elements(line, f'{self.check_box_id}_{self.checked_id}')
                    is_possible_next_bullet = False
                    if current_bullet_point:
                        # Append the current bullet point and start a new one without the numeric prefix
                        if is_checked_line:
                            bullet_points['checked'].append(current_bullet_point)
                        else:
                            bullet_points['unchecked'].append(current_bullet_point)
                        current_bullet_point = line_without_number
                    else:
                        # Start a new bullet point without the numeric prefix
                        current_bullet_point = line_without_number
                    is_checked_line = True
                    is_unchecked_line = False
                elif line[0]['text'].strip().startswith(str(f'{self.check_box_id}_{self.unchecked_id}')) or is_possible_next_bullet:                                   
                    line_without_number = OCRUtils.remove_substring_from_elements(line, f'{self.check_box_id}_{self.unchecked_id}')
                    is_possible_next_bullet = False
                    if current_bullet_point:
                        # Append the current bullet point and start a new one without the numeric prefix
                        if is_checked_line:
                            bullet_points['checked'].append(current_bullet_point)
                        else:
                            bullet_points['unchecked'].append(current_bullet_point)
                        current_bullet_point = line_without_number
                    else:
                        # Start a new bullet point without the numeric prefix
                        current_bullet_point = line_without_number
                    is_checked_line = False
                    is_unchecked_line = True
                else:
                    current_bullet_point.extend(line)
                
                if (max_hpos - (line[-1]['HPOS'] + line[-1]['WIDTH'])) > 80:
                    is_possible_next_bullet = True
                else:
                    is_possible_next_bullet = False

            if current_bullet_point:
                if is_checked_line:
                    bullet_points['checked'].append(current_bullet_point)
                else:
                    bullet_points['unchecked'].append(current_bullet_point)
            bullet_points_str = []
            bullet_points_bbox = []
            bullet_points_line_numbers = []
            for bullet_point in bullet_points['checked']:
                # bbox, line_no = cls.pu.get_bbox_and_line_number(bullet_points)
                bp_str = ''
                start_vpos = float('inf')
                start_hpos = float('inf')
                end_vpos = float('-inf')
                end_hpos = float('-inf')
                line_no = float('inf')
                for line in bullet_point:
                    bp_str += ' ' + line['text']
                    start_vpos = min(start_vpos, line['VPOS'])
                    start_hpos = min(start_hpos, line['HPOS'])
                    end_vpos = max(end_vpos, line['END_VPOS'])
                    end_hpos = max(end_hpos, line['END_HPOS'])
                    line_no = min(line_no, line.get('LINE_NUMBER', 0))
                bullet_points_str.append(bp_str)
                bullet_points_bbox.append((start_hpos, start_vpos, end_hpos, end_vpos))
                bullet_points_line_numbers.append(line_no)
            return bullet_points_str, bullet_points_bbox, bullet_points_line_numbers
        else:
            # return SplitByBullets.split_by_bullets(lines, sort=False)
            return [], [], []

    def post_process(self, value):
        post_processed_values = []
        for v in value:
            found, post_processed_value = TextPreprocessor.strip_and_remove_starting_substring(v, parser_config["OMR_MARKER_CHARS"])
            if post_processed_value.strip() != '':
                post_processed_values.append(post_processed_value)
        return post_processed_values

    def get_checkbox_list(self, block_data, checkbox_cord, field_info, 
                          is_nex_page=False):
        # Get the starting checkbox or unchecked box
        if len(checkbox_cord) == 0:
            starting_checkbox = None
            ending_checkbox = None
        else:
            starting_checkbox = checkbox_cord[0]
            ending_checkbox = checkbox_cord[-1]
            
        search_multi_page = field_info.get('multi_page_value', False)

        end_identifier = field_info.get('end_identifier', [])
        # Check for key matching
        key_found = is_nex_page
        possible_answers = []
        end_pos = None
        found_end_identifier = False
        for item in block_data:
            for block in item["TEXT_BLOCK"]:
                for text_line in block["TEXT_LINE"]:
                    for text_string in text_line["STRING"]:
                        if not key_found and any(key in text_string['text'] for key in field_info['key']):
                            key_found = True
                            # Store or perform any necessary action with the key
                            continue
                        if key_found:
                            if text_string['text'] == 'Countersigned by:':
                                pass
                            if (len(end_identifier) != 0
                                    and any(ei.lower() in text_string['text'].lower()
                                                                 for ei in end_identifier if ei != "")):
                                if ending_checkbox and (ending_checkbox['bbox']['y1'] < text_string['VPOS']):
                                    end_pos = {"VPOS": text_string["VPOS"], "HPOS": text_string["HPOS"]}
                                    found_end_identifier = True
                                    break
                            text_string["LINE_NUMBER"] = text_line["LINE_NUMBER"]
                            possible_answers.append(text_string)
                        if found_end_identifier:
                            break
                    if found_end_identifier:
                        break
                if found_end_identifier:
                    break
            if found_end_identifier:
                break
        for cbc in checkbox_cord:
            if end_pos is not None and found_end_identifier:
                if int(cbc['bbox']['y1']) > end_pos["VPOS"]:
                    continue
            possible_answers.append({
                    'HPOS': int(cbc['bbox']['x1']),
                    'VPOS': int(cbc['bbox']['y1']),
                    'WIDTH': abs(int(cbc['bbox']['y2']) - int(cbc['bbox']['y1'])),
                    'HEIGHT': abs(int(cbc['bbox']['x2']) - int(cbc['bbox']['x1'])),
                    'END_HPOS': int(cbc['bbox']['x2']),
                    'END_VPOS': int(cbc['bbox']['y2']),
                    'text': f'{self.check_box_id}_{self.checked_id}' if cbc['checked'] else f'{self.check_box_id}_{self.unchecked_id}'
                })

        ## add logic to search in the next page if end_identifier is not found
        ## and start identifier is found
        if not found_end_identifier and key_found and search_multi_page:
            return True, possible_answers
        else:
            return True, possible_answers

    def get_checkbox_value(self, blocks_data, field_info, img_path, is_nex_page = False):
        output = OutputFormat()

        base_dir = os.path.dirname(img_path)
        filename = os.path.basename(img_path).split('.')[0] + "_cb.jpg"
        cb_image_path = os.path.join(base_dir, filename)
        
            
        checkbox_data = ModelManager.yolo_v8_for_checkbox.get_checked_boxes(img_path, cb_image_path)
        serch_next_page, checked_boxes = self.get_checkbox_list(blocks_data,
                                                                 checkbox_data, 
                                                                 field_info, 
                                                                 is_nex_page = is_nex_page)
        checked_boxes = self.clean_empty_stings(checked_boxes)
        lines = OCRUtils.sort_and_merge_ocr_data(checked_boxes, merge_lines=False)
        lines = self.clean_cb_chars(lines)
        if DEBUG:
            print('@@@@@@@@@@')
            print(lines)
        output_list, bullet_points_bbox, bullet_points_line_numbers = self.split_by_checked_boxes(lines, \
                                                                                            len(checkbox_data) == 0)
        if DEBUG:
            print('$$$$$$$$')
            print(output_list)
        output_list = self.post_process(output_list)

        if DEBUG:
            print('output_list =', output_list)
        
        return OutputFormat(value = output_list,
                            search_next_page = serch_next_page,
                            success=True,
                            elements = checked_boxes,
                            is_table = True,
                            line_number = bullet_points_line_numbers,
                            bbox = bullet_points_bbox)

