import re
from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils.element_utils import ElementUtils

class SplitByRegEx:
    def __init__(self, regex_list, consider_found_line=False, consider_contents_before_found=False, keyword_zone='complete'):
        """
        Initialize the splitter with options to control the splitting behavior.

        :param regex_list: List of regex patterns to use for splitting text.
        :param consider_found_line: <PERSON><PERSON><PERSON> indicating whether to include text in the line where regex is matched.
        :param consider_contents_before_found: <PERSON><PERSON><PERSON> indicating whether to consider contents before the first regex match.
        :param keyword_zone: The zone where the regex should be found ('complete', 'left', 'right', 'center', 'left_center', 'right_center').
        """
        self.regex_list = regex_list
        self.consider_found_line = consider_found_line
        self.consider_contents_before_found = consider_contents_before_found
        self.keyword_zone = keyword_zone
        self.patterns = self.build_patterns()

    def build_patterns(self):
        """
        Build a list of compiled regular expression patterns based on the provided regex list.

        :return: A list of compiled regular expression patterns.
        """
        return [re.compile(pattern) for pattern in self.regex_list]

    def split_text(self, input_data, get_elements=False, get_keyword_info=False):
        """
        Split the structured data using the compiled regex patterns.

        :param input_data: List of structured data elements to split.
        :param get_elements: Boolean indicating whether to include split elements.
        :param get_keyword_info: Boolean indicating whether to include keyword info.
        :return: An OutputFormat object containing bounding boxes, line numbers, points (text content), and success flag.
        """
        if isinstance(input_data, list) and all(isinstance(elem, dict) for elem in input_data):
            return self.split_text_structured(input_data, get_elements=get_elements, get_keyword_info=get_keyword_info)
        else:
            raise ValueError("Input data must be a list of dictionaries with bounding box data.")



    def split_text(self, input_data, get_elements=False, get_keyword_info=False):
        """
        Split the structured data using the compiled regex patterns.

        :param input_data: List of structured data elements to split.
        :param get_elements: Boolean indicating whether to include split elements.
        :param get_keyword_info: Boolean indicating whether to include keyword info.
        :return: An OutputFormat object containing bounding boxes, line numbers, points (text content), and success flag.
        """
        if isinstance(input_data, list) and all(isinstance(elem, dict) for elem in input_data):
            return self.split_text_structured(input_data, get_elements=get_elements, get_keyword_info=get_keyword_info)
        else:
            raise ValueError("Input data must be a list of dictionaries with bounding box data.")

    def find_matches(self, element, elements):
        """
        Find all matches in the text for the compiled regex patterns within the specified keyword zone.

        :param text: The text to search.
        :param elements: The list of structured data elements to determine positions.
        :return: A list of match objects.
        """
        text = ' '.join(e['text'] for e in element) if isinstance(element, list) else element['text']

        end_hpos = max(e['END_HPOS'] for e in elements)
        start_hpos = min(e['HPOS'] for e in elements)
        width = end_hpos - start_hpos
        zone_limits = {
            'complete': (start_hpos, end_hpos),
            'left': (start_hpos, start_hpos + (width * 0.4)),
            'right': (start_hpos + (width * 0.6), end_hpos),
            'center': (start_hpos + (width * 0.3), start_hpos + (width * 0.7)),
            'left_center': (start_hpos, start_hpos + (width * 0.7)),
            'right_center': (start_hpos + (width * 0.3), end_hpos),
        }

        zone_start, zone_end = zone_limits[self.keyword_zone]

        matches = []
        for pattern in self.patterns:
            for match in pattern.finditer(text):
                start_pos = match.start()
                end_pos = match.end()
                keyword_element = ElementUtils.filter_elements_by_string_indices(element, start_pos, end_pos)
                if not keyword_element:  # Check if keyword_element is empty
                    continue
                element_start_hpos = min(ke['HPOS'] for ke in keyword_element) if isinstance(keyword_element, list) \
                            else keyword_element['HPOS']
                element_end_hpos = max(ke['END_HPOS'] for ke in keyword_element) if isinstance(keyword_element, list) \
                            else keyword_element['END_HPOS']

                if zone_start <= element_start_hpos <= zone_end or zone_start <= element_end_hpos <= zone_end:
                    matches.append(match)
        return matches

    def split_text_structured(self, elements, get_elements=False, get_keyword_info=False):
        """
        Split text based on structured data elements that include position data.

        :param elements: List of dictionaries containing text and bounding box data.
        :param get_elements: Boolean indicating whether to include split elements.
        :param get_keyword_info: Boolean indicating whether to include keyword info.
        :return: An OutputFormat object containing bounding boxes, line numbers, points (text content), and success flag.
        """
        splits = []
        current_heading = None
        buffer = []
        buffer_bbox = []
        buffer_line_numbers = []
        regex_found = False
        first_regex_found = False
        keyword_info = []

        for element in elements:
            matches = self.find_matches(element, elements)
            match_index = 0

            if matches and not regex_found:
                first_regex_found = True
            else:
                first_regex_found = False
            if matches:
                regex_found = True

            if self.consider_contents_before_found and first_regex_found:
                splits.append((
                    '', ' '.join([b['text'] for b in buffer]),
                    (min(b[0] for b in buffer_bbox) if buffer_bbox else 0,
                     min(b[1] for b in buffer_bbox) if buffer_bbox else 0,
                     max(b[2] for b in buffer_bbox) if buffer_bbox else 0,
                     max(b[3] for b in buffer_bbox) if buffer_bbox else 0),
                    min(buffer_line_numbers) if buffer_line_numbers else 0,
                    buffer[:]  # Add the relevant elements
                ))
                keyword_info.append('')

            while match_index < len(matches):
                match = matches[match_index]
                if buffer:
                    content = ' '.join([elem['text'] for elem in buffer]).strip()
                    if content and current_heading:
                        bbox, line_number = ElementUtils.get_bbox_and_line_number(buffer)
                        splits.append((
                            current_heading, content,
                            bbox,
                            line_number,
                            buffer[:]  # Add the relevant elements
                        ))
                    buffer = []
                    buffer_bbox = []
                    buffer_line_numbers = []

                current_heading = match.group()
                buffer.append(element)
                bbox, line_number = ElementUtils.get_bbox_and_line_number(buffer)
                buffer_bbox.append(bbox)
                buffer_line_numbers.append(line_number)
                keyword_info.append(current_heading)

                if not self.consider_found_line:
                    remaining_text_start = match.end()
                    remaining_text_end = len(element['text'])
                    remaining_elements = ElementUtils.filter_elements_by_string_indices([element], remaining_text_start, remaining_text_end)
                    buffer.extend(remaining_elements if isinstance(remaining_elements, list) else [remaining_elements])
                match_index += 1

            if not self.consider_found_line and match_index == 0:
                buffer.append(element)
                buffer_bbox.append((element['HPOS'], element['VPOS'], element['END_HPOS'], element['END_VPOS']))
                buffer_line_numbers.append(element.get('LINE_NUMBER', buffer_line_numbers[-1] if buffer_line_numbers else float('inf')))

        if buffer:
            content = ' '.join([elem['text'] for elem in buffer]).strip()
            if content:
                bbox, line_number = ElementUtils.get_bbox_and_line_number(buffer)
                splits.append((
                    current_heading, content,
                    bbox,
                    line_number,
                    buffer[:]  # Add the relevant elements
                ))

        points = []
        bboxes = []
        line_numbers = []
        split_elements = []
        for split in splits:
            points.append(split[1])
            bboxes.append(split[2])
            line_numbers.append(split[3])
            split_elements.append(split[4])

        return PostProcessOutputFormat(
            bbox=bboxes,
            line_number=line_numbers,
            value=points,
            success=regex_found,
            elements=split_elements if get_elements else None,
            keyword=keyword_info if get_keyword_info else None
        )

    def __call__(self, **kwargs):
        input_data = kwargs.get('elements')
        return self.split_text(input_data, get_elements=kwargs.get('get_elements', False), get_keyword_info=kwargs.get('get_keyword_info', False))


