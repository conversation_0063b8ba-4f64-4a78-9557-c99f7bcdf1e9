import cv2
import os
from docvu_de_core.io import ObjectDetectionOutputFormat
from docvu_de_core.models import ModelManager

class  ExtractLbsdObjects:
    # Mapping of numeric label IDs to their corresponding object type names 
    label_map = {
        6: "initial",
        4: "seal",
        5: "seal_date",
        0: "signature",
    }

    def __init__(self, logger):
        """
        Initializes the ExtractInitial class with a logger instance.
        
        Parameters:
        logger (object): Logger instance for logging messages and errors.
        """
        self.logger = logger
    
    def compute_iou(self, boxA, boxB):
        xA = max(boxA["x1"], boxB["x1"])
        yA = max(boxA["y1"], boxB["y1"])
        xB = min(boxA["x2"], boxB["x2"])
        yB = min(boxA["y2"], boxB["y2"])

        interArea = max(0, xB - xA) * max(0, yB - yA)
        if interArea == 0:
            return 0.0

        boxAArea = (boxA["x2"] - boxA["x1"]) * (boxA["y2"] - boxA["y1"])
        boxBArea = (boxB["x2"] - boxB["x1"]) * (boxB["y2"] - boxB["y1"])
        # Check if either of the areas is zero to prevent division by zero
        if boxAArea == 0 or boxBArea == 0:
            return 0.0

        iou = interArea / float(boxAArea + boxBArea - interArea)
        return iou
         
    # Extracts and returns relevant details from a given object.
    def extract_object_details(self, image_path, output_dir=None, return_cropped_regions=False, class_id=None, class_labels=None):
        """
        Extracts objects details from the label_map using class_id and class_labels.
    
        Parameters:
        image_path (str): Path to the input image.
        output_dir (str, optional): Directory to save cropped initial images. Defaults to None.
        return_cropped_regions (bool, optional): Whether to return cropped initial images. Defaults to False.
        class_id : Numeric identifier representing the object category in the label map.
        class_labels : A list to store corresponding class labels for each detected object.
        
        Returns:
        ObjectDetectionOutputFormat: It containing success status, message, bounding boxes, class labels, 
        confidences, image path, and optionally cropped image regions as arrays or saved file paths.
        """
        bounding_boxes = []  # List to store bounding boxes of detected initials.
        class_labels = []    # List to store class labels ("initial").
        confidences = []     # List to store confidence scores.
        crops = []           # List to store cropped initial images or their paths.

        try:
            # Perform object detection using the YOLOv8 model.
            detected_objects = ModelManager.yolo_v8_for_lbsd_model.get_detected_objects(image_path, output_path=image_path.replace('.jpg', '_lbsd.jpg'))
        except Exception as e:
            # Log the error and return a failure response.
            self.logger.error(f"Error during object detection: {e}")
            return ObjectDetectionOutputFormat(
                success=False,
                message="Error during detection.",
                image_path=image_path
            )
        # Filter detected objects by given class_id and label them using label_map.
        if class_id is not None:
            filtered_objects = {
                self.label_map[class_id]: [
                    obj for obj in detected_objects if obj["class_id"] == class_id and obj["detected"]
                ]
            }
        else:
            # Create a dictionary to hold filtered objects per label
            filtered_objects = {
                label: [obj for obj in detected_objects if obj["class_id"] == class_id and obj["detected"]]
                for class_id, label in self.label_map.items()
            }
            
        # Check if class_id 0 ("signature") exists in detected_objects
        if any(obj["class_id"] == 0 and obj["detected"] for obj in detected_objects):
            signature_objects = filtered_objects.get("signature",[]) 
            selected_signature_objects = []
            iou_threshold=0.5

            for i, bboxA in enumerate(signature_objects):
                keep = True  # Flag to check if bboxA should be kept
            
                for j, bboxB in enumerate(signature_objects):
                    if i != j:  # Don't compare the bounding box with itself
                        iou = self.compute_iou(bboxA['bbox'], bboxB['bbox'])
                    
                    # If IoU is above the threshold, compare areas and choose the larger one
                        if iou > iou_threshold:
                            bboxA_area = (bboxA['bbox']['x2'] - bboxA['bbox']['x1']) * (bboxA['bbox']['y2'] - bboxA['bbox']['y1'])
                            bboxB_area = (bboxB['bbox']['x2'] - bboxB['bbox']['x1']) * (bboxB['bbox']['y2'] - bboxB['bbox']['y1'])
                            
                            # Keep the larger area box and discard the smaller one
                            if bboxA_area < bboxB_area:
                                keep = False
                                break
            
                if keep:
                    selected_signature_objects.append(bboxA)  

                # Replace the signature list with filtered results
                filtered_objects["signature"] = selected_signature_objects

        for label, objects in filtered_objects.items():
            for obj in objects:
                bounding_boxes.append(obj["bbox"])
                class_labels.append(label)
                confidences.append(obj["confidence"])

                if return_cropped_regions:
                    # Read the original image
                    orig_image = cv2.imread(image_path)
                    if orig_image is None:
                        self.logger.error(f"Unable to read image: {image_path}")
                        continue

                    # Extract bounding box coordinates
                    bbox = obj["bbox"]
                    x1, y1, x2, y2 = bbox["x1"], bbox["y1"], bbox["x2"], bbox["y2"]
                    cropped_image = orig_image[y1:y2, x1:x2]  # Crop the detected initial region

                    if output_dir:
                        # Create the output directory if it doesn't exist
                        os.makedirs(output_dir, exist_ok=True)
                        # Create a file path for the cropped image using its label name and image number.
                        cropped_path = os.path.join(output_dir, f"class_labels_{len(crops) + 1}.jpg")  
                        cv2.imwrite(cropped_path, cropped_image)  # Save the cropped image
                        crops.append(cropped_path)  # Store the cropped image path
                    else:
                        crops.append(cropped_image)  # Store the cropped image directly

        return ObjectDetectionOutputFormat(
            success=True,
            message=f"Detected {len(filtered_objects)} filtered_objects(s).",
            bounding_boxes=bounding_boxes,
            class_labels=class_labels,
            confidences=confidences,
            image_path=image_path,
            crops=crops
        )

if __name__ == "__main__":
    # Load the YOLOv8 model for initial detection
    ModelManager.load(None, load_yolo_v8_for_lbsd_model=True, overwrite=False)
