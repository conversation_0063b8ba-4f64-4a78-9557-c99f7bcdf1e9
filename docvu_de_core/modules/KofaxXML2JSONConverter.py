import xml.etree.ElementTree as ET
import json
import os
# from sklearn.cluster import DBSCAN
from pprint import pprint
import numpy as np
from pdf2image import convert_from_path
import pandas as pd
import platform
from PyPDF2 import PdfReader

DEBUG = False


class XMLToJSONConverter:
    """
    This class is designed to convert XML files into a JSON format that aggregates lines into text blocks
    and text blocks into composed blocks, adhering to specified horizontal and vertical thresholds.
    """

    def __init__(self,
                 xml_file_path=None,
                 input_kofax_json_path=None,
                 pdf_file_path=None,
                 output_dir=None,
                 horizontal_threshold_tb=30,
                 vertical_threshold_tb=50,
                 vertical_threshold_cb=200,
                 horizontal_threshold_line=50):
        """
        Initializes the converter with the path to the XML file and thresholds for grouping lines and blocks.

        :param xml_file_path: Path to the XML file to be converted.
        :param horizontal_threshold: Horizontal proximity threshold for grouping lines into a text block.
        :param vertical_threshold: Vertical proximity threshold for grouping text blocks into composed blocks.
        """
        self.input_kofax_json_path = input_kofax_json_path
        self.pdf_file_path = pdf_file_path
        if self.input_kofax_json_path is not None:
            # Replace whitespace with underscores in the filename
            filename_with_underscores = (os.path.splitext(input_kofax_json_path)[0]).replace(" ", "_")
            get_folder_name = filename_with_underscores.split('/')[-1]
            self.output_dir = f"{output_dir}/{get_folder_name}" if output_dir else filename_with_underscores
            if DEBUG:
                print("__init__", "OUTPUT_DIR????", self.output_dir)
            if not os.path.exists(self.output_dir):
                os.makedirs(self.output_dir)

        self.xml_file_path = xml_file_path
        if self.xml_file_path is not None:
            self.output_json_path = self.xml_file_path.replace('xml', 'json')
        else:
            self.output_json_path = None
        self.horizontal_threshold_line = horizontal_threshold_line
        self.horizontal_threshold_tb = horizontal_threshold_tb
        self.vertical_threshold_tb = vertical_threshold_tb
        self.vertical_threshold_cb = vertical_threshold_cb

    def parse_xml(self, xml_text=None):
        """
        Parses the XML file and returns the root element along with page width and height.

        :return: A tuple containing the root element of the XML tree, page width, and page height.
        """
        if xml_text is None:
            tree = ET.parse(self.xml_file_path)
            root = tree.getroot()
        else:
            root = ET.ElementTree(ET.fromstring(xml_text)).getroot()

        return root

    def sort_and_merge_ocr_data(self, text_lines, return_list_of_lines=False):
        if len(text_lines) == 0:
            return []

        sorted_lines = text_lines
        sorted_lines.sort(key=lambda x: (int(x['VPOS']), int(x['HPOS'])))

        lines_grouped = []
        current_line = [sorted_lines[0]]

        for current_text in sorted_lines[1:]:
            vpos_tolerance = max(current_text['HEIGHT'], max(cl['HEIGHT'] for cl in current_line)) // 3
            if abs(int(current_text['VPOS']) - int(current_line[-1]['VPOS'])) <= vpos_tolerance:
                if abs(int(current_text['HPOS']) - int(current_line[-1]['END_HPOS'])) <= 70:
                    last_text = current_line[-1]
                    merged_text = {
                        'text': last_text['text'] + ' ' + current_text['text'],
                        'HPOS': last_text['HPOS'],
                        'VPOS': min(last_text['VPOS'], current_text['VPOS']),
                        'WIDTH': current_text['END_HPOS'] - last_text['HPOS'],
                        'HEIGHT': max(last_text['HEIGHT'], current_text['HEIGHT']),
                        'END_HPOS': current_text['END_HPOS'],
                        'END_VPOS': max(last_text['END_VPOS'], current_text['END_VPOS']),
                        'WORDS': last_text['WORDS'] + current_text['WORDS']
                    }
                    current_line[-1] = merged_text
                else:
                    current_line.append(current_text)
            else:
                # Before sorting the current line by HPOS, adjust VPOS to the minimum in the line
                min_vpos = min(text['VPOS'] for text in current_line)
                for idx in range(len(current_line)):
                    current_line[idx]['VPOS'] = min_vpos  # Update VPOS to the lowest VPOS in the line

                current_line.sort(key=lambda x: int(x['HPOS']))
                lines_grouped.append(current_line)
                current_line = [current_text]

        # Adjust VPOS for the last line before adding it to the grouped lines
        if current_line:
            min_vpos = min(text['VPOS'] for text in current_line)
            for idx in range(len(current_line)):
                current_line[idx]['VPOS'] = min_vpos

            current_line.sort(key=lambda x: int(x['HPOS']))
            lines_grouped.append(current_line)

        ## check for empty lines
        new_lines_grouped = []
        for lg in lines_grouped:
            new_lg = []
            for line in lg:
                if line['text'].strip():
                    new_lg.append(line)
            if new_lg:
                new_lines_grouped.append(new_lg)
        lines_grouped = new_lines_grouped

        if return_list_of_lines:
            if DEBUG:
                print("sort_and_merge_ocr_data", "lines_grouped::", lines_grouped)
            return lines_grouped

        sorted_and_grouped_lines = [item for group in lines_grouped for item in group]

        return sorted_and_grouped_lines

    def extract_lines(self, root, iH=None, iW=None):
        function_name = 'extract_lines'
        # Assuming the PageWidth and PageHeight attributes are always present in the root <Page> element
        pW = int(root.attrib.get('PageWidth')) if root.attrib.get('PageWidth') is not None else None
        pH = int(root.attrib.get('PageHeight')) if root.attrib.get('PageHeight') is not None else None

        if pH == 0 or pH is None:
            pH = iH
        if iW == 0 or pW is None:
            pW = iW
        if iH is None and iW is None:
            iH = pH = 1
            iW = pW = 1

        lines = []
        for page in root.findall('Lines'):
            for text_line in page.findall('Line'):
                for words in text_line.findall('Words'):
                    if not words:
                        continue
                    all_word = words.findall('Word')
                    sorted_words = sorted(all_word, key=lambda w: int(iW * (int(w.get('StartingX')) / pW)))
                    current_line_words = [sorted_words[0]]
                    for i in range(1, len(sorted_words)):
                        previous_word = current_line_words[-1]
                        current_word = sorted_words[i]

                        prev_word_end = int(previous_word.get('EndingX'))
                        curr_word_start = int(current_word.get('StartingX'))

                        if (curr_word_start - prev_word_end) > self.horizontal_threshold_line:
                            self.process_line_segment(current_line_words, lines, pH, pW, iH, iW)
                            current_line_words = [current_word]
                        else:
                            current_line_words.append(current_word)

                    self.process_line_segment(current_line_words, lines, pH, pW, iH, iW)
        if DEBUG:
            print(function_name, "1", 'lines>>')
            pprint(lines)
        lines = self.sort_and_merge_ocr_data(lines, return_list_of_lines=True)

        text_lines = []
        for idx, line in enumerate(lines):
            for line_idx in range(len(line)):
                line[line_idx]['LINE_NUMBER'] = idx + 1
            text_lines.append({'HPOS': min(l['HPOS'] for l in line),
                               'VPOS': line[0]['VPOS'],
                               'WIDTH': line[-1]['END_HPOS'] - line[0]['HPOS'],
                               'HEIGHT': max(l['HEIGHT'] for l in line),
                               'END_HPOS': line[-1]['END_HPOS'],
                               'END_VPOS': max(l['END_VPOS'] for l in line),
                               'LINE_NUMBER': idx + 1,
                               'STRING': line})
        if DEBUG:
            print(function_name, 2, 'modified lines>>')
            pprint(text_lines)
        return text_lines

    def process_line_segment(self, words, lines, pH, pW, iH, iW):
        if not words:
            return

        line_text = ' '.join(word.text for word in words if word.text is not None)
        starting_xs = [int(word.get('StartingX')) for word in words]
        starting_ys = [int(word.get('StartingY')) for word in words]
        ending_xs = [int(word.get('EndingX')) for word in words]
        heights = [int(word.get('Height')) for word in words]

        starting_x = int((int(words[0].get('StartingX')) / pW) * iW)  # min(starting_xs)
        starting_y = int((int(words[0].get('StartingY')) / pH) * iH)  # min(starting_ys)
        ending_x = int((int(words[-1].get('EndingX')) / pW) * iW)  # max(ending_xs)
        width = ending_x - starting_x
        height = int((max(heights) / pH) * iH)

        words_list = []
        for word in words:
            words_list.append({
                'text': word.text,
                'HPOS': ((int(word.get('StartingX')) / pW) * iW),
                'VPOS': ((int(word.get('StartingY')) / pW) * iW),
                'WIDTH': ((int(word.get('EndingX')) / pW) * iW) - \
                         ((int(word.get('StartingX')) / pW) * iW),
                'HEIGHT': ((int(word.get('Height')) / pW) * iW),
            })

        lines.append({
            'text': line_text,
            'HPOS': starting_x,
            'VPOS': starting_y,
            'WIDTH': width,
            'HEIGHT': height,
            'END_HPOS': starting_x + width,
            'END_VPOS': starting_y + height,
            'WORDS': words_list
        })

    def scale_positions(self, lines, enable_vertical_separation):
        h_scale = 1.0 / self.horizontal_threshold_tb
        v_scale = 1.0 / self.vertical_threshold_tb

        if enable_vertical_separation:
            scaled_points = np.array([(line['VPOS'] * v_scale, line['HPOS'] * h_scale) for line in lines])
        else:
            scaled_points = np.array([(line['VPOS'] * v_scale, 0.0) for line in lines])
        return scaled_points

    def calculate_gaps(self, lines):
        """
        Calculate the horizontal and vertical gaps between consecutive lines.
        """
        gaps = []
        for i in range(len(lines) - 1):
            current_line = lines[i]
            next_line = lines[i + 1]

            # Calculate horizontal gap
            horizontal_gap = next_line['HPOS'] - (current_line['HPOS'] + current_line['WIDTH'])

            # Calculate vertical gap
            vertical_gap = next_line['VPOS'] - (current_line['VPOS'] + current_line['HEIGHT'])

            gaps.append((horizontal_gap, vertical_gap))
        return gaps

    def group_lines_into_text_blocks(self, lines, enable_vertical_separation=False, vpos_difference_threshold=100):
        """
        Group lines into text blocks based on the proximity of the end of one text line to the start of another.
        """
        function_name = 'group_lines_into_text_blocks'
        if not lines:
            return []

        gaps = self.calculate_gaps(lines)

        text_blocks = []
        current_block = [lines[0]]

        for i, (horizontal_gap, vertical_gap) in enumerate(gaps):
            # Decide if the current line should start a new block
            if not enable_vertical_separation:
                if vertical_gap > self.vertical_threshold_tb:
                    # Start a new block
                    text_blocks.append(current_block)
                    current_block = [lines[i + 1]]
                else:
                    # Continue the current block
                    current_block.append(lines[i + 1])
            else:
                if horizontal_gap > self.horizontal_threshold_tb or vertical_gap > self.vertical_threshold_tb or \
                        abs(lines[i + 1]['VPOS'] - lines[i]['VPOS']) > vpos_difference_threshold:
                    # Start a new block
                    text_blocks.append(current_block)
                    current_block = [lines[i + 1]]
                else:
                    # Continue the current block
                    current_block.append(lines[i + 1])

        # Add the last block
        if current_block:
            text_blocks.append(current_block)

        formatted_text_blocks = []
        for text_block in text_blocks:
            min_hpos = min(tb['HPOS'] for tb in text_block)
            max_hpos = max(tb['HPOS'] for tb in text_block)
            min_vpos = text_block[0]['VPOS']
            min_end_hpos = min(tb['END_HPOS'] for tb in text_block)
            max_end_hpos = max(tb['END_HPOS'] for tb in text_block)
            min_end_vpos = min(tb['END_VPOS'] for tb in text_block)
            max_end_vpos = max(tb['END_VPOS'] for tb in text_block)

            formatted_text_blocks.append({'HPOS': min_hpos,
                                          'VPOS': min_vpos,
                                          'WIDTH': max_end_hpos - min_hpos,
                                          'HEIGHT': max_end_vpos - min_vpos,
                                          'END_HPOS': max_end_hpos,
                                          'END_VPOS': max_end_vpos,
                                          'TEXT_LINE': text_block})

        if DEBUG:
            print(function_name, 1, "formatted_text_blocks>>")
            pprint(formatted_text_blocks)

        return formatted_text_blocks

    def calculate_block_gaps(self, text_blocks):
        """
        Calculate the vertical gaps between consecutive text blocks.
        """
        gaps = []
        for i in range(len(text_blocks) - 1):
            current_block = text_blocks[i]
            next_block = text_blocks[i + 1]

            # Calculate vertical gap between the current block's end and the next block's start
            vertical_gap = next_block['VPOS'] - (current_block['VPOS'] + current_block['HEIGHT'])

            gaps.append(vertical_gap)
        return gaps

    def group_text_blocks_into_composed_blocks(self, text_blocks):
        """
        Group text blocks into composed blocks based on the proximity of the end of one text block to the start of another.
        """
        function_name = 'group_text_blocks_into_composed_blocks'

        if not text_blocks:
            return []

        # Sort text blocks by the vertical position of their first line
        # text_blocks = sorted(text_blocks, key=lambda block: block[0]['VPOS'])
        gaps = self.calculate_block_gaps(text_blocks)

        composed_blocks = []
        current_composed_block = [text_blocks[0]]

        for i, vertical_gap in enumerate(gaps):
            # Decide if the current text block should start a new composed block
            if vertical_gap > self.vertical_threshold_cb:
                # Start a new composed block
                composed_blocks.append(current_composed_block)
                current_composed_block = [text_blocks[i + 1]]
            else:
                # Continue the current composed block
                current_composed_block.append(text_blocks[i + 1])

        # Add the last composed block
        if current_composed_block:
            composed_blocks.append(current_composed_block)

        formatted_composed_blocks = []
        for idx, composed_block in enumerate(composed_blocks):
            min_hpos = min(cb['HPOS'] for cb in composed_block)
            max_hpos = max(cb['HPOS'] for cb in composed_block)
            min_vpos = composed_block[0]['VPOS']
            min_end_hpos = min(cb['END_HPOS'] for cb in composed_block)
            max_end_hpos = max(cb['END_HPOS'] for cb in composed_block)
            min_end_vpos = min(cb['END_VPOS'] for cb in composed_block)
            max_end_vpos = max(cb['END_VPOS'] for cb in composed_block)

            formatted_composed_blocks.append({"ID": f"cblock_{idx}",
                                              'HPOS': min_hpos,
                                              'VPOS': min_vpos,
                                              'WIDTH': max_end_hpos - min_hpos,
                                              'HEIGHT': max_end_vpos - min_vpos,
                                              'END_HPOS': max_end_hpos,
                                              'END_VPOS': max_end_vpos,
                                              'TEXT_BLOCK': composed_block})

        if DEBUG:
            print(function_name, 1, "formatted_composed_blocks>>")
            pprint(formatted_composed_blocks)

        return formatted_composed_blocks

    def convert_to_json_new(self, xml_text=None, enable_vertical_separation=False,
                            iH=None, iW=None):
        """
        Converts the XML file content into a JSON format.

        :return: JSON-like list structure representing the composed blocks with nested text blocks and lines.
        """
        function_name = 'convert_to_json_new'
        if xml_text is None:
            root = self.parse_xml()
        else:
            root = self.parse_xml(xml_text=xml_text)

        lines = self.extract_lines(root, iH, iW)
        # text_blocks = self.group_lines_into_text_blocks(lines, enable_vertical_separation = enable_vertical_separation)
        text_blocks = self.group_lines_into_text_blocks(lines)

        composed_blocks = self.group_text_blocks_into_composed_blocks(text_blocks)
        if not iW:
            pW = int(root.attrib.get('PageWidth')) if root.attrib.get('PageWidth') is not None else None
        else:
            pW = iW
        if not iH:
            pH = int(root.attrib.get('PageHeight')) if root.attrib.get('PageHeight') is not None else None
        else:
            pH = iH
        page_dict = {'PAGE_WIDTH': pW,
                     'PAGE_HEIGHT': pH,
                     'COMPOSED_BLOCKS': composed_blocks}

        if DEBUG:
            print(function_name, 1, "page_dict>>")
            pprint(page_dict)

        return page_dict

    def convert_to_json(self, xml_text=None, enable_vertical_separation=False,
                        iH=None, iW=None):
        """
        Converts the XML file content into a JSON format.

        :return: JSON-like list structure representing the composed blocks with nested text blocks and lines.
        """
        if xml_text is None:
            root = self.parse_xml()
        else:
            root = self.parse_xml(xml_text=xml_text)

        lines = self.extract_lines(root, iH, iW)
        # text_blocks = self.group_lines_into_text_blocks(lines, enable_vertical_separation = enable_vertical_separation)
        text_blocks = self.group_lines_into_text_blocks(lines)

        composed_blocks = self.group_text_blocks_into_composed_blocks(text_blocks)

        # Initialize the JSON output list
        json_output = []

        # Iterate through composed blocks to construct the JSON structure
        for cb_index, cblock in enumerate(composed_blocks):
            HPOS_list = []
            VPOS_list = []
            End_HPOS_list = []
            End_VPOS_list = []
            for block in cblock:
                for line in block:
                    HPOS_list.append(line['HPOS'])
                    VPOS_list.append(line['VPOS'])
                    End_HPOS_list.append(line['END_HPOS'])
                    End_VPOS_list.append(line['END_VPOS'])

            min_hpos = min(HPOS_list)
            max_hpos = max(End_HPOS_list)
            min_vpos = min(VPOS_list)
            max_vpos = max(End_VPOS_list)

            cblock_width = max_hpos - min_hpos
            cblock_height = max_vpos - min_vpos
            cblock_dict = {
                "ID": f"cblock_{cb_index}",
                "HPOS": min_hpos,
                "VPOS": max_vpos,
                "WIDTH": cblock_width,
                "HEIGHT": cblock_height,
                "IMAGE_HEIGHT": iH,
                "IMAGE_WIDTH": iW,
                "TEXT_BLOCK": []
            }

            # Flatten all text lines from all text blocks within the composed block
            all_lines = [line for tblock in cblock for line in tblock]
            # Sort lines first by VPOS, then by HPOS if VPOS values are within a threshold of 5 units
            all_lines.sort(key=lambda x: (x['VPOS'], x['HPOS']))

            text_block_dict = {"TEXT_LINE": []}
            for line in all_lines:
                line_dict = {
                    "HPOS": line['HPOS'],
                    "VPOS": line['VPOS'],
                    "WIDTH": line['WIDTH'],
                    "HEIGHT": line['HEIGHT'],
                    "END_HPOS": line['END_HPOS'],
                    "END_VPOS": line['END_VPOS'],
                    "STRING": [{
                        "text": line['text'],
                        "HPOS": line['HPOS'],
                        "VPOS": line['VPOS'],
                        "WIDTH": line['WIDTH'],
                        "HEIGHT": line['HEIGHT'],
                        "SCORE": 99.99  # Placeholder for score
                    }]
                }
                text_block_dict["TEXT_LINE"].append(line_dict)

            # Calculate TEXT_BLOCK dimensions based on aggregated TEXT_LINE dimensions
            if text_block_dict["TEXT_LINE"]:
                text_block_dict["HPOS"] = min(line["HPOS"] for line in text_block_dict["TEXT_LINE"])
                text_block_dict["VPOS"] = min(line["VPOS"] for line in text_block_dict["TEXT_LINE"])
                text_block_dict["WIDTH"] = max(line["END_HPOS"] for line in text_block_dict["TEXT_LINE"]) - \
                                           text_block_dict["HPOS"]
                text_block_dict["HEIGHT"] = max(line["END_VPOS"] for line in text_block_dict["TEXT_LINE"]) - \
                                            text_block_dict["VPOS"]

            cblock_dict["TEXT_BLOCK"].append(text_block_dict)
            json_output.append(cblock_dict)

        # Return the constructed JSON output
        return json_output

    def process_pages_and_generate_jsons(self, enable_vertical_separation=False,
                                         flush_dir=False,
                                         scale_according_to_image_dimention=True,
                                         for_testing=False):
        """
        Processes each page in the input_data, converting PageXML to JSON,
        and saves the JSON to a file named after the PageNumber in output_dir.

        :param input_data: Dictionary containing PageNumber and PageXML.
        :param output_dir: Directory where the JSON files will be saved.
        """
        combined_json = {}
        # Set the path to the directory containing Poppler binaries
        # Check the operating system
        # Modified for Loading the large files
        if platform.system() == "Linux":
            images = []
            total_pages = len(PdfReader(self.pdf_file_path).pages)
            for page_num in range(1, total_pages + 1):
                page_images = convert_from_path(self.pdf_file_path, dpi=200, first_page=page_num, last_page=page_num)
                images.extend(page_images)  # Add current page image(s) to the list
        elif platform.system() == "Darwin":
            images = []
            total_pages = len(PdfReader(self.pdf_file_path).pages)
            for page_num in range(1, total_pages + 1):
                page_images = convert_from_path(self.pdf_file_path, dpi=200, first_page=page_num, last_page=page_num)
                images.extend(page_images)  # Add current page image(s) to the list
                # poppler_path='/opt/homebrew/opt/poppler/bin'
        else:
            poppler_path = "/Users/<USER>/Downloads/poppler-0.68.0/poppler-0.68.0/bin"
            images = convert_from_path(self.pdf_file_path, dpi=200,
                                       poppler_path=poppler_path)  # , poppler_path='/opt/homebrew/opt/poppler/bin')
        # Convert PDF to a list of PIL Image objects
        with open(self.input_kofax_json_path, "r", encoding='utf-8', errors='ignore') as infile:
            input_data = json.load(infile)
            if for_testing:
                input_data = input_data['OcrData'] if "OcrData" in input_data else input_data['OcrDocument'][0][
                    "OcrData"]
            else:
                input_data = input_data['OcrData'] if isinstance(input_data, dict) \
                    else input_data
            for page_data in input_data:

                page_number = page_data['PageNumber']
                page_xml = page_data['PageXML']

                ## get size and save image
                images[page_number - 1].save(os.path.join(self.output_dir, f"{page_number}.jpg"), "JPEG")
                width, height = images[page_number - 1].size

                # Use the existing method to convert XML to JSON
                self.xml_file_path = page_xml  # Temporarily assign page_xml for compatibility
                if len(page_xml) == 0:
                    json_output = {"PAGE_WIDTH": width, "PAGE_HEIGHT": height, "COMPOSED_BLOCKS": []}
                else:
                    if scale_according_to_image_dimention:
                        json_output = self.convert_to_json_new(xml_text=page_xml,
                                                               enable_vertical_separation=enable_vertical_separation,
                                                               iW=width, iH=height)
                    else:
                        json_output = self.convert_to_json_new(xml_text=page_xml,
                                                               enable_vertical_separation=enable_vertical_separation,
                                                               iW=None, iH=None)
                json_filename = os.path.join(self.output_dir, f"{page_number}.json")
                with open(json_filename, "w") as json_file:
                    json.dump(json_output, json_file, indent=4)

                combined_json[page_number] = json_output

                print(f"Generated JSON for Page {page_number} at {json_filename}")
            combined_json_filename = os.path.join(self.output_dir, f"combined.json")
            with open(combined_json_filename, "w+") as combined_json_file:
                json.dump(combined_json, combined_json_file)

            return combined_json_filename, self.output_dir


# Usage example
if __name__ == '__main__':
    DEBUG = True
    json_file = "/Users/<USER>/Downloads/8_PDFsam_space issue.json"
    pdf_file = "/Users/<USER>/Downloads/8_PDFsam_space issue.pdf"
    output_dir = "/Users/<USER>/Downloads"

    if json_file.endswith('.xml'):
        converter = XMLToJSONConverter(xml_file_path=json_file)
        json_output = converter.convert_to_json(enable_vertical_separation=False)
        print(json_output)
    else:
        converter = XMLToJSONConverter(input_kofax_json_path=json_file, pdf_file_path=pdf_file, output_dir=output_dir)
        converter.process_pages_and_generate_jsons(enable_vertical_separation=False)