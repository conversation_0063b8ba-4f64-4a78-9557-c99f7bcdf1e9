from docvu_de_core import configure_logging
from docvu_de_core.modules import SplitByCheckedBox
import cv2
from docvu_de_core.models import ModelManager
from PIL import Image


class ExtractOMRData:
    """
    A class for extracting and processing Optical Mark Recognition (OMR) data from images.
    """
    def __init__(self):
        self.debug = False

    def draw_bounding_box(self, image_path, coordinates_list, text):
        """
        Draws a bounding box on the image at the specified coordinates.

        Parameters:
        image_path (str): Path to the image file.
        coordinates_list (tuple): Coordinates of the bounding box (x1, y1, x2, y2).
        text (str): Text to be appended to the output image filename.

        Returns:
        None
        """
        # Load the image
        image = cv2.imread(image_path)
        # Unpack the coordinates
        x1, y1, x2, y2 = coordinates_list
        output_path = image_path.replace('.jpg', f'_{text}.jpg')
        # Draw the rectangle on the image
        cv2.rectangle(image, (x1, y1), (x2, y2), color=(0, 255, 0), thickness=2)
        # Save the output image
        cv2.imwrite(output_path, image)

    def get_zone_starting_points(self, element, text_list):
        """
        Retrieves the starting coordinates of a text zone in the document.

        Parameters:
        element (dict): Dictionary containing the OCR results.
        text (str): Text to search for in the document.

        Returns:
        tuple: Starting coordinates (HPOS, VPOS) of the text zone, or None if not found.
        """
        if self.debug:
            print("element for omr:", element)

        text_hpos_list = []
        text_vpos_list = []

        for text in text_list:
            text = text.lower()
            for text_line in element:
                if text in text_line['text'].lower():
                    for words in text_line['WORDS']:
                        if words['text'] is not None:
                            if text in words['text'].lower():
                                # start_x = words['HPOS']
                                # start_y = words['VPOS']
                                # return (start_x, start_y)

                                text_hpos_list.append(words['HPOS'])
                                text_vpos_list.append(words['VPOS'])

        if len(text_hpos_list) > 0 and len(text_vpos_list) > 0:
            start_x = min(text_hpos_list)
            start_y = min(text_vpos_list)
        
            return (start_x, start_y)
        
        return None

    def prepare_abs_vpos_zone(self, image_path, key_element, item_key, zone_bbox):
        """
        Prepares the absolute vertical position zone based on the image and OCR data.

        Parameters:
        image_path (str): Path to the image file.
        key_element (dict): Dictionary containing the OCR results.
        item_key (list): List containing the key text.
        zone_bbox (tuple): Tuple representing the width and height of the zone.

        Returns:
        tuple: Bounding box coordinates (x1, y2, x2, y1), or None if starting points not found.
        """
        zone_bbox = self.get_zone_bbox_image(image_path, zone_bbox)
        zone_sp = self.get_zone_starting_points(key_element, item_key)
        if zone_sp:
            if zone_bbox[0] < 0:
                hpos = zone_sp[0] + zone_bbox[0]
                end_hpos = zone_sp[0]
            else:
                hpos = zone_sp[0]
                end_hpos = zone_sp[0] + zone_bbox[0]

            if zone_bbox[1] < 0:
                vpos = zone_sp[1] + zone_bbox[1]
                end_vpos = zone_sp[1]
            else:
                vpos = zone_sp[1]
                end_vpos = zone_sp[1] + zone_bbox[1]

            return (int(hpos), int(end_vpos), int(end_hpos), int(vpos))
        else:
            return None

    def get_zone_bbox_image(self, image_path, zone_bbox):
        """
        Converts zone bounding box dimensions from relative to absolute values.

        Parameters:
        image_path (str): Path to the image file.
        zone_bbox (tuple): Tuple representing the relative width and height of the zone.

        Returns:
        tuple: Absolute width and height of the zone.
        """
        img = Image.open(image_path)
        width, height = img.size
        if self.debug:
            print(f"Width: {width}, Height: {height}")
        zone_bbox = zone_bbox[0] * width / 1000, zone_bbox[1] * height / 1000
        return zone_bbox

    def get_matched_bbox(self, field_bbox, check_box_result, range_percent=5):
        """
        Matches checkboxes within a specific bounding box range.x

        Parameters:
        field_bbox (tuple): Coordinates of the field's bounding box (x1, y1, x2, y2).
        check_box_result (list): List of detected checkboxes with their bounding boxes.
        range_percent (int): Percentage range for matching coordinates.

        Returns:
        list: List of matched checkboxes within the specified range.
        """
        x1_gt, y1_gt, x2_gt, y2_gt = field_bbox[0], field_bbox[1], field_bbox[2], field_bbox[3]
        matched_checkbox_list = []
        for checkbox in check_box_result:
            bbox = checkbox['bbox']
            x1, y1, x2, y2 = bbox['x1'], bbox['y1'], bbox['x2'], bbox['y2']
            # Calculate the range limits
            x_range = (x2_gt - x1_gt) * (range_percent / 100)
            y_range = (y1_gt - y2_gt) * (range_percent / 100)
            # Check if box1 is inside box2 within the range
            if x1 >= x1_gt - x_range and y1 <= y1_gt + y_range and \
               x2 <= x2_gt + x_range and y2 >= y2_gt - y_range:
                matched_checkbox_list.append(checkbox)
        if self.debug:
            print("no of checkboxes in the zone:", len(matched_checkbox_list))
        return matched_checkbox_list

    def get_sorted_checkbox(self, matched_checkbox_list, vpos_tolerance=3):
        """
        Sorts the matched checkboxes based on vertical and horizontal positions.

        Parameters:
        matched_checkbox_list (list): List of matched checkboxes with their bounding boxes.
        vpos_tolerance (int): Tolerance level for grouping lines based on vertical position.

        Returns:
        list: List of sorted checkboxes.
        """
        sorted_lines = sorted(matched_checkbox_list, key=lambda x: (x['bbox']['y1'], x['bbox']['x1']))
        # Step 2: Group by lines considering VPOS tolerance
        lines_grouped = []
        current_line = [sorted_lines[0]]  # Start with the first word
        for next_text in sorted_lines[1:]:
            # If the current text is within VPOS tolerance, it's part of the current line
            if abs(int(next_text['bbox']['y1']) - int(current_line[-1]['bbox']['y1'])) <= 0.01* abs(max(int(next_text['bbox']['y1']),int(current_line[-1]['bbox']['y1']) )):
                current_line.append(next_text)
            else:
                # Sort the current line by HPOS and add to the grouped lines
                current_line.sort(key=lambda x: int(x['bbox']['x1']))
                for i in current_line:
                    lines_grouped.append(i)
                current_line = [next_text]  # Start a new line group
        # Don't forget to add the last line

        current_line.sort(key=lambda x: int(x['bbox']['x1']))
        for i in current_line:
            lines_grouped.append(i)
        return lines_grouped

    def get_checked_box_number(self, sorted_checkbox_list):
        """
        Retrieves the index of checked checkboxes from the sorted list.

        Parameters:
        sorted_checkbox_list (list): List of sorted checkboxes with their status.

        Returns:
        list: List of indices representing the checked checkboxes.
        """
        is_checked_list = []
        for index, bbox_list in enumerate(sorted_checkbox_list):
            is_checked = bbox_list['checked']
            if is_checked:
                is_checked_list.append(index)
        return is_checked_list

    def filter_present_field_options(self, ocr_data, field_options):
        present_options = []
        text_blocks = [item['text'] for item in ocr_data]

        for option in field_options:
            for text in text_blocks:
                if option in text:
                    present_options.append(option)
                    break  # Avoid duplicates if found multiple times
        return present_options


    def get_checkbox_value_from_image(self, image_path, key_element, item):
        """
        Extracts checkbox values from an image based on OCR and YOLO inference.

        Parameters:
        image_path (str): Path to the image file.
        key_element (dict): Dictionary containing the OCR results.
        item (dict): Dictionary containing the key and additional information.

        Returns:
        list: List of checked field values, or None if no checkboxes matched.
        """
        # Prepare the zone coordinates based on the image and OCR data
        zone_coordinates = self.prepare_abs_vpos_zone(image_path, key_element, item['key'], item['additional_info']['zone_coords'])
        if zone_coordinates:
            # Draw the bounding box around the zone
            self.draw_bounding_box(image_path, zone_coordinates, item['key'][0])
            # Perform YOLO inference to detect checkboxes
            # check_box_result = yolo_infer.get_checked_boxes(image_path, output_path=image_path.replace('.jpg', '_cb.jpg'))
            check_box_result = ModelManager.yolo_v8_for_checkbox.get_checked_boxes(image_path,  output_path=image_path.replace('.jpg', '_cb.jpg'))
            
            if self.debug:
                print("No of checkboxes detected:", len(check_box_result))
            
            # Match detected checkboxes within the bounding box
            matched_checkbox = self.get_matched_bbox(zone_coordinates, check_box_result)
            
            if matched_checkbox:
                field_options = item['additional_info']['field_options']

                if "1003_table_cb" in item["additional_info"]:
                    table_cb = self.process_checkboxes(matched_checkbox)
                    return table_cb

                if "field_options_variant" in item['additional_info']:
                    # match field options which are part of defined zone
                    field_options = self.filter_present_field_options(key_element, field_options) # function call

                # Sort the matched checkboxes
                sorted_checkbox_list = self.get_sorted_checkbox(matched_checkbox)
                
                # Filter the checkboxes to match the number of field options
                if len(field_options) == len(sorted_checkbox_list):
                    filter_checkbox_list = sorted_checkbox_list
                else:
                    filter_checkbox_list = sorted_checkbox_list[:len(field_options)]

                for text_id in range(min(len(field_options), len(filter_checkbox_list))):
                    filter_checkbox_list[text_id]["text"] = field_options[text_id]
                
                for cb_id in filter_checkbox_list:
                        if cb_id['class_id'] == 1 and cb_id['text']:
                            cb_id['text_bbox'] = self.extract_text_positions(key_element, cb_id['text'])

                # Get the indices of checked checkboxes
                is_checked_list = self.get_checked_box_number(filter_checkbox_list)
                if not is_checked_list and len(filter_checkbox_list)+1 == len(field_options):
                     is_checked_list = [-1]
                
                if self.debug:
                    print("sorted_matched_checkbox", filter_checkbox_list)
                    print("is_checked_list", is_checked_list)

                # Retrieve the field values corresponding to the checked checkboxes
                checked_fields = {}
                for cl in is_checked_list:
                    checked_fields[cl] = {}
                    # checked_field_value = field_options[cl]

                    if 'text_bbox' in filter_checkbox_list[cl] and filter_checkbox_list[cl]['text_bbox']:
                        checked_field_value_bbox = self.merge_bboxes(filter_checkbox_list[cl]['bbox'], filter_checkbox_list[cl]['text_bbox'])
                    else: 
                        field_value_bbox = filter_checkbox_list[cl]['bbox']
                        checked_field_value_bbox = {
                            'x': field_value_bbox['x1'], 
                            'y': field_value_bbox['y1'],
                            'width': field_value_bbox['x2'] - field_value_bbox['x1'], 
                            'height': field_value_bbox['y2'] - field_value_bbox['y1']
                        }
                   
                    # checked_field_value_list.append(checked_field_value)
                    checked_fields[cl]["value"] = filter_checkbox_list[cl]['text']
                    checked_fields[cl]["bbox"] = checked_field_value_bbox
                # return checked_field_value_list
                return checked_fields
        return None
    
    def extract_text_positions(self, ocr_data, target_text):
        """
        Extracts the position and dimensions of a target text in OCR data.

        Parameters:
        - ocr_data (list): A list of dictionaries containing OCR-extracted text and word positions.
        - target_text (str): The text to locate within the OCR data.

        Returns:
        - dict: A dictionary with the bounding box coordinates (x, y, width, height) of the detected text.
        - None: If the target text is not found in the OCR data.
        """
        
        # Split the target text into individual words for matching
        words = target_text.split()

        # Iterate over the OCR data items
        for item in ocr_data:
            # Check if the target text is present in the item's text field
            if target_text in item['text']:
                word_positions = []  # List to store matching word positions

                # Iterate over the words in the OCR result
                for word in item.get('WORDS', []):
                    if word['text']:  # Ensure the word has a valid text entry
                        # Check if the word matches any part of the target text
                        if word['text'] in target_text:  # Substring match
                            word_positions.append(word)

                # If any matching words were found, calculate bounding box coordinates
                if word_positions:
                    # Determine the leftmost (smallest HPOS) and topmost (smallest VPOS) position
                    hpos = min(w['HPOS'] for w in word_positions)
                    vpos = min(w['VPOS'] for w in word_positions)

                    # Calculate the rightmost position (end_hpos) and the width of the bounding box
                    end_hpos = max(w['HPOS'] + w['WIDTH'] for w in word_positions)
                    width = end_hpos - hpos + 15  # Adding padding for better bounding box coverage

                    # Determine the maximum height among all matched words
                    height = max(w['HEIGHT'] for w in word_positions)

                    # Return the bounding box coordinates
                    return {
                        'x': hpos,
                        'y': vpos,
                        'width': width,
                        'height': height
                    }

        # Return None if the target text is not found in the OCR data
        return None


    def merge_bboxes(self, bbox1, bbox2):
        """
        Merge two bounding boxes and return the minimum x, minimum y, 
        and the calculated width and height of the encompassing bounding box.
        
        Parameters:
        bbox1, bbox2: Tuples of the form (x, y, width, height)
        
        Returns:
        (min_x, min_y, merged_width, merged_height)
        """
        
        min_x = int(min(bbox1['x1'], bbox2['x']))
        min_y = int(min(bbox1['y1'], bbox2['y']))
        max_x = int(max(bbox1['x2'], bbox2['x'] + bbox2['width']))
        max_y = int(max(bbox1['y2'], bbox2['y'] + bbox2['height']))
        
        merged_width = max_x - min_x
        merged_height = max_y - min_y
        
        return {
            'x': min_x, 
            'y': min_y, 
            'width': merged_width, 
            'height': merged_height
        }
    
    def process_checkboxes(self, checkboxes):
        """
        Sort checkboxes by y1, add 'value' field, and transform bbox to:
        {
            'x': x1,
            'y': y1,
            'width': x2 - x1,
            'height': y2 - y1
        }
        Return as dict with keys 1, 2, 3,...
        """
        # Sort by bbox y1
        sorted_checkboxes = sorted(checkboxes, key=lambda x: x['bbox']['y1'])
        
        # Process each checkbox
        for cb in sorted_checkboxes:
            cb['value'] = 'yes' if cb['checked'] else 'no'
            field_value_bbox = cb['bbox']
            # cb['bbox'] = {
            #     'x': field_value_bbox['x1'],
            #     'y': field_value_bbox['y1'],
            #     'width': field_value_bbox['x2'] - field_value_bbox['x1'],
            #     'height': field_value_bbox['y2'] - field_value_bbox['y1']
            # }
            cb['bbox'] = (
                field_value_bbox['x1'],
                field_value_bbox['y1'],
                field_value_bbox['x2'] - field_value_bbox['x1'],
                field_value_bbox['y2'] - field_value_bbox['y1']
            )
        
        # Numbered dict
        numbered_dict = {i: cb for i, cb in enumerate(sorted_checkboxes)}
        
        return numbered_dict