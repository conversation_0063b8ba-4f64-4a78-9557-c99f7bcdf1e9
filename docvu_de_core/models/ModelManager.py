import os
import tempfile
import re
import yaml
import zipfile
import importlib.util
import torch
from pathlib import Path
from transformers import AutoModelForCausalLM, AutoTokenizer
from docvu_de_core.models.InstructionTextGenerationPipeline import InstructionTextGenerationPipeline
from docvu_de_core.models import YOLOv8ForCheckBox
from azure.storage.blob import BlobServiceClient
from docvu_de_core.config import azure_storage_connection_string, model_blob_container_name
from docvu_de_core.models.GLiNERModel import GLiNERModel
import tensorflow as tf

MODEL_MODULE = 'docvu_de_core.models'

# Set the hidden cache directory for models, using Path to support Windows
CACHE_DIR = Path.home() / ".models_cache"


class ModelManager:
    slm_tokenizer = None
    slm_model = None
    slm_generate_text = None
    slm_model_loaded = False

    ner_model = None
    ner_model_loaded = False

    yolo_v8_for_checkbox = None
    yolo_v8_for_checkbox_loaded = False

    subdivision_model = None
    subdivision_model_loaded = False

    image_matching_model = None
    image_matching_model_loaded = False

    yolo_v8_for_lbsd_model = None
    yolo_v8_for_lbsd_model_loaded = False

    overwrite = True

    config = None

    @classmethod
    def load_config(cls, config):
        """
        Load the configuration from a YAML file.

        Args:
            config_path (str): Path to the YAML configuration file.
        """
        cls.config = config

    @classmethod
    def fetch_model_info(cls, model_key):
        """
        Fetch model information from the loaded YAML configuration.

        Args:
            model_key (str): Key to identify the model in the configuration file.

        Returns:
            tuple: (model_name, model_blob_path)
        """
        if cls.config is None:
            raise ValueError("Configuration not loaded. Call `load_config` first.")

        model_config = cls.config.get('models', {}).get(model_key, {})
        if not model_config:
            raise KeyError(f"Configuration for {model_key} not found in the YAML file.")

        model_name = model_config.get('model_name')
        model_blob_path = model_config.get('model_blob_path')

        if not model_name or not model_blob_path:
            raise ValueError(
                f"Invalid configuration for model '{model_key}': Missing 'model_name' or 'model_blob_path'.")

        return model_name, model_blob_path

    @classmethod
    def load(cls,
             logger,
             load_ner=False,
             load_slm=False,
             load_yolo_v8_for_checkbox=False,
             load_subdivision_model=False,
             image_matching_model=False,
             load_yolo_v8_for_lbsd_model=False,
             overwrite=True,
             *kwargs):
        """
        Load models as requested. Try loading each model on all available GPUs.
        If no GPU is available, load the model on the CPU.
        Args:
            logger (logger): Logger for logging information.
            load_ner (bool): Whether to load the NER model (default: True).
            load_slm (bool): Whether to load the sequence-to-sequence language model (default: False).
            load_yolo_v8_for_checkbox (bool): Whether to load YOLO for checkbox detection (default: False).
            load_subdivision_model (bool): Whether to load the subdivision model from blob (default: False).
            image_matching_model (bool): Whether to load the Image Matching model (default: False).
        """

        def load_model_on_device(load_func, model_name, model_attr):
            """Load the model on the first available GPU, or fall back to CPU."""
            for gpu in available_gpus:
                try:
                    is_model_loaded, model = load_func(gpu)  # Load the model (default on CPU)
                    model.to(gpu)
                    if is_model_loaded:
                        logger.info(f"{model_name} successfully loaded on {gpu}.")
                        return  model, is_model_loaded, gpu  # Return the GPU device
                except RuntimeError as e:
                    logger.warning(f"Failed to load {model_name} on {gpu}. Error: {e}")

            # If all GPU attempts fail, load on CPU
            logger.info(f"Loading {model_name} on CPU.")
            is_model_loaded, model = load_func("cpu")  # Load on CPU
            model.to('cpu')
            if is_model_loaded:
                return model, is_model_loaded, torch.device("cpu")
            return None, False, None

        def load_model_on_device_tf2(load_func, model_name, model_attr):
            """
            Load the model on the first available GPU, or fall back to CPU in case of failure.
            Args:
                load_func: A function that loads the model.
                model_name: Name of the model being loaded.
                model_attr: Attribute to store the loaded model.
            Returns:
                model: The loaded model.
                is_model_loaded: Whether the model was successfully loaded.
                device: The device the model was loaded on (GPU or CPU).
            """
            available_gpus = tf.config.experimental.list_physical_devices('GPU')

            # Try loading the model on available GPUs
            for gpu in available_gpus:
                try:
                    # Use the tf.device context manager to assign the model to the specific GPU
                    with tf.device(gpu.name):
                        model, is_model_loaded = load_func(gpu.name)
                        if is_model_loaded:
                            logger.info(f"{model_name} successfully loaded on {gpu.name}.")
                            return model, is_model_loaded, gpu.name
                except RuntimeError as e:
                    logger.warning(f"Failed to load {model_name} on {gpu.name}. Error: {e}")

            # If all GPU attempts fail, load on CPU
            logger.info(f"Loading {model_name} on CPU.")
            with tf.device('/CPU:0'):
                model, is_model_loaded = load_func('/CPU:0')
                if is_model_loaded:
                    return model, is_model_loaded, '/CPU:0'

            return None, False, None

        def fetch_model_info(model_key):
            """Fetch model information from the YAML configuration."""
            if cls.config is None:
                raise ValueError("Configuration not loaded. Please call `load_config` first.")
            model_config = cls.config.get('models', {}).get(model_key, {})
            if not model_config:
                raise KeyError(f"Configuration for {model_key} not found in the YAML file.")
            return model_config.get('model_name'), model_config.get('model_blob_path')
        
        cls.overwrite = overwrite
        available_gpus = [torch.device(f"cuda:{i}") for i in
                          range(torch.cuda.device_count())] if torch.cuda.is_available() else []
        logger.info(f"Available GPUs: {available_gpus}")


        # Load NER model
        if load_ner and not cls.ner_model_loaded:
            ner_model_name, ner_model_blob_path = fetch_model_info('ner_model')
            cls.ner_model, cls.ner_model_loaded, _ = load_model_on_device(
                lambda x: cls.load_model_from_blob(logger,
                                                   ner_model_name,
                                                   ner_model_blob_path,
                                                   cls.ner_model_loaded,
                                                   x),
                "NER model",
                "ner_model"
            )

        # Load SLM model
        if load_slm:
            slm_name, slm_path = fetch_model_info('slm_model')
            load_model_on_device(cls.load_slm, "SLM model", "slm_model")

        # Load YOLOv8 for checkbox detection
        if load_yolo_v8_for_checkbox:
            # cls.load_yolo_v8_for_checkbox(logger)
            # if torch.cuda.is_available():
            #     cls.yolo_v8_for_checkbox.model.to(torch.device("cuda:0"))
            # cls.yolo_v8_for_checkbox_loaded = True

            yolo_v8_for_checkbox_name, yolo_v8_for_checkbox_blob_path = fetch_model_info('yolo_v8_for_checkbox')

            cls.yolo_v8_for_checkbox, cls.yolo_v8_for_checkbox_loaded, _ = load_model_on_device(
                   lambda x: cls.load_model_from_blob(logger,
                                                      yolo_v8_for_checkbox_name,
                                                      yolo_v8_for_checkbox_blob_path,
                                                      cls.yolo_v8_for_checkbox_loaded,
                                                      x),
                   "YOLOv8 for checkbox model",
                   "yolov8_for_checkbox_model"
            )

        # Load YOLOv8 for lbsd detection
        if load_yolo_v8_for_lbsd_model:
            yolo_v8_for_lbsd_name, yolo_v8_for_lbsd_blob_path = fetch_model_info('yolo_v8_for_lbsd_model')
            cls.yolo_v8_for_lbsd_model, cls.yolo_v8_for_lbsd_model_loaded, _ = load_model_on_device(
                   lambda x: cls.load_model_from_blob(logger,
                                                      yolo_v8_for_lbsd_name,
                                                      yolo_v8_for_lbsd_blob_path,
                                                      cls.yolo_v8_for_lbsd_model_loaded,
                                                      x),
                   "YOLOv8 for lbsd model",
                   "yolov8_for_lbsd_model"
            )

        if image_matching_model:
            image_matching_model_name, image_matching_model_name_blob_path = fetch_model_info('image_matching_model')
            cls.image_matching_model, cls.image_matching_model_loaded, _ = load_model_on_device_tf2(
                    lambda x: cls.load_model_from_blob(logger,
                                                       image_matching_model_name,
                                                       image_matching_model_name_blob_path,
                                                       cls.image_matching_model_loaded,
                                                       x),
                    "ImageMatching model",
                    "image_matching_model"
            )

        # Load subdivision model
        if load_subdivision_model and not cls.subdivision_model_loaded:
            subdivision_model_name, subdivision_blob_path = fetch_model_info('subdivision_model')
            cls.subdivision_model, cls.subdivision_model_loaded, _ = load_model_on_device(
                                        lambda x: cls.load_model_from_blob(logger,
                                                                         subdivision_model_name,
                                                                         subdivision_blob_path,
                                                                         cls.subdivision_model_loaded,
                                                                         x),
                                        "Subdivision model",
                                        "subdivision_model"
                                )


    @classmethod
    def load_slm(cls):
        local_model_dir = "microsoft/Phi-3-mini-128k-instruct"
        if cls.slm_tokenizer is None:
            cls.slm_tokenizer = AutoTokenizer.from_pretrained(local_model_dir, padding_side="left")
        if cls.slm_model is None:
            cls.slm_model = AutoModelForCausalLM.from_pretrained(local_model_dir, device_map="auto",
                                                                 attn_implementation='eager', trust_remote_code=True,
                                                                 torch_dtype=torch.bfloat16)
        if cls.slm_generate_text is None:
            cls.slm_generate_text = InstructionTextGenerationPipeline(model=cls.slm_model, tokenizer=cls.slm_tokenizer)

        cls.slm_model_loaded = True

    @classmethod
    def unload_slm(cls):
        cls.slm_tokenizer = None
        cls.slm_model = None
        cls.slm_generate_text = None
        cls.slm_model_loaded = False

    @classmethod
    def load_yolo_v8_for_checkbox(cls, logger):
        if cls.yolo_v8_for_checkbox is None:
            cls.yolo_v8_for_checkbox = YOLOv8ForCheckBox(logger)

    @classmethod
    def parse_filename(cls, filename, suffix):
        match = re.match(suffix + r"-(\d{4}\.\d{1,2}\.\d{1,2}\.\d+).zip", filename)
        if match:
            return match.group(1)
        return None

    @classmethod
    def compare_versions(cls, version1, version2):
        v1 = list(map(int, version1.split('.')))
        v2 = list(map(int, version2.split('.')))
        return (v1 > v2) - (v1 < v2)

    @classmethod
    def load_model_from_blob(cls, logger, model_name, blob_model_path, is_model_loaded, device) -> object:
        """
        Load a model from a blob or cache, and ensure the correct version is loaded based on the naming pattern.

        Args:
            logger: Logger for logging information.
            model_name: Name of the model, used as the base name for cache or blob storage.
            blob_model_path: Path in blob storage to locate the model.

        Returns:
            The loaded model object.
        """
        if is_model_loaded:
            logger.info(f"{model_name} is already loaded.")
            return

        model_dir = CACHE_DIR / model_name
        latest_local_version_dir = None

        model = None

        # Try to find the latest model version locally first
        if model_dir.exists():
            logger.info(f"Searching for cached model in {model_dir}")
            latest_local_version_dir = cls._find_model_version(model_dir, model_name)

        try:
            model_version_dir = None
            if cls.overwrite:
                model_version_dir = cls.download_and_unzip_model_from_blob(logger, model_name, blob_model_path)

            if cls.overwrite and model_version_dir is None and latest_local_version_dir:
                logger.info(
                    f"Cached version {latest_local_version_dir} is up-to-date or newer than blob version.\
                     Using cached model.")
                logger.info(f"No new version found in blob. Using cached model: {latest_local_version_dir}")
                model = cls.load_model_class_from_config(latest_local_version_dir, logger, device)
            elif cls.overwrite and model_version_dir:
                logger.info(f"Newer version found in blob storage: {model_version_dir}. Downloading...")
                model = cls.load_model_class_from_config(model_version_dir, logger, device)
            else:
                logger.warning(f"No cached model found locally and unable to access blob storage.")
                raise FileNotFoundError(f"No valid model version found locally for {model_name}.")
        except Exception as e:
            logger.error(f"Error accessing blob storage: {e}.")
            # Fall back to local version in case of connection issues or blob access failure
            if latest_local_version_dir:
                logger.info(f"Falling back to cached model version: {latest_local_version_dir}")
                model = cls.load_model_class_from_config(latest_local_version_dir, logger, device)
            else:
                raise FileNotFoundError(
                    f"Unable to access blob storage and no valid local model found for {model_name}.")

        if model is not None:
            is_model_loaded = True

        return is_model_loaded, model

    @classmethod
    def _find_model_version(cls, model_dir, model_name):
        """
        Find the correct model version directory based on the naming pattern.

        Args:
            model_dir: The directory where models are cached.
            model_name: The base name of the model.

        Returns:
            Path to the correct model version directory, or None if no valid version is found.
        """
        version_pattern = re.compile(rf"{model_name}-(\d{{4}}\.\d{{1,2}}\.\d{{1,2}}\.\d+)")

        # Find all directories that match the version pattern
        matching_dirs = []
        for entry in os.listdir(model_dir):
            entry_path = model_dir / entry
            if entry_path.is_dir() and version_pattern.match(entry):
                matching_dirs.append(entry_path)

        if matching_dirs:
            # Sort matching directories based on version numbers (in reverse, to pick the latest)
            matching_dirs.sort(key=lambda d: version_pattern.search(d.name).group(1), reverse=True)
            return matching_dirs[0]  # Return the latest version directory
        else:
            return None

    @classmethod
    def check_blob_for_new_version(cls, logger, model_name, model_path):
        """
        Check if there is a newer version of the model in blob storage.

        Args:
            logger: Logger for logging information.
            model_name: Name of the model, used as the base name for blob storage.
            model_path: Path in blob storage to locate the model.

        Returns:
            The path to the new model version in blob storage, or None if no new version is found.
        """
        logger.info(f"Checking blob storage for new version of {model_name}...")

        blob_service_client = BlobServiceClient.from_connection_string(azure_storage_connection_string)
        container_client = blob_service_client.get_container_client(model_blob_container_name)

        # List blobs and check if a new version is available
        blobs = container_client.list_blobs(name_starts_with=model_path)
        version_pattern = re.compile(rf"{model_name}-(\d{{4}}\.\d{{1,2}}\.\d{{1,2}}\.\d+)")

        latest_blob_version = None
        for blob in blobs:
            if version_pattern.match(blob.name):
                latest_blob_version = blob.name  # You can further refine this to find the latest version

        if latest_blob_version:
            return latest_blob_version
        else:
            logger.info(f"No new version found in blob storage for {model_name}.")
            return None

    @classmethod
    def get_latest_model_path(cls, parent_dir, model_name):
        # Regular expression to capture the version numbers in the format "model_name-2024.9.8"
        version_pattern = re.compile(rf"{model_name}-(\d{{4}})\.(\d{{1,2}})\.(\d{{1,2}})\.(\d+)")

        # Create a list to store the valid model versions found in the directory
        versions = []

        # Iterate through the directory and find all directories or files that match the model name and version pattern
        for item in parent_dir.iterdir():
            match = version_pattern.match(item.name)
            if match:
                # Extract version components and store them with the file/directory path
                version_tuple = tuple(map(int, match.groups()))  # Convert version components to integers for sorting
                versions.append((version_tuple, item))

        if not versions:
            print(f"No versions found for {model_name} in {parent_dir}")
            return

        # Sort the versions based on the version number (latest version will be the last in the sorted list)
        versions.sort()

        # The latest version is the last one in the sorted list
        latest_version = versions[-1]
        print(f"latest version: {latest_version[1].name}")

        return parent_dir / latest_version[1].name

    @classmethod
    def download_and_unzip_model_from_blob(cls, logger, model_name, blob_model_path):
        model_dir = CACHE_DIR / model_name

        # Ensure the cache directory exists
        model_dir.mkdir(parents=True, exist_ok=True)

        # Initialize Blob service client and access the container where models are stored
        blob_service_client = BlobServiceClient.from_connection_string(azure_storage_connection_string)
        container_client = blob_service_client.get_container_client(model_blob_container_name)
        
        # List all blobs (files) that match the given model path in blob storage
        blobs = container_client.list_blobs(name_starts_with=model_name)
        blob_names = [blob.name for blob in blobs]
        
        # Filter out only the zip files containing the models
        zip_files = [name for name in blob_names if name.lower().endswith('.zip')]

        # Initialize variables to keep track of the latest model version
        latest_zip = None
        latest_version = None

        # Loop through all zip files and extract the version using the model's naming convention
        for zip_file in zip_files:
            version = cls.parse_filename(Path(zip_file).name, model_name)  # Extract version from file name
            if version:
                # Compare versions and update the latest model information if the current version is newer
                if not latest_zip or cls.compare_versions(version, latest_version) > 0:
                    latest_zip = zip_file
                    latest_version = version

        # If no valid zip file is found in the blob storage, raise an exception
        if not latest_zip:
            raise Exception(f"No zip file found for {model_name} in Blob storage.")

        # Check if the cached version is up-to-date by comparing with the latest blob version
        cached_version_file = model_dir / 'version.txt'
        if cached_version_file.exists():
            with cached_version_file.open('r') as f:
                cached_version = f.read().strip()

            # If the cached version is the same or newer, use the cached model and skip download
            if cls.compare_versions(latest_version, cached_version) <= 0:
                logger.info(f"Using cached version {cached_version} for {model_name}")
                return None

        # If the latest version is newer, log the download process
        logger.info(f"Downloading latest version {latest_version} of {model_name}")

        # Create a temporary directory to store the downloaded zip file
        with tempfile.TemporaryDirectory() as tmpdir:
            zip_file_path = Path(tmpdir) / Path(latest_zip).name
            blob_client = container_client.get_blob_client(latest_zip)

            # Download the zip file from blob storage and save it to the temporary directory
            with zip_file_path.open("wb") as download_file:
                download_file.write(blob_client.download_blob().readall())

            # Define the directory where the unzipped model will be stored in the cache
            unzip_dir = model_dir

            # Unzip the downloaded model into the cache directory
            with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
                zip_ref.extractall(unzip_dir)

            # Save the latest version information to the version.txt file in the cache
            with (unzip_dir / 'version.txt').open('w') as version_file:
                version_file.write(latest_version)

            # If the model directory already exists, clear the older cached version
            if unzip_dir.exists():
                cls.delete_older_versions(logger, unzip_dir, model_name)
            # Return the directory where the model has been unzipped
            return cls.get_latest_model_path(unzip_dir, model_name)

    @classmethod
    def load_model_class_from_config(cls, model_dir, logger, device):
        config_file_path = model_dir / 'config.yml'

        if not config_file_path.exists():
            raise FileNotFoundError(f"config.yml not found in {model_dir}")

        with config_file_path.open('r') as config_file:
            config = yaml.safe_load(config_file)

        model_class = config.get('model_class')
        if not model_class:
            raise KeyError("No 'model_class' key found in config.yml")

        module = importlib.import_module(MODEL_MODULE)
        model_class = getattr(module, model_class)
        logger.info(f"Successfully loaded class {model_class} from {MODEL_MODULE}")

        return model_class.from_pretrained(model_dir, logger=logger, device=device)

    @classmethod
    def delete_older_versions(cls, logger, parent_dir, model_name):
        """
        Deletes older versions of the model from the given parent directory.

        Args:
            parent_dir (Path): The directory containing model versions.
            model_name (str): The base name of the model.

        This function deletes all older versions of the model, except for the latest version.
        """
        # Regular expression to capture the version numbers in the format "model_name-2024.9.8"
        version_pattern = re.compile(rf"{model_name}-(\d{{4}})\.(\d{{1,2}})\.(\d{{1,2}})\.(\d+)")

        # Create a list to store the valid model versions found in the directory
        versions = []

        # Iterate through the directory and find all directories or files that match the model name and version pattern
        for item in parent_dir.iterdir():
            match = version_pattern.match(item.name)
            if match:
                # Extract version components and store them with the file/directory path
                version_tuple = tuple(map(int, match.groups()))  # Convert version components to integers for sorting
                versions.append((version_tuple, item))

        if not versions:
            print(f"No versions found for {model_name} in {parent_dir}")
            return

        # Sort the versions based on the version number (latest version will be the last in the sorted list)
        versions.sort()

        # The latest version is the last one in the sorted list
        latest_version = versions[-1]
        print(f"Keeping the latest version: {latest_version[1].name}")

        # Delete all other versions except for the latest
        for version, item in versions[:-1]:
            try:
                if item.is_file():
                    item.unlink()
                    print(f"Deleted file: {item}")
                elif item.is_dir():
                    for sub_item in item.iterdir():
                        if sub_item.is_file():
                            sub_item.unlink()
                        elif sub_item.is_dir():
                            sub_item.rmdir()
                    item.rmdir()
                    print(f"Deleted directory: {item}")
            except Exception as e:
                print(f"Error deleting {item}: {e}")