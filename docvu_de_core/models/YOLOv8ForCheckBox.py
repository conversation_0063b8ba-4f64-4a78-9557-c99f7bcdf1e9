from docvu_de_core import yolo_model_path
from ultralytics import YOL<PERSON>
from matplotlib import pyplot as plt
import cv2


class YOLOv8ForCheckBox:
    def __init__(self, logger):
        self.logger = logger
        self.model = self.load_model()
        self.checkbox_class = [1]

    def load_model(self):
        model = YOLO(yolo_model_path)
        self.logger.info("YOLO model loaded")
        print("YOLO model loaded")
        return model

    def predict(self, image_path, output_path=None):
        colors = [
            (0, 255, 0),
            (0, 0, 255),
            (255, 0, 0),
            (255, 255, 0),
            (0, 255, 255),
            (255, 0, 255),
            (128, 128, 128),
            (255, 255, 255),
        ]

        results = self.model([image_path])  # Run batched inference on a list of images

        orig_image = results[0].orig_img  # Get original image
        bboxes = results[0].boxes  # Get bounding boxes

        for bbox in bboxes:  # Draw bounding boxes
            klass = int(bbox.cls.item())
            conf = bbox.conf
            xyxy = bbox.xyxy

            top = (int(xyxy[0][0]), int(xyxy[0][1]))
            bottom = (int(xyxy[0][2]), int(xyxy[0][3]))
            orig_image = cv2.rectangle(orig_image, top, bottom, colors[klass], 4)

            # Add confidence score behind the bounding box
            label = f"Conf: {conf.item():.2f}"
            text_color = (244, 67, 54)
            orig_image = cv2.putText(orig_image, label, (top[0] - 120, top[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.7,
                                     text_color, 3)

        # Save the image if output path is provided
        if output_path:
            plt.imsave(output_path, orig_image)

        return results

    def get_checked_boxes(self, image_path, output_path=None):
        checkboxes = []
        results = self.predict(image_path, output_path=output_path)

        for bbox in results[0].boxes:
            bbox_dict = {
                "bbox": {
                    "x1": int(bbox.xyxy[0][0]),
                    "y1": int(bbox.xyxy[0][1]),
                    "x2": int(bbox.xyxy[0][2]),
                    "y2": int(bbox.xyxy[0][3])
                },
                "confidence": bbox.conf.item(),
                "class_id": int(bbox.cls.item()),
                "checked": int(bbox.cls.item()) in self.checkbox_class
            }
            checkboxes.append(bbox_dict)

        checkboxes = sorted(checkboxes, key=lambda x: (x['bbox']['x1'], x['bbox']['y1']))
        return checkboxes
