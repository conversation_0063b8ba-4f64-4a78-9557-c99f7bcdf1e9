{"document_type": "closing_disclosure", "extraction_rules": {"closing_info": {"fields": [{"name": "date_issued", "search_patterns": ["Date Issued", "01/29/2025"], "extraction_type": "date_pattern", "required": true}, {"name": "closing_date", "search_patterns": ["Closing Date"], "extraction_type": "date_pattern", "required": true}, {"name": "disbursement_date", "search_patterns": ["Disbursement Date"], "extraction_type": "date_pattern", "required": true}]}, "borrower_info": {"fields": [{"name": "borrower_name", "search_patterns": ["<PERSON><PERSON><PERSON>", "THOMAS GRAY JR"], "extraction_type": "text_after_pattern", "required": true}]}, "property_info": {"fields": [{"name": "property_address", "search_patterns": ["2820 HARVARD BOULEVARD", "Property"], "extraction_type": "address_pattern", "required": true}, {"name": "sale_price", "search_patterns": ["Sale Price", "$200,000"], "extraction_type": "currency_pattern", "required": true}]}, "loan_terms": {"fields": [{"name": "loan_amount", "search_patterns": ["<PERSON><PERSON>", "$196,377"], "extraction_type": "currency_pattern", "required": true}, {"name": "interest_rate", "search_patterns": ["Interest Rate", "7.625%"], "extraction_type": "percentage_pattern", "required": true}, {"name": "loan_term", "search_patterns": ["<PERSON>an <PERSON>", "30 years"], "extraction_type": "text_after_pattern", "required": true}, {"name": "loan_purpose", "search_patterns": ["Purpose", "Purchase"], "extraction_type": "text_after_pattern", "required": true}, {"name": "loan_product", "search_patterns": ["Product", "FIXED RATE"], "extraction_type": "text_after_pattern", "required": true}]}, "monthly_payment": {"fields": [{"name": "principal_interest", "search_patterns": ["Monthly Principal & Interest", "$1,389.94"], "extraction_type": "currency_pattern", "required": true}, {"name": "mortgage_insurance", "search_patterns": ["Mortgage Insurance", "88.10"], "extraction_type": "currency_pattern", "required": false}, {"name": "estimated_escrow", "search_patterns": ["Estimated Escrow", "462.11"], "extraction_type": "currency_pattern", "required": false}, {"name": "estimated_total_monthly", "search_patterns": ["Estimated Total", "$1,940.15"], "extraction_type": "currency_pattern", "required": true}]}, "closing_costs": {"fields": [{"name": "total_closing_costs", "search_patterns": ["Closing Costs", "$18,681.44"], "extraction_type": "currency_pattern", "required": true}, {"name": "cash_to_close", "search_patterns": ["Cash to Close"], "extraction_type": "currency_pattern", "required": true}]}, "lender_info": {"fields": [{"name": "lender_name", "search_patterns": ["<PERSON><PERSON>", "AMERICAN FINANCIAL NETWORK INC"], "extraction_type": "text_after_pattern", "required": true}, {"name": "loan_id", "search_patterns": ["Loan ID #", "26100062700"], "extraction_type": "pattern_match", "pattern": "\\d{8,}", "required": true}]}}, "validation_rules": {"required_fields": ["date_issued", "borrower_name", "property_address", "loan_amount", "interest_rate", "total_closing_costs"], "field_validations": {"interest_rate": {"pattern": "\\d+\\.\\d+%?", "error_message": "Invalid interest rate format"}, "loan_amount": {"min_value": 1000, "error_message": "Loan amount must be at least $1,000"}}}}