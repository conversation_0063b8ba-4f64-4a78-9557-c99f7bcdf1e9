[{"id": 80017404, "key": ["BORROWER NAME"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Assessment Summary", "APPRAISAL IDENTIFIER"], "start_identifier": [""], "field_id": 80017404, "field_name": "Borrower Name", "document_id": 9000593, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*[0-9\\-]+$", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 8001704, "key": ["LOAN APPLICATION NUMBER"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["DOCUMENTATION LEVEL"], "start_identifier": [""], "field_id": 8001704, "field_name": "Loan Number", "document_id": 9000593, "alternate_locations": [{"id": 8001704, "key": ["LENDER LOAN NUMBER"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PROGRAM IDENTIFIER(S)"], "start_identifier": [""], "field_id": 8001704, "field_name": "Loan Number", "document_id": 9000593}]}, {"id": 80017532, "key": ["PROPERTY ADDRESS"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PROGRAM IDENTIFIER(S)", "results"], "start_identifier": [""], "field_id": 80017532, "field_name": "Subject Property", "document_id": 9000593, "output_format": {"address_parser_output_format": {"get_line1_and_line2": true, "from_field": "Subject Property"}}}, {"id": 80017532, "key": ["PROPERTY ADDRESS"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PROGRAM IDENTIFIER(S)", "results"], "start_identifier": [""], "field_id": 80017532, "field_name": "city", "document_id": 9000593, "output_format": {"address_parser_output_format": {"get_city": true, "from_field": "city"}}}, {"id": 80017532, "key": ["PROPERTY ADDRESS"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PROGRAM IDENTIFIER(S)", "results"], "start_identifier": [""], "field_id": 80017532, "field_name": "State", "document_id": 9000593, "output_format": {"address_parser_output_format": {"get_state": true, "from_field": "State"}}}, {"id": 80017532, "key": ["PROPERTY ADDRESS"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PROGRAM IDENTIFIER(S)", "results"], "start_identifier": [""], "field_id": 80017532, "field_name": "Zip_code", "document_id": 9000593, "output_format": {"address_parser_output_format": {"get_zip_code": true, "from_field": "Zip_code"}}}, {"id": 8007404, "key": ["NUMBER OF UNITS"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["NEW CONSTRUCTION"], "start_identifier": [""], "field_id": 8007404, "field_name": "Number of Units", "document_id": 9000593, "alternate_locations": [{"id": 8007404, "key": ["NUMBER OF UNITS"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TEMPORARY SUBSIDY BUYDOWN"], "start_identifier": [""], "field_id": 8007404, "field_name": "Number of Units", "document_id": 9000593}]}, {"id": 80017558, "key": ["OCCUPANCY", "INTENDED USE OF PROPERTY"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["SALES CONCESSIONS", "CASH OUT AMOUNT"], "start_identifier": [""], "field_id": 80017558, "field_name": "Occupancy", "document_id": 9000593, "alternate_locations": [{"id": 80017558, "key": ["OCCUPANCY"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["SALES CONCESSIONS"], "start_identifier": [""], "field_id": 80017558, "field_name": "Occupancy", "document_id": 9000593}]}, {"id": 80017523, "key": ["PURCHASE ELIGIBILITY", "Purchase eligibility"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER NAME"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017523, "field_name": "Purchase eligibility", "document_id": 9000593, "alternate_locations": [{"id": 80017523, "key": ["PURCHASE ELIGIBILITY", "Purchase eligibility"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ASSET R&W*"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017523, "field_name": "Purchase eligibility", "document_id": 9000593}]}, {"id": 80017524, "key": ["RISK CLASS", "Risk class"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER NAME"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017524, "field_name": "Risk class", "document_id": 9000593, "alternate_locations": [{"id": 80017524, "key": ["RISK CLASS", "Risk class"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ASSET R&W*"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017524, "field_name": "Risk class", "document_id": 9000593}]}, {"id": 80017552, "key": ["PROPERTY TYPE"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["INTEREST RATE BUYDOWN", "AFFORDABLE PRODUCT TYPE"], "start_identifier": [""], "field_id": 80017552, "field_name": "Property type", "document_id": 9000593}, {"id": 80017556, "key": ["LOAN PURPOSE"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PROPERTY TYPE"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017556, "field_name": "Purpose of <PERSON>an", "document_id": 9000593, "alternate_locations": [{"id": 80017556, "key": ["Purpose of <PERSON>an"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ESTIMATED VALUE"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017556, "field_name": "Purpose of <PERSON>an", "document_id": 9000593}], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$", "^\\$"], "remove_from_end": ["\\s.*", "\\n.*", "\\..*"], "keep_only_alpha_numeric": true, "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 80017562, "key": ["Purchase price"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["OCCUPANCY", "APPRAISED VALUE OF PROPERTY"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017562, "field_name": "Purchase price", "document_id": 9000593, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 80017551, "key": ["Loan amount"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["APPRAISED PROPERTY VALUE", "PROPERTY TYPE"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017551, "field_name": "Loan amount", "document_id": 9000593, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 80017550, "key": ["Interest rate"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["NUMBER OF UNITS", "LOAN AMOUNT"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017550, "field_name": "Interest rate", "document_id": 9000593, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 80017549, "key": ["Amortization months"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ESTIMATED PROPERTY VALUE", "PURCHASE PRICE"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017549, "field_name": "Amortization_months", "document_id": 9000593}, {"id": 80017548, "key": ["Amortization type"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PURCHASE PRICE", "PURPOSE OF LOAN"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017548, "field_name": "Amortization type", "document_id": 9000593}, {"id": 80017547, "key": ["PRODUCT TYPE", "Product type"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["LOAN PURPOSE"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017547, "field_name": "Product type", "document_id": 9000593, "alternate_locations": [{"id": 80017547, "key": ["PRODUCT TYPE", "Product type"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Interest rate"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017547, "field_name": "Product type", "document_id": 9000593}]}, {"id": 80017560, "key": ["Subordinate amount"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PURPOSE OF REFINANCE"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017560, "field_name": "Subordinate_amount_second_mortgage", "document_id": 9000593, "alternate_locations": [{"id": 80017560, "key": ["TOTAL SUBORDINATE FINANCING"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Asset Details"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017560, "field_name": "Subordinate_amount_second_mortgage", "document_id": 9000593}], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 201, "key": ["PROGRAM IDENTIFIER(S)", "Loan-to-<PERSON> Ratios"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["SELECTED BORROWER"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 201, "field_name": "Qualifying_Ratios_save", "document_id": 9000593}, {"id": 80017579, "key": ["qualifying ratios"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["credit report information", "SELECTED BORROWER"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017579, "field_name": "Front_end_housing_ratio", "document_id": 9000593, "alternate_locations": [{"id": 80017579, "key": ["HOUSING RATIO"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["OCCUPANT DEBT RATIO"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017579, "field_name": "Front_end_housing_ratio", "document_id": 9000593}]}, {"id": 80017579, "key": ["HOUSING RATIO DEBT"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["SELECTED REPOSITORY"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017579, "field_name": "Back_end_Debt_ratio", "document_id": 9000593, "alternate_locations": [{"id": 80017579, "key": ["DEBT RATIO"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["MAX MORTGAGE"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017579, "field_name": "Back_end_Debt_ratio", "document_id": 9000593}]}, {"id": 80017574, "key": ["LTV"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PROPOSED HOUSING (PITI)"], "start_identifier": ["LTV"], "possible_page_numbers": [], "field_id": 80017574, "field_name": "LTV", "document_id": 9000593, "alternate_locations": [{"id": 80017574, "key": ["LTV"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Information"], "start_identifier": ["LTV"], "possible_page_numbers": [], "field_id": 80017574, "field_name": "LTV", "document_id": 9000593}], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "extract_pattern": "^\\d+\\.\\d+"}}}, {"id": 80017575, "key": ["TLTV"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PROPOSED HOUSING (PITI)"], "start_identifier": [""], "field_id": 80017575, "field_name": "TLTV", "document_id": 9000593, "alternate_locations": [{"id": 80017575, "key": ["TLTV"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Information"], "start_identifier": [""], "field_id": 80017575, "field_name": "TLTV", "document_id": 9000593}], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 80017576, "key": ["HTLTV", " MTV"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PROPOSED HOUSING (PITI)"], "start_identifier": [""], "field_id": 80017576, "field_name": "HTLTV", "document_id": 9000593, "alternate_locations": [{"id": 80017576, "key": ["HTLTV", " MTV"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Information"], "start_identifier": [""], "field_id": 80017576, "field_name": "HTLTV", "document_id": 9000593}], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 80017533, "key": ["MORTGAGE TYPE", "Mortgage type"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PROGRAM IDENTIFIER(S)"], "start_identifier": [""], "field_id": 80017533, "field_name": "loan_program_Mortgage_type", "document_id": 9000593}, {"id": 80017561, "key": ["REFINANCE TYPE"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ARM QUALIFYING RATE"], "start_identifier": [""], "field_id": 80017561, "field_name": "loan_type_Purpose_of_Refinance", "document_id": 9000593, "alternate_locations": [{"id": 80017561, "key": ["PURPOSE OF REFINANCE"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ARM QUALIFYING RATE"], "start_identifier": [""], "field_id": 80017561, "field_name": "loan_type_Purpose_of_Refinance", "document_id": 9000593}]}, {"id": 80017562, "key": ["PURCHASE PRICE"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["OCCUPANCY"], "start_identifier": [""], "field_id": 80017562, "field_name": "sale_price_Purchase_price", "document_id": 9000593, "alternate_locations": [{"id": 80017562, "key": ["PURCHASE PRICE"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["APPRAISED VALUE"], "start_identifier": [""], "field_id": 80017562, "field_name": "sale_price_Purchase_price", "document_id": 9000593}], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 80017529, "key": ["APPRAISED PROPERTY VALUE"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["CASH OUT AMOUNT"], "start_identifier": [""], "field_id": 80017529, "field_name": "Appraisal_value_of_property", "document_id": 9000593, "alternate_locations": [{"id": 80017529, "key": ["APPRAISED VALUE OF PROPERTY"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["NUMBER OF UNITS"], "start_identifier": [""], "field_id": 80017529, "field_name": "Appraisal_value_of_property", "document_id": 9000593}], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*[a-zA-Z\\s]+$", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}]