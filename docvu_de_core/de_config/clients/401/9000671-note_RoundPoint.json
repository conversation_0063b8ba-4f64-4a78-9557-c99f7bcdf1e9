[{"id": 80022595, "key": ["Note"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER'S PROMISE TO PAY", "<PERSON><PERSON><PERSON>'s Promise"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022595, "field_name": "note_signed_date", "document_id": 9000671, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]