[{"id": 3005897, "key": ["signed and sworn to", "sworn to", "Subscribed and sworn to", "Witness my hand and official seal", "subscribed before"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3005897, "field_name": "signature_name_affidavit_printed_commission_expires_date", "document_id": 9000882, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000435, "key": ["Commission", "Expires", "who did not take an oath", "presence of"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000435, "field_name": "signature_and_name_affidavit_notary_seal", "document_id": 9000882, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3000425, "key": ["loan documents", "as the name", "TYPED BELOW", "(Print or Type Name)", "other names", "correct signature", "same person", "printed variation", "I swear"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Be it remembered", "File No:"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000425, "field_name": "signature_and_name_affidavit_borrower_sign", "document_id": 9000882, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000426, "key": ["referenced property", "loan documents", "as the name", "TYPED BELOW", "(Print or Type Name)"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["State of"], "start_identifier": [""], "field_id": 3000426, "field_name": "signature_and_name_affidavit_borrower_date", "document_id": 9000882, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000430, "key": ["on this day", "personally appeared"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["who, after being", "who. after being", "(\"Affiant\")"], "start_identifier": ["personally appeared"], "possible_page_numbers": [], "field_id": 3000430, "field_name": "signature_and_name_affidavit_appeared_before", "document_id": 9000882}, {"id": 3000431, "key": ["State of", "STATE OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [", do hereby ", "County ss:", "Ltd//"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000431, "field_name": "signature_and_name_affidavit_state_filed", "document_id": 9000882}, {"id": 3000432, "key": ["County of", "Countyof", "CO&nty of", "COUNTY OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000432, "field_name": "signature_and_name_affidavit_county", "document_id": 9000882}, {"id": 3000433, "key": ["notarization, this", "acknowledged before me", "before me on this", "before me this", "seal this"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["by", "My Commission Expires:"], "start_identifier": [""], "field_id": 3000433, "field_name": "signature_and_name_affidavit_execution_date", "document_id": 9000882}, {"id": 3000434, "key": ["Notary Public for", "Notary Public in and for", "(Notary Public)", "Notary Public", "Notary Public Signature", "Notary Sign", "Signature M person taking acknowledgement", "(Notary Si ature)"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000434, "field_name": "signature_and_name_affidavit_notary_acknowledgment", "document_id": 9000882, "post_process": {"check_for_signature_post_processor": {"signature_present": true, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 70}}}, {"id": 3000437, "key": ["commission expires", "Commission", "My Comm. Expires", "Expires", "My Commission Expires:"], "direction": "inline", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000437, "field_name": "signature_and_name_affidavit_commission_expires_date_validation", "document_id": 9000882}]