[{"id": 3000915, "key": ["Agency Case No."], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Uniform Residential Loan Application"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000915, "field_name": "urla_agency_case_number", "document_id": 9000001}, {"id": 3000913, "key": ["Loan Identifier"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "Agency Case", "|", "I"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000913, "field_name": "urla_loan_number", "document_id": 9000001}, {"id": 101010, "key": ["borrower information"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 101010, "field_name": "dummy_section1_1", "document_id": 9000001}, {"id": 3000011, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000011, "field_name": "urla_borrower_name", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004736, "key": ["Date of Birth"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Permanent Resident Alien", "Non-Permanent Resident Alien", "type of credit"], "start_identifier": ["U. S. Citizen"], "field_id": 3004736, "field_name": "Date of birth", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004735, "key": ["Borrower information"], "direction": "right", "type": "number", "return_type": "text", "multi_line_value": true, "start_identifier": ["Social Security Number"], "end_identifier": ["Individual Taxpayer", "Date of Birth"], "field_id": 3004735, "field_name": "Social Security Number  - 3004735", "document_id": 9000001, "output_format": {"number_parser_output_format": {"extract_multiple": false, "pattern_keys": ["ssn"]}}, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3000004, "key": ["Total Number of Borrowers"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Each Borrower intends"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000004, "field_name": "Number of Borrowers", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_end": ["\\D*"], "remove_special_chars_from_end": true, "retain_only_numbers": true}}, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 1002, "key": ["Section 1"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 1002, "field_name": "Borrower_Section1_omr_fields", "sub_keys": ["PC_Section1_omr_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000821, "key": ["Section 1"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000821, "field_name": "Borrower_Section1_omr_fields", "sub_keys": ["UW_Section1_omr_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 1003, "key": ["Loan and Property Information"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 1003, "field_name": "dummy_section4_4", "document_id": 9000001}, {"id": 10003, "key": ["Section 4"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Creditor Name"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 10003, "field_name": "Loan_Purpose_checkbox_save_section", "sub_keys": ["PC_Section4_loan_property_information"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 106, "key": ["section 6"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 106, "field_name": "dummy_section6_6", "document_id": 9000001}, {"id": 3000006, "key": ["Borrower Signature"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Date"], "start_identifier": [""], "field_id": 3000006, "field_name": "urla_borrower_sign", "document_id": 9000001, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000007, "key": ["is approved", "Borrower Signature"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Additional Borrower Signature", "Borrower Name"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000007, "field_name": "urla_borrower_date", "document_id": 9000001}, {"id": 3000009, "key": ["Additional Borrower Signature"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Date"], "start_identifier": [""], "field_id": 3000009, "field_name": "urla_coborrower_sign", "document_id": 9000001, "post_process": {"check_for_signature_post_processor": {"sign_bbox_direction": "right", "vertical_threshold": 60, "horizontal_threshold": 0, "signature_present": true}}}, {"id": 3000010, "key": ["Additional Borrower Signature"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name", "Continuation Sheet"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000010, "field_name": "urla_coborrower_date", "document_id": 9000001}, {"id": 1017, "key": ["Section 9"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 1017, "field_name": "dummy_section9_9", "document_id": 9000001}, {"id": 920, "key": ["section 9"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 920, "field_name": "Loan_Originator_Information_save", "sub_keys": ["PC_Loan_originator_information"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 107, "key": ["Citizenship"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 107, "field_name": "dummy_section1_11", "document_id": 9000001}, {"id": 3000012, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000012, "field_name": "urla_coborrower_name", "additional_info": {"search_dummy_found_page_only": true}, "document_id": 9000001}, {"id": 3005717, "key": ["Date of Birth"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Permanent Resident Alien", "Non-Permanent Resident Alien", "type of credit"], "start_identifier": ["U. S. Citizen"], "field_id": 3005717, "field_name": "Co-Borrower Date of birth - 3005717", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3005716, "key": ["Borrower information", "Additional <PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Individual Taxpayer", "Date of Birth"], "start_identifier": ["Social Security Number"], "field_id": 3005716, "field_name": "Co-Borrower Social Security Number - 3005716", "document_id": 9000001, "output_format": {"number_parser_output_format": {"extract_multiple": false, "pattern_keys": ["ssn"]}}, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 101014, "key": ["Name", "Social Security Number"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 101014, "field_name": "<PERSON><PERSON><PERSON><PERSON>er_Section1_omr_fields", "sub_keys": ["UW_Co_borrower_Section1_omr_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 101016, "key": ["Original Cost"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Title to the Property Will"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 101016, "field_name": "UW_property_save_section", "sub_keys": ["UW_project_type_omr"], "document_id": 9000001}]