{"max_upper_block": 3, "use_upper_split_percentage": 0.45, "max_lines_for_header": 5, "max_upper_lines_for_key_search": 8, "debug": true, "default_return": "9000089-AUS_UnderWritter_DocVuDemo.json", "document_types": {"new_form": {"return": "9000089-DU_UnderWritter_DocVuDemo.json", "header": {"include_strings": ["VERIFICATION MESSAGES/APPROVAL CONDITIONS", "RISK/ELIGIBILITY"], "exclude_strings": ["Summary of Findings"], "length_comparison": false}, "body": {"include_strings": ["VERIFICATION MESSAGES/APPROVAL CONDITIONS", "RISK/ELIGIBILITY"], "exclude_strings": ["Summary of Findings"], "length_comparison": false}}}}