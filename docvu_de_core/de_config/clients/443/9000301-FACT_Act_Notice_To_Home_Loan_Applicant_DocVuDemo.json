[{"id": 3000446, "key": ["By sign"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000446, "field_name": "fact_act_notice_borrower_date", "document_id": 9000301}, {"id": 3000449, "key": ["By sign"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000449, "field_name": "fact_act_notice_coborrower_date", "document_id": 9000301, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 3000445, "key": ["Property Address", "Telephone Number"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000445, "field_name": "fact_act_notice_borrower_sign", "document_id": 9000301, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000448, "key": ["Property Address", "Telephone Number"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000448, "field_name": "fact_act_notice_coborrower_sign", "document_id": 9000301, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}]