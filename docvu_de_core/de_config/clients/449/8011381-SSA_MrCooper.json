[{"id": 1, "key": ["Master Servicer", "as Master Servicer", "Depositor", "as Depositor", "EXECUITON", "EXECUTION", "EXECUTION VERSION", "EXECUITON VERSION"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "max_page_to_search": 10, "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 1, "field_name": "Temporary_Field1", "document_id": 8011381}, {"id": 3006476, "key": ["EXECUITON", "EXECUTION", "EXECUTION VERSION", "EXECUITON VERSION"], "direction": "down_block", "type": "text", "return_type": "text", "use_match": "fuzzy", "use_prev_key_page_no": true, "exclude_header": true, "exclude_footer": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3006476, "field_name": "Complete_Title_From_Trust_Agreement_Cover_Page", "document_id": 8011381, "alternate_locations": [{"id": 3006476, "key": [""], "direction": "full_page", "type": "text", "return_type": "text", "include_key": true, "use_match": "fuzzy", "use_prev_key_page_no": true, "exclude_header": true, "exclude_footer": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3006476, "field_name": "Complete_Title_From_Trust_Agreement_Cover_Page", "document_id": 8011381}], "output_format": {"string_operations_output_format": {"prepend_custom_value": "SSA - "}}}, {"id": 3006487, "key": ["Dated"], "direction": "inline", "type": "date", "return_type": "date", "probable_place": "individual", "include_key": false, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006487, "field_name": "Effective_Date", "document_id": 8011381, "alternate_locations": [{"id": 3006487, "key": ["“Closing Date”:", "Closing Date:"], "direction": "inline", "type": "date", "return_type": "date", "include_key": false, "use_match": "fuzzy", "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006487, "field_name": "Effective_Date", "document_id": 8011381}]}, {"id": 2, "key": ["registered holders of"], "type": "text", "return_type": "text", "direction": "right", "multi_line_value": true, "end_identifier": ["Funds in the", "from which", "distributions"], "start_identifier": [""], "field_id": 2, "field_name": "Temporary_Deal_Name", "document_id": 8011381, "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true}}}, {"id": 3006475, "key": ["Master Servicer"], "direction": "down", "type": "text", "return_type": "text", "exclude_keys": ["Trustee"], "multi_line_value": true, "end_identifier": ["Issuing Entity"], "start_identifier": ["and"], "field_id": 3006475, "field_name": "Deal_Name", "document_id": 8011381, "output_format": {"copy_extraction_items_output_format": {"is_empty": true, "swap_with": "Temporary_Deal_Name"}}}, {"id": 3, "key": ["Article VIII"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "use_prev_field_page_no": true, "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3, "field_name": "Temporary_Article_IX_Starts_Field", "document_id": 8011381}, {"id": 4, "key": ["Merger or Consolidation of the Master Servicer", "Merger or Consolidation of Master Servicer", "Merger or Consolidation"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 10, "multi_line_value": true, "end_identifier": ["Section 6.02", "Resignation or Removal of Master Servicer", "Resignation of The Master Servicer", "Resignation of Master Servicer", "Section 9.05", "Assignment or Delegation of Duties"], "start_identifier": [""], "field_id": 4, "field_name": "Temporary_Merger_or_Consolidation", "document_id": 8011381, "sub_keys": ["mr_cooper_ssa_merge_consolidation_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3006601, "key": ["Resignation of The Master Servicer", "Resignation or Removal of the Master Servicer", "Resignation or Removal of Master Servicer", "Resignation of Master Servicer"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "exclude_keys": ["assignment of this Agreement and"], "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["SECTION 6.04.", "Assignment or Delegation of Duties", "Assignment or Delegation of Duties by the Master Servicer", "Section 9.07"], "start_identifier": [""], "field_id": 3006601, "field_name": "MS_Resignation_Language", "document_id": 8011381, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["termination or assignment of this Agreement and the resignation or removal of the Master Servicer."], "remove_special_chars_from_beginning": true}}}, {"id": 3006579, "key": ["Assignment or Delegation of Duties by the Master Servicer"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Limitation on Liability of the Master Servicer", "Section 9.08", "Indemnification"], "start_identifier": [""], "field_id": 3006579, "field_name": "Master_Servicer_Assignment_Language", "document_id": 8011381, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["Assignment or Delegation of Duties by the Master Servicer.", "Section 6.04. Assignment or Delegation of Duties by the Master Servicer"], "remove_special_chars_from_beginning": true}}}, {"id": 3006576, "key": ["Assign_Rights_to_Master_Servicing_Comp_language"], "direction": "right", "type": "text", "return_type": "text", "use_prev_field_page_no": true, "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006576, "field_name": "Assign_Rights_to_Master_Servicing_Comp_language", "document_id": 8011381, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Master_Servicer_Assignment_Language", "is_empty": true}}}, {"id": 13, "key": ["MISCELLANEOUS PROVISIONS"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "use_match": "exact_match", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 7, "multi_line_value": true, "end_identifier": ["Voting Rights", "Provision of Information"], "start_identifier": [""], "field_id": 13, "field_name": "Temporary_Amendment_Restrictions", "document_id": 8011381, "sub_keys": ["mr_cooper_ssa_amendment_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3006544, "key": ["SOX", "SSAE", "<PERSON><PERSON><PERSON>", "Statements on Standards for Attestation Engagements"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006544, "field_name": "Annual_SOX/SSAE_Due? if_so_yes_if_not_No", "document_id": 8011381, "output_format": {"string_operations_output_format": {"replace_with_custom_value": "Yes", "set_default_if_empty": "No"}}}, {"id": 3006523, "key": ["Entity_Due_Recoveries_if_Loan_is_Charged_Off"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006523, "field_name": "Entity_Due_Recoveries_if_Loan_is_Charged_Off", "document_id": 8011381, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Trust"}}}, {"id": 3006557, "key": ["Modifications-does_MS_have_to_purchase"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002472, "field_name": "Modifications - does_MS_have_to_purchase", "document_id": 8011381, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No"}}}, {"id": 3006558, "key": ["Modifications-does_MS_have_to_purchase_Language"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006558, "field_name": "Modifications - does_MS_have_to_purchase - Language", "document_id": 8011381, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3006559, "key": ["VA-buy_down_language_Trust"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006559, "field_name": "VA - buy_down_language (Trust)", "document_id": 8011381, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No Requirement"}}}, {"id": 3006560, "key": ["Master_Servicing_Other_Purchase_Options"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006560, "field_name": "Master_Servicing_Other_Purchase_Options", "document_id": 8011381, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3006563, "key": ["Any_Other_Requirements_Out_of_the_Ordinary"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006563, "field_name": "Any_Other_Requirements_Out_of_the_Ordinary", "document_id": 8011381, "output_format": {"string_operations_output_format": {"set_default_if_empty": "None"}}}, {"id": 3006584, "key": ["Additional_Info_Notes"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006584, "field_name": "Additional_Info_Notes", "document_id": 8011381, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3006634, "key": ["Termination_Notes"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006634, "field_name": "Termination_Notes", "document_id": 8011381, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3006633, "key": ["Who_Does_Master_Servicer_Inform_of_a_MS_EOD"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006633, "field_name": "Who_does_Master_Servicer_informs_of_MS_EOD", "document_id": 8011381, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}]