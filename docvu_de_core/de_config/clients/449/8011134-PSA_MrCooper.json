[{"id": 1, "key": ["Master Servicer", "as Master Servicer", "Depositor", "as Depositor", "EXECUITON", "EXECUTION", "EXECUTION VERSION", "EXECUITON VERSION"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "max_page_to_search": 10, "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 1, "field_name": "Temporary_Field1", "document_id": 8011134}, {"id": 3002053, "key": ["EXECUITON", "EXECUTION", "EXECUTION VERSION", "EXECUITON VERSION"], "direction": "down_block", "type": "text", "return_type": "text", "use_match": "fuzzy", "use_prev_key_page_no": true, "exclude_header": true, "exclude_footer": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002053, "field_name": "Complete_Title_From_Trust_Agreement_Cover_Page", "document_id": 8011134, "alternate_locations": [{"id": 3002053, "key": [""], "direction": "full_page", "type": "text", "return_type": "text", "include_key": true, "use_match": "fuzzy", "use_prev_key_page_no": true, "exclude_header": true, "exclude_footer": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002053, "field_name": "Complete_Title_From_Trust_Agreement_Cover_Page", "document_id": 8011134}], "output_format": {"string_operations_output_format": {"prepend_custom_value": "PSA - "}}}, {"id": 3002054, "key": ["Agreement"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "inline", "include_key": true, "use_prev_key_page_no": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002054, "field_name": "Agreement_Type ", "document_id": 8011134}, {"id": 3002064, "key": ["Dated"], "type": "date", "return_type": "date", "direction": "right", "use_match": "fuzzy", "probable_place": "individual", "use_prev_key_page_no": true, "include_key": false, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002064, "field_name": "Effective_Date", "document_id": 8011134}, {"id": 2, "key": ["registered holders of"], "type": "text", "return_type": "text", "direction": "right", "multi_line_value": true, "end_identifier": ["Funds in the", "from which", "distributions"], "start_identifier": [""], "field_id": 2, "field_name": "Temporary_Deal_Name", "document_id": 8011134, "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true}}}, {"id": 3002052, "key": ["MORTGAGE TRUST"], "type": "text", "return_type": "text", "direction": "inline", "exclude_keys": ["Trustee"], "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002052, "field_name": "Deal_Name", "document_id": 8011134, "output_format": {"copy_extraction_items_output_format": {"is_empty": true, "swap_with": "Temporary_Deal_Name"}}}, {"id": 3002076, "key": ["Indenture Trustee"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002076, "field_name": "Indenture_Trustee", "document_id": 8011134}, {"id": 3002084, "key": ["owner"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002084, "field_name": "Owner", "document_id": 8011134}, {"id": 6, "key": ["Paying Agent"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 6, "field_name": "Temporary_Paying_Agent", "document_id": 8011134}, {"id": 3002094, "key": ["Securities Administrator"], "type": "text", "return_type": "text", "direction": "up", "probable_place": "just_above", "use_prev_key_page_no": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002094, "field_name": "Securities_Admin/Paying_Agent", "document_id": 8011134, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Paying_Agent", "is_empty": true}}}, {"id": 3002096, "key": ["Servicing Administrator"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002096, "field_name": "Servicing_Administrator", "document_id": 8011134}, {"id": 3002121, "key": ["SOX", "SSAE", "<PERSON><PERSON><PERSON>", "Statements on Standards for Attestation Engagements"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002121, "field_name": "Annual_SOX/SSAE_Due?", "document_id": 8011134, "output_format": {"string_operations_output_format": {"replace_with_custom_value": "Yes", "set_default_if_empty": "No"}}}, {"id": 3002100, "key": ["Entity_Due_Recoveries_if_Loan_is_Charged_Off"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002100, "field_name": "Entity_Due_Recoveries_if_Loan_is_Charged_Off", "document_id": 8011134, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Trust"}}}, {"id": 3002136, "key": ["Modifications-does_MS_have_to_purchase"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002136, "field_name": "Modifications-does_MS_have_to_purchase", "document_id": 8011134, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No"}}}, {"id": 3002137, "key": ["Modifications-does_MS_have_to_purchase_Language"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002137, "field_name": "Modifications-does_MS_have_to_purchase_Language", "document_id": 8011134, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002138, "key": ["VA-buy_down_language_Trust"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002138, "field_name": "VA-buy_down_language_Trust", "document_id": 8011134, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No Requirement"}}}, {"id": 3002139, "key": ["Master_Servicing_Other_Purchase_Options"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002139, "field_name": "Master_Servicing_Other_Purchase_Options", "document_id": 8011134, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002142, "key": ["Any_Other_Requirements_Out_of_the_Ordinary"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002142, "field_name": "Any_Other_Requirements_Out_of_the_Ordinary", "document_id": 8011134, "output_format": {"string_operations_output_format": {"set_default_if_empty": "None"}}}, {"id": 3002164, "key": ["Additional_Info_Notes"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002164, "field_name": "Additional_Info_Notes", "document_id": 8011134, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002216, "key": ["Termination_Notes"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002216, "field_name": "Termination_Notes", "document_id": 8011134, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002215, "key": ["Who_Does_Master_Servicer_Inform_of_a_MS_EOD"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002215, "field_name": "Who_Does_Master_Servicer_Inform_of_a_MS_EOD", "document_id": 8011134, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 12, "key": ["ARTICLE II"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 12, "field_name": "Temporary_Article_II_Starts_Field", "document_id": 8011134}, {"id": 13, "key": ["Intention of Parties", "Intention of the Parties"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": false, "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["ARTICLE III", "Section 3.01", "The Certificates shall be"], "start_identifier": [""], "field_id": 13, "field_name": "Temporary_UCC", "document_id": 8011134, "sub_keys": ["mr_cooper_psa_ucc_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 14, "key": ["ADMINISTRATION OF THE TRUST FUND", "ADMINISTRATION OF THE ISSUING ENTITY"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": false, "probable_place": "individual", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["the aggregate of collections and recoveries", "the aggregate of collections with respect to", "In the event a Servicer has remitted", "In the event the Master Servicer has remitted", "Reports to Trustee and Certificateholders", "Reports to the Trustee and the Certificateholders"], "start_identifier": [""], "field_id": 14, "field_name": "Temporary_Custodial_Account", "document_id": 8011134, "sub_keys": ["mr_cooper_psa_custodial_deposit_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002211, "key": ["following events shall constitute a"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 3, "multi_line_value": true, "end_identifier": ["Event of Default described in clause", "Additional Remedies of the Trustee", "Additional Remedies of Trustee"], "start_identifier": [], "field_id": 3002211, "field_name": "Master_Servicer_Termination_With_Cause", "document_id": 8011134, "sub_keys": ["mr_cooper_psa_eod_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 20, "key": ["If an Event of Default described in clauses", "If a Master Servicer Event of Default described in clauses"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["The Trustee and such successor shall take", "If the Trustee, after reasonable effort", "Additional Remedies of Trustee", "Additional Remedies of the Trustee"], "start_identifier": [], "field_id": 20, "field_name": "Temporary_Successor_MS_Qualifications", "document_id": 8011134, "sub_keys": ["mr_cooper_psa_ms_qualification_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002114, "key": ["Annual Statements of Compliance", "Annual Statement of Compliance"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "use_prev_field_page_no": true, "exclude_footer": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Annual Assessments of Compliance", "OPTIONAL REDEMPTION", "Annual Assessments of Compliance", "OPTIONAL TERMINATION", "Accountant’s Attestation", "Accountant's Attestation", "ARTICLE VII"], "start_identifier": [""], "field_id": 3002114, "field_name": "Annual_Officer_Cert_Language", "document_id": 8011134, "sub_keys": ["mr_cooper_psa_annual_statement_compliance_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 16, "key": ["Annual Assessments of Compliance", "Annual Assessment of Compliance"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "use_prev_field_page_no": true, "exclude_footer": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["OPTIONAL REDEMPTION", "OPTIONAL TERMINATION", "Accountant’s Attestation", "Accountant's Attestation", "ARTICLE VII"], "excluding_end_identifiers": ["Assessment of Compliance"], "start_identifier": [""], "field_id": 16, "field_name": "Temporary_Annual_Assessment_Compliance_Language", "document_id": 8011134, "sub_keys": ["mr_cooper_psa_annual_assessment_compliance_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 17, "key": ["ARTICLE IX"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "use_prev_field_page_no": true, "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 17, "field_name": "Temporary_Article_IX_Starts_Field", "document_id": 8011134}, {"id": 18, "key": ["Merger or Consolidation of the Master Servicer", "Merger or Consolidation of Master Servicer", "Merger or Consolidation"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Resignation or Removal of Master Servicer", "Resignation of The Master Servicer", "Resignation of Master Servicer", "Section 9.05", "Assignment or Delegation of Duties"], "start_identifier": [""], "field_id": 18, "field_name": "Temporary_Merger_or_Consolidation", "document_id": 8011134, "sub_keys": ["mr_cooper_psa_merger_consolidation_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002183, "key": ["Resignation of The Master Servicer", "Resignation or Removal of the Master Servicer", "Resignation or Removal of Master Servicer", "Resignation of Master Servicer"], "direction": "down_multi_page", "use_match": "fuzzy", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Assignment or Delegation of Duties", "Assignment or Delegation of Duties by the Master Servicer", "Section 9.07"], "start_identifier": [""], "field_id": 3002183, "field_name": "MS_Resignation_Language", "document_id": 8011134, "sub_keys": ["mr_cooper_psa_ms_resignation_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002159, "key": ["Assignment or Delegation of Duties by the Master Servicer"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Limitation on Liability of the Master Servicer", "Section 9.08", "Indemnification"], "start_identifier": [""], "field_id": 3002159, "field_name": "Master_Servicer_Assignment_Language", "document_id": 8011134, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002156, "key": ["Assign_Rights_to_Master_Servicing_Comp_language"], "direction": "right", "type": "text", "return_type": "text", "use_prev_field_page_no": true, "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002156, "field_name": "Assign_Rights_to_Master_Servicing_Comp_language", "document_id": 8011134, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Master_Servicer_Assignment_Language", "is_empty": true}}}, {"id": 3002157, "key": ["Assignment or Delegation of Duties by the Master Servicer"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Limitation on Liability of the Master Servicer", "Section 9.08", "Indemnification"], "start_identifier": [""], "field_id": 3002157, "field_name": "Assign_Rights_to_Master_Servicing_Compensation", "document_id": 8011134, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"replace_with_custom_value": "Yes", "set_default_if_empty": "Silent"}}}, {"id": 3002158, "key": ["Assignment or Delegation of Duties by the Master Servicer"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Limitation on Liability of the Master Servicer", "Section 9.08", "Indemnification"], "start_identifier": [""], "field_id": 3002158, "field_name": "Master_Servicer_Assignment_Allowed?", "document_id": 8011134, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"replace_with_custom_value": "Yes", "set_default_if_empty": "No"}}}, {"id": 19, "key": ["MISCELLANEOUS PROVISIONS"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "use_match": "exact_match", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 8, "multi_line_value": true, "end_identifier": ["Voting Rights", "Provision of Information"], "start_identifier": [""], "field_id": 19, "field_name": "Temporary_Amendment_Restrictions", "document_id": 8011134, "sub_keys": ["mr_cooper_psa_amendment_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}]