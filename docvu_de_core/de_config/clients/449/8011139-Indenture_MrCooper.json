[{"id": 1, "key": ["Master Servicer", "as Master Servicer", "as Issuer", "Issuer", "Depositor", "as Indenture Trustee", "INDENTURE", "as Depositor", "EXECUITON", "EXECUTION", "EXECUTION VERSION", "EXECUITON VERSION"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "max_page_to_search": 20, "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 1, "field_name": "Temporary_Field1", "document_id": 8011139}, {"id": 3003733, "key": ["EXECUITON", "EXECUTION", "EXECUTION VERSION", "EXECUITON VERSION"], "direction": "down_block", "type": "text", "return_type": "text", "use_match": "fuzzy", "use_prev_key_page_no": true, "exclude_header": true, "exclude_footer": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3003733, "field_name": "Complete_Title_From_Trust_Agreement_Cover_Page", "document_id": 8011139, "alternate_locations": [{"id": 3003733, "key": [""], "direction": "full_page", "type": "text", "return_type": "text", "include_key": true, "use_match": "fuzzy", "use_prev_key_page_no": true, "exclude_header": true, "exclude_footer": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3003733, "field_name": "Complete_Title_From_Trust_Agreement_Cover_Page", "document_id": 8011139}], "output_format": {"string_operations_output_format": {"prepend_custom_value": "IND - "}}}, {"id": 3003734, "key": ["Agreement_Type"], "type": "text", "return_type": "text", "direction": "right", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003734, "field_name": "Agreement_Type ", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Indenture"}}}, {"id": 3003744, "key": ["Dated"], "type": "date", "return_type": "date", "direction": "right", "use_match": "fuzzy", "probable_place": "individual", "use_prev_key_page_no": true, "include_key": false, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003744, "field_name": "Effective_Date", "document_id": 8011139}, {"id": 2, "key": ["registered holders of"], "type": "text", "return_type": "text", "direction": "right", "multi_line_value": true, "end_identifier": ["Funds in the", "from which", "distributions"], "start_identifier": [""], "field_id": 2, "field_name": "Temporary_Deal_Name", "document_id": 8011139, "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true}}}, {"id": 3003732, "key": ["MORTGAGE TRUST", "FUNDING TRUST", "SECURITIZATION TRUST", "TRUST", "MORTGAGE LOAN TRUST"], "type": "text", "return_type": "text", "direction": "inline", "exclude_keys": ["Trustee", "Bank", "Company"], "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3003732, "field_name": "Deal_Name", "document_id": 8011139, "output_format": {"copy_extraction_items_output_format": {"is_empty": true, "swap_with": "Temporary_Deal_Name"}}}, {"id": 3003756, "key": ["Indenture Trustee"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3003756, "field_name": "Indenture_Trustee", "document_id": 8011139}, {"id": 3003764, "key": ["owner"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3003764, "field_name": "Owner", "document_id": 8011139}, {"id": 3003757, "key": ["Issuer", "Issuing Entity"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3003757, "field_name": "Issuer", "document_id": 8011139}, {"id": 6, "key": ["Paying Agent"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 6, "field_name": "Temporary_Paying_Agent", "document_id": 8011139}, {"id": 3003774, "key": ["Securities Administrator"], "type": "text", "return_type": "text", "direction": "up", "probable_place": "just_above", "use_prev_key_page_no": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003774, "field_name": "Securities_Admin/Paying_Agent", "document_id": 8011139, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Paying_Agent", "is_empty": true}}}, {"id": 3003776, "key": ["Servicing Administrator"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3003776, "field_name": "Servicing_Administrator", "document_id": 8011139}, {"id": 3003801, "key": ["SOX", "SSAE", "<PERSON><PERSON><PERSON>", "Statements on Standards for Attestation Engagements"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003801, "field_name": "Annual_SOX/SSAE_Due?", "document_id": 8011139, "output_format": {"string_operations_output_format": {"replace_with_custom_value": "Yes", "set_default_if_empty": "No"}}}, {"id": 3003780, "key": ["Entity_Due_Recoveries_if_Loan_is_Charged_Off"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003780, "field_name": "Entity_Due_Recoveries_if_Loan_is_Charged_Off", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Trust"}}}, {"id": 3003816, "key": ["Modifications-does_MS_have_to_purchase"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003816, "field_name": "Modifications-does_MS_have_to_purchase", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No"}}}, {"id": 3003817, "key": ["Modifications-does_MS_have_to_purchase_Language"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003817, "field_name": "Modifications-does_MS_have_to_purchase_Language", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003818, "key": ["VA-buy_down_language_Trust"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003818, "field_name": "VA-buy_down_language_Trust", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No Requirement"}}}, {"id": 3003819, "key": ["Master_Servicing_Other_Purchase_Options"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003819, "field_name": "Master_Servicing_Other_Purchase_Options", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003822, "key": ["Any_Other_Requirements_Out_of_the_Ordinary"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003822, "field_name": "Any_Other_Requirements_Out_of_the_Ordinary", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "None"}}}, {"id": 3003844, "key": ["Additional_Info_Notes"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003844, "field_name": "Additional_Info_Notes", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003896, "key": ["Termination_Notes"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003896, "field_name": "Termination_Notes", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003895, "key": ["Who_Does_Master_Servicer_Inform_of_a_MS_EOD"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003895, "field_name": "Who_Does_Master_Servicer_Inform_of_a_MS_EOD", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 13, "key": ["ARTICLE II"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 13, "field_name": "Temporary_Article_II_Starts_Field", "document_id": 8011139}, {"id": 14, "key": ["ARTICLE IX"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "use_prev_field_page_no": true, "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 14, "field_name": "Temporary_Article_IX_Starts_Field", "document_id": 8011139}, {"id": 15, "key": ["Merger or Consolidation of the Master Servicer", "Merger or Consolidation of Master Servicer", "Merger or Consolidation"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Resignation or Removal of Master Servicer", "Resignation of The Master Servicer", "Resignation of Master Servicer", "Section 9.05", "Section 10.", "Assignment or Delegation of Duties"], "start_identifier": [""], "field_id": 15, "field_name": "Temporary_Merger_or_Consolidation", "document_id": 8011139, "sub_keys": ["mr_cooper_ind_merger_consolidation_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003863, "key": ["Resignation of The Master Servicer", "Resignation or Removal of the Master Servicer", "Resignation or Removal of Master Servicer", "Resignation of Master Servicer"], "direction": "down_multi_page", "use_match": "fuzzy", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Assignment or Delegation of Duties", "Assignment or Delegation of Duties by the Master Servicer", "Section 10.", "Section 9.07"], "start_identifier": [""], "field_id": 3003863, "field_name": "MS_Resignation_Language", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003839, "key": ["Assignment or Delegation of Duties by the Master Servicer"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Limitation on Liability of the Master Servicer", "Section 9.08", "Indemnification"], "start_identifier": [""], "field_id": 3003839, "field_name": "Master_Servicer_Assignment_Language", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003836, "key": ["Assign_Rights_to_Master_Servicing_Comp_language"], "direction": "right", "type": "text", "return_type": "text", "use_prev_field_page_no": true, "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003836, "field_name": "Assign_Rights_to_Master_Servicing_Comp_language", "document_id": 8011139, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Master_Servicer_Assignment_Language", "is_empty": true}}}, {"id": 16, "key": ["MISCELLANEOUS PROVISIONS"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "use_match": "exact_match", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 10, "multi_line_value": true, "end_identifier": ["Voting Rights", "Provision of Information"], "start_identifier": [""], "field_id": 16, "field_name": "Temporary_Amendment_Restrictions", "document_id": 8011139, "sub_keys": ["mr_cooper_ind_amendment_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}]