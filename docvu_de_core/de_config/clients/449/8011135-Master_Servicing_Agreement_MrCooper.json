[{"id": 1, "key": ["Master Servicer", "as Master Servicer", "Depositor", "as Depositor", "EXECUITON", "EXECUTION", "EXECUTION VERSION", "EXECUITON VERSION"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "max_page_to_search": 10, "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 1, "field_name": "Temporary_Field1", "document_id": 8011135}, {"id": 3002391, "key": ["EXECUITON", "EXECUTION", "EXECUTION VERSION", "EXECUITON VERSION"], "direction": "down_block", "type": "text", "return_type": "text", "use_match": "fuzzy", "use_prev_key_page_no": true, "exclude_header": true, "exclude_footer": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002391, "field_name": "Complete_title_from_Master_Servicing_Agreement_cover_page", "document_id": 8011135, "alternate_locations": [{"id": 3002391, "key": [""], "direction": "full_page", "type": "text", "return_type": "text", "include_key": true, "use_match": "fuzzy", "use_prev_key_page_no": true, "exclude_header": true, "exclude_footer": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002391, "field_name": "Complete_title_from_Master_Servicing_Agreement_cover_page", "document_id": 8011135}], "output_format": {"string_operations_output_format": {"prepend_custom_value": "MSA - "}}}, {"id": 3002400, "key": ["Dated"], "direction": "inline", "type": "date", "return_type": "date", "probable_place": "individual", "include_key": false, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002400, "field_name": "Effective_Date", "document_id": 8011135, "alternate_locations": [{"id": 3002400, "key": ["“Closing Date”:", "Closing Date:"], "direction": "inline", "type": "date", "return_type": "date", "include_key": false, "use_match": "fuzzy", "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002400, "field_name": "Effective_Date", "document_id": 8011135}]}, {"id": 2, "key": ["registered holders of"], "type": "text", "return_type": "text", "direction": "right", "multi_line_value": true, "end_identifier": ["Funds in the", "from which", "distributions"], "start_identifier": [""], "field_id": 2, "field_name": "Temporary_Deal_Name", "document_id": 8011135, "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true}}}, {"id": 3002388, "key": ["Master Servicer"], "direction": "down", "type": "text", "return_type": "text", "exclude_keys": ["Trustee"], "multi_line_value": false, "end_identifier": ["as Issuer"], "start_identifier": [""], "field_id": 3002388, "field_name": "Deal_Name", "document_id": 8011135, "output_format": {"copy_extraction_items_output_format": {"is_empty": true, "swap_with": "Temporary_Deal_Name"}}}, {"id": 3, "key": ["Indenture Trustee"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3, "field_name": "Temporary_Indenture_Trustee", "document_id": 8011135}, {"id": 4, "key": ["owner"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 4, "field_name": "Temporary_Owner", "document_id": 8011135}, {"id": 5, "key": ["“Paying Agent”:", "Paying Agent:"], "type": "text", "return_type": "text", "direction": "right", "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Percentage Interest", "Person", "Plan", "Prepayment"], "start_identifier": [""], "field_id": 3002094, "field_name": "Temporary_Paying_Agent", "document_id": 5, "output_format": {"ner_service_req_output_format": {"text_field_name": "Temporary_Paying_Agent", "label": ["ORGANIZATION"]}}}, {"id": 6, "key": ["“Securities Administrator”:", "Securities Administrator:"], "type": "text", "return_type": "text", "direction": "right", "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Securities Administrator <PERSON><PERSON>", "<PERSON><PERSON>"], "start_identifier": [""], "field_id": 5, "field_name": "Temporary_Securities_Admin", "document_id": 8011135, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Paying_Agent", "is_empty": true}, "ner_service_req_output_format": {"text_field_name": "Temporary_Securities_Admin", "label": ["ORGANIZATION"]}}}, {"id": 3002430, "key": ["Securities Administrator"], "type": "text", "return_type": "text", "direction": "up", "probable_place": "just_above", "use_prev_key_page_no": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002430, "field_name": "Securities_Admin/Paying_Agent", "document_id": 8011135, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Securities_Admin", "is_empty": true}}}, {"id": 7, "key": ["“Servicing Administrator”:", "Servicing Administrator:"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": false, "end_identifier": [", as", "Servicing Administrator <PERSON><PERSON>", "Servicing Advances", "Servicing Agreement"], "start_identifier": [""], "field_id": 6, "field_name": "Temporary_Servicing_Administrator", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Temporary_Servicing_Administrator", "label": ["ORGANIZATION"]}}}, {"id": 3002432, "key": ["Servicing Administrator"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002432, "field_name": "Servicing_Administrator", "document_id": 8011135, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Servicing_Administrator", "is_empty": true}}}, {"id": 8, "key": ["“Trustee”:", "Trustee:", "“Resident Trustee”:", "Resident Trustee:"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Trustee Credit", "Trustee Fee"], "start_identifier": [""], "field_id": 7, "field_name": "Temporary_Trustee", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Temporary_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3002435, "key": ["Trustee"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3002435, "field_name": "Trustee (Include_Resident_Trustee)", "document_id": 8011135, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Trustee", "is_empty": true}}}, {"id": 3002395, "key": ["“Administrator”:", "Administrator:", "“Trust Administrator”:", "Trust Administrator:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Advance", "Adverse Grantor Trust Event", "Adverse", "Affiliate"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002395, "field_name": "Administrator/Trust_Administrator", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"ner_service_req_output_format": {"text_field_name": "Administrator/Trust_Administrator", "label": ["ORGANIZATION"]}}}, {"id": 3002396, "key": ["“Affiliate”:", "Affiliate:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Annual Cap", "Aggregate Voting Interests", "Aggregate Expense Rate", "Aggregate Servicing Fee", "Agent Member", "Aggregate Stated Principal <PERSON>"], "start_identifier": ["Affiliate"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002396, "field_name": "Definition_of_Affiliates", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002397, "key": ["“Ancillary Income”:", "Ancillary Income:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Aggregate Voting Interests", "Aggregate Expense Rate", "Aggregate Servicing Fee", "Agent Member", "Aggregate Stated Principal <PERSON>"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002397, "field_name": "Ancillary_Income", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002398, "key": ["“Closing Date”:", "Closing Date:"], "direction": "inline", "type": "date", "return_type": "date", "include_key": true, "probable_place": "individual", "use_match": "fuzzy", "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002398, "field_name": "Closing_Date", "document_id": 8011135}, {"id": 3002434, "key": ["“Sponsor”:", "Sponsor:"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": false, "end_identifier": ["SPS", "Startup Day", "SR 202", "Stated Principal <PERSON>"], "start_identifier": [""], "field_id": 3002434, "field_name": "Sponsor", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Sponsor", "label": ["ORGANIZATION"]}}}, {"id": 3002402, "key": ["Computershare Custodial <PERSON>e", "“<PERSON>ustodian”:", "Custodian:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Compensating Interest", "<PERSON><PERSON><PERSON><PERSON>", "Cut-off Date", "Debt Service Reduction"], "start_identifier": ["<PERSON><PERSON><PERSON><PERSON>"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002402, "field_name": "Custodian_Names", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"ner_service_req_output_format": {"text_field_name": "Custodian_Names", "label": ["ORGANIZATION"]}}}, {"id": 3002405, "key": ["“Delaware Trustee”:", "Delaware Trustee:"], "type": "text", "return_type": "text", "direction": "right", "multi_line_value": false, "probable_place": "individual", "end_identifier": ["Delegated Authority", "Delinquency Trigger"], "start_identifier": [""], "field_id": 3002405, "field_name": "Delaware_Trustee", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Delaware_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3002407, "key": ["“Depositor”:", "Depositor:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "multi_line_value": false, "probable_place": "individual", "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3002407, "field_name": "Depositor", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Depositor", "label": ["ORGANIZATION"]}}}, {"id": 3002408, "key": ["“Determination Date”:", "Determination Date:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": false, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Designated Depository Institution", "Directing Noteholders", "Distributed Ledger Agent", "Disqualified Organization"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002408, "field_name": "Determination_Date", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent", "remove_from_beginning": ["Determination Date"], "remove_special_chars_from_beginning": true}}}, {"id": 3002409, "key": ["“Distribution Date”:", "Distribution Date:", "“Payment Date”:", "Payment Date:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Percentage Interest", "PennyMac Servicer", "Distribution Date Statement", "Distribution Compliance Period", "Due Date", "Due Period"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002409, "field_name": "Distribution_Date/Payment_Date", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["Payment Date"], "remove_special_chars_from_beginning": true}}}, {"id": 3002410, "key": ["“Due period”:", "Due period:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "include_key": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Electronic Form", "eCommerce Laws", "Eligible", "eligible account", "edgar"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002410, "field_name": "Due_Period", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002411, "key": ["“Eligible Institution:”:", "Eligible Institution:", "Qualified Depository:", "“Qualified Depository”:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 3, "multi_line_value": true, "end_identifier": ["Eligible Investments", "Eligible Deposit Account", "Eligible Substitute Mortgage Loan", "EPD event", "erisa"], "start_identifier": ["Eligible Account"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002411, "field_name": "Eligible_Institution_Language", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "alternate_locations": [{"id": 3002411, "key": ["“Eligible Account”:", "Eligible Account:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 3, "multi_line_value": true, "end_identifier": ["Eligible Investments", "Eligible Deposit Account", "EPD event", "erisa"], "start_identifier": ["Eligible Account"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002411, "field_name": "Eligible_Institution_Language", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}]}, {"id": 3002412, "key": ["“Indenture Trustee”:", "Indenture Trustee:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_line_value": false, "end_identifier": [", a", "Indenture Trustee Fee"], "start_identifier": [""], "field_id": 3002412, "field_name": "Indenture_Trustee", "document_id": 8011135, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Indenture_Trustee", "is_empty": true}, "ner_service_req_output_format": {"text_field_name": "Indenture_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3002413, "key": ["Issuer or Trust:", "“Issuer”:", "Issuer:", "“Issuing Entity”:", "Issuing Entity:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Issuer Request", "Item 1123", "KBRA", "Latest Possible Maturity Date", "Liquidat"], "start_identifier": [""], "field_id": 3002413, "field_name": "Issuer", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Issuer", "label": ["ORGANIZATION"]}}}, {"id": 3002415, "key": ["“Loan Data Agent”:", "Loan Data Agent:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Fee", "Loan-To-Value Ratio", "Loan Data Agent <PERSON>e", "Loan Data Agreement", "Lower-Tier"], "start_identifier": [""], "field_id": 3002415, "field_name": "Loan_Data_Agent", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Loan_Data_Agent", "label": ["ORGANIZATION"]}}}, {"id": 3002418, "key": ["“Master Servicing Fee Rate”:", "“Master Servicer Fee Rate”:", "Master Servicer Fee Rate:", "Master Servicing Fee Rate:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Master Servicing Officer", "Master Servicing Transfer Costs", "Moody’s", "Master Servicer Remittance Date", "Maturity"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002418, "field_name": "Master_Servicing_Fee_Definition/MS_Rate", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 9, "key": ["“Compensating Interest Payments”:", "Compensating Interest Payments:", "“Compensating Interest Payment”:", "Compensating Interest Payment:", "“Compensating Interest”:", "Compensating Interest:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Compliance Certificate", "Confidentiality Agreement", "Condemnation Proceeds"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 9, "field_name": "Temporary_Compensating_Int_Payments", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002419, "key": ["“Commission", "“Master Servicer Compensating Interest Payments”:", "Master Servicer Compensating Interest Payments:", "“MS Compensating Interest Payments”:", "MS Compensating Interest Payments:", "“Master Servicer Compensating Interest Payment”:", "Master Servicer Compensating Interest Payment:", "“MS Compensating Interest Payment”:", "MS Compensating Interest Payment:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Condemnation Proceeds", "Master Servicer Remittance Date", "Master Servicer Event of Default", "Master Servicer Report Date", "Master Servicer Remittance Date", "Maturity"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002419, "field_name": "MS_Compensating_Int_Payments", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Compensating_Int_Payments", "is_empty": true}, "string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002420, "key": ["“Owner”:", "Owner:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Owner Trustee", "Paying Agent", "Ownership Interest", "Payahead"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002420, "field_name": "Owner", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"ner_service_req_output_format": {"text_field_name": "Owner", "label": ["ORGANIZATION"]}, "copy_extraction_items_output_format": {"swap_with": "Temporary_Owner", "is_empty": true}}}, {"id": 3002087, "key": ["“Owner Trustee”:", "Owner Trustee:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Owner <PERSON>ee <PERSON><PERSON>"], "start_identifier": [""], "field_id": 3002087, "field_name": "Owner_Trustee", "document_id": 8011135, "output_format": {"ner_service_req_output_format": {"text_field_name": "Owner_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3002424, "key": ["“Eligible Investments”:", "Eligible Investments:", "“Permitted Investments”:", "Permitted Investments:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 3, "multi_line_value": true, "end_identifier": ["ERISA", "Escrow", "EPD event", "Event of Default", "Permitted Transferee", "Person"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002424, "field_name": "Permitted_Investments", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002425, "key": ["“Prepayment Interest Shortfall”:", "Prepayment Interest Shortfall:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Prepayment Premium", "Primary Mortgage Insurance Policy", "Prepayment Period"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002425, "field_name": "Prepayment_Interest_Shortfall", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No Requirement"}}}, {"id": 10, "key": ["“Collection Period”:", "Collection Period:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "include_key": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Commission", "Compliance certificate", "Condemnation Proceeds", "eCommerce Laws", "eligible account", "edgar"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 10, "field_name": "Temporary_Collection_Period", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002427, "key": ["“Prepayment Period”:", "Prepayment Period:", "Collection Period"], "direction": "down_multi_page", "type": "text", "return_type": "text", "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "include_key": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Prepayment Premium", "Principal <PERSON><PERSON><PERSON><PERSON>", "Primary Mortgage Insurance", "Principal Prepayment", "Principal Remittance Amount", "Principal Balance Shortfall", "Principal Distribution Amount", "Commission"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002427, "field_name": "Prepayment_Period_Language", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002428, "key": ["Rating Agencies", "“Rating Agency”:", "Rating Agency:"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": true, "end_identifier": [", or any", "rating agency information", "rating agency confirmation", "Rated Notes"], "start_identifier": [""], "field_id": 3002428, "field_name": "Rating_Agencies", "document_id": 8011135, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["Rating Agency"], "remove_keywords": ["Rating Agency"]}, "ner_service_req_output_format": {"text_field_name": "Rating_Agencies", "label": ["ORGANIZATION"]}}}, {"id": 3002433, "key": ["17g-5 of the Exchange Act.", "Realized Losses", "“Realized Loss”:", "Realized Loss:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Record Date", "Reconciled Market Value", "Redemption Price", "Registered Holder", "Record Date:"], "start_identifier": ["Exchange Act", "Realized Losses"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3002433, "field_name": "Realized_Loss_Definition", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002457, "key": ["SOX", "SSAE", "<PERSON><PERSON><PERSON>", "Statements on Standards for Attestation Engagements"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002457, "field_name": "Annual_SOX/SSAE_Due? if_so_yes_if_not_No", "document_id": 8011135, "output_format": {"string_operations_output_format": {"replace_with_custom_value": "Yes", "set_default_if_empty": "No"}}}, {"id": 3002436, "key": ["Entity_Due_Recoveries_if_Loan_is_Charged_Off"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002436, "field_name": "Entity_Due_Recoveries_if_Loan_is_Charged_Off", "document_id": 8011135, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Trust"}}}, {"id": 3002472, "key": ["Modifications-does_MS_have_to_purchase"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002472, "field_name": "Modifications - does_MS_have_to_purchase", "document_id": 8011135, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No"}}}, {"id": 3002473, "key": ["Modifications-does_MS_have_to_purchase_Language"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002473, "field_name": "Modifications - does_MS_have_to_purchase - Language", "document_id": 8011135, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002474, "key": ["VA-buy_down_language_Trust"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002474, "field_name": "VA - buy_down_language (Trust)", "document_id": 8011135, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No Requirement"}}}, {"id": 3002475, "key": ["Master_Servicing_Other_Purchase_Options"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002475, "field_name": "Master_Servicing_Other_Purchase_Options", "document_id": 8011135, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002478, "key": ["Any_Other_Requirements_Out_of_the_Ordinary"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002478, "field_name": "Any_Other_Requirements_Out_of_the_Ordinary", "document_id": 8011135, "output_format": {"string_operations_output_format": {"set_default_if_empty": "None"}}}, {"id": 3002500, "key": ["Additional_Info_Notes"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002500, "field_name": "Additional_Info_Notes", "document_id": 8011135, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002552, "key": ["Termination_Notes"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002552, "field_name": "Termination_Notes", "document_id": 8011135, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3002551, "key": ["Who_Does_Master_Servicer_Inform_of_a_MS_EOD"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002551, "field_name": "Who_does_Master_Servicer_informs_of_MS_EOD", "document_id": 8011135, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 11, "key": ["ARTICLE VI"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "use_prev_field_page_no": true, "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 11, "field_name": "Temporary_Article_IX_Starts_Field", "document_id": 8011135}, {"id": 12, "key": ["Merger or Consolidation of the Master Servicer", "Merger or Consolidation of Master Servicer", "Merger or Consolidation"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Section 6.02", "Resignation or Removal of Master Servicer", "Resignation of The Master Servicer", "Resignation of Master Servicer", "Section 9.05", "Assignment or Delegation of Duties"], "start_identifier": [""], "field_id": 12, "field_name": "Temporary_Merger_or_Consolidation", "document_id": 8011135, "sub_keys": ["mr_cooper_msa_merge_consolidation_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3002519, "key": ["Resignation of The Master Servicer", "Resignation or Removal of the Master Servicer", "Resignation or Removal of Master Servicer", "Resignation of Master Servicer"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "exclude_keys": ["assignment of this Agreement and"], "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["SECTION 6.04.", "Assignment or Delegation of Duties", "Assignment or Delegation of Duties by the Master Servicer", "Section 9.07"], "start_identifier": [""], "field_id": 3002519, "field_name": "MS_Resignation_Language", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["termination or assignment of this Agreement and the resignation or removal of the Master Servicer."], "remove_special_chars_from_beginning": true}}}, {"id": 3002495, "key": ["Assignment or Delegation of Duties by the Master Servicer"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Limitation on Liability of the Master Servicer", "Section 9.08", "Indemnification"], "start_identifier": [""], "field_id": 3002495, "field_name": "Master_Servicer_Assignment_Language", "document_id": 8011135, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["Assignment or Delegation of Duties by the Master Servicer.", "Section 6.04. Assignment or Delegation of Duties by the Master Servicer"], "remove_special_chars_from_beginning": true}}}, {"id": 3002492, "key": ["Assign_Rights_to_Master_Servicing_Comp_language"], "direction": "right", "type": "text", "return_type": "text", "use_prev_field_page_no": true, "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3002492, "field_name": "Assign_Rights_to_Master_Servicing_Comp_language", "document_id": 8011135, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Master_Servicer_Assignment_Language", "is_empty": true}}}, {"id": 13, "key": ["MISCELLANEOUS PROVISIONS"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "use_match": "exact_match", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 7, "multi_line_value": true, "end_identifier": ["Voting Rights", "Provision of Information"], "start_identifier": [""], "field_id": 13, "field_name": "Temporary_Amendment_Restrictions", "document_id": 8011135, "sub_keys": ["mr_cooper_msa_amendment_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}]