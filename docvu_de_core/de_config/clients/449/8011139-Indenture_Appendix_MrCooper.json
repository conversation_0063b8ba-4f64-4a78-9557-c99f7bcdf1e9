[{"id": 1, "key": ["Master Servicer", "as Master Servicer", "as Issuer", "Issuer", "Depositor", "as Indenture Trustee", "INDENTURE", "as Depositor", "EXECUITON", "EXECUTION", "EXECUTION VERSION", "EXECUITON VERSION"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "max_page_to_search": 20, "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 1, "field_name": "Temporary_Field1", "document_id": 8011139}, {"id": 3003733, "key": ["EXECUITON", "EXECUTION", "EXECUTION VERSION", "EXECUITON VERSION"], "direction": "down_block", "type": "text", "return_type": "text", "use_match": "fuzzy", "use_prev_key_page_no": true, "exclude_header": true, "exclude_footer": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3003733, "field_name": "Complete_Title_From_Trust_Agreement_Cover_Page", "document_id": 8011139, "alternate_locations": [{"id": 3003733, "key": [""], "direction": "full_page", "type": "text", "return_type": "text", "include_key": true, "use_match": "fuzzy", "use_prev_key_page_no": true, "exclude_header": true, "exclude_footer": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3003733, "field_name": "Complete_Title_From_Trust_Agreement_Cover_Page", "document_id": 8011139}], "output_format": {"string_operations_output_format": {"prepend_custom_value": "IND - "}}}, {"id": 3003734, "key": ["Agreement_Type"], "type": "text", "return_type": "text", "direction": "right", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003734, "field_name": "Agreement_Type ", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Indenture"}}}, {"id": 3003744, "key": ["Dated"], "type": "date", "return_type": "date", "direction": "right", "use_match": "fuzzy", "probable_place": "individual", "use_prev_key_page_no": true, "include_key": false, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003744, "field_name": "Effective_Date", "document_id": 8011139}, {"id": 2, "key": ["registered holders of"], "type": "text", "return_type": "text", "direction": "right", "multi_line_value": true, "end_identifier": ["Funds in the", "from which", "distributions"], "start_identifier": [""], "field_id": 2, "field_name": "Temporary_Deal_Name", "document_id": 8011139, "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true}}}, {"id": 3003732, "key": ["MORTGAGE TRUST", "FUNDING TRUST", "SECURITIZATION TRUST", "TRUST", "MORTGAGE LOAN TRUST"], "type": "text", "return_type": "text", "direction": "inline", "exclude_keys": ["Trustee", "Bank", "Company"], "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3003732, "field_name": "Deal_Name", "document_id": 8011139, "output_format": {"copy_extraction_items_output_format": {"is_empty": true, "swap_with": "Temporary_Deal_Name"}}}, {"id": 3, "key": ["Indenture Trustee"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3, "field_name": "Temporary_Indenture_Trustee", "document_id": 8011139}, {"id": 4, "key": ["owner"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 4, "field_name": "Temporary_Owner", "document_id": 8011139}, {"id": 5, "key": ["Issuer", "Issuing Entity"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 5, "field_name": "Temporary_Issuer", "document_id": 8011139}, {"id": 6, "key": ["Paying Agent"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 6, "field_name": "Temporary_Paying_Agent", "document_id": 8011139}, {"id": 7, "key": ["Paying Agent:"], "type": "text", "return_type": "text", "direction": "right", "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Percentage Interest", "Person", "Plan", "Prepayment"], "start_identifier": [""], "field_id": 3002094, "field_name": "Paying_Agent", "document_id": 7, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Paying_Agent", "is_empty": true}, "ner_service_req_output_format": {"text_field_name": "Paying_Agent", "label": ["ORGANIZATION"]}}}, {"id": 8, "key": ["Securities Administrator:"], "type": "text", "return_type": "text", "direction": "right", "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Securities Administrator <PERSON><PERSON>", "<PERSON><PERSON>"], "start_identifier": [""], "field_id": 8, "field_name": "Securities_Administrator", "document_id": 8011139, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Paying_Agent", "is_empty": true}, "ner_service_req_output_format": {"text_field_name": "Securities_Administrator", "label": ["ORGANIZATION"]}}}, {"id": 3003774, "key": ["Securities Administrator"], "type": "text", "return_type": "text", "direction": "up", "probable_place": "just_above", "use_prev_key_page_no": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003774, "field_name": "Securities_Admin/Paying_Agent", "document_id": 8011139, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Securities_Administrator", "is_empty": true}}}, {"id": 9, "key": ["Servicing Administrator:"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": true, "end_identifier": [", as", "Servicing Administrator <PERSON><PERSON>", "Servicing Advances", "Servicing Agreement"], "start_identifier": [""], "field_id": 9, "field_name": "Temporary_Servicing_Administrator", "document_id": 8011139, "output_format": {"ner_service_req_output_format": {"text_field_name": "Temporary_Servicing_Administrator", "label": ["ORGANIZATION"]}}}, {"id": 3003776, "key": ["Servicing Administrator"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3003776, "field_name": "Servicing_Administrator", "document_id": 8011139, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Servicing_Administrator", "is_empty": true}}}, {"id": 10, "key": ["Trustee:", "Resident Trustee:"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Trustee Credit", "Trustee Fee"], "start_identifier": [""], "field_id": 10, "field_name": "Trustee", "document_id": 8011139, "output_format": {"ner_service_req_output_format": {"text_field_name": "Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3003739, "key": ["Administrator:", "Trust Administrator:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Advance", "Adverse Grantor Trust Event", "Adverse", "Affiliate", "Trust Administrator <PERSON><PERSON>", "Trust Agreement"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003739, "field_name": "Administrator/Trust_Administrator", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"ner_service_req_output_format": {"text_field_name": "Administrator/Trust_Administrator", "label": ["ORGANIZATION"]}}}, {"id": 3003740, "key": ["Affiliate:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Aggregate Voting Interests", "Aggregate Expense Rate", "Affiliated", "Aggregate", "Aggregate Servicing Fee", "Agent Member", "Aggregate Stated Principal <PERSON>"], "start_identifier": ["Affiliate"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003740, "field_name": "Definition_of_Affiliates", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003741, "key": ["Ancillary Income:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Aggregate Voting Interests", "Aggregate Expense Rate", "Aggregate Servicing Fee", "Agent Member", "Aggregate Stated Principal <PERSON>"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003741, "field_name": "Ancillary_Income", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003742, "key": ["Closing Date:"], "direction": "right", "type": "date", "return_type": "date", "probable_place": "individual", "include_key": false, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003742, "field_name": "Closing_Date", "document_id": 8011139}, {"id": 3003778, "key": ["Sponsor:"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": false, "end_identifier": ["SPS", "Startup Day", "SR 202", "Stated Principal <PERSON>"], "start_identifier": [""], "field_id": 3003778, "field_name": "Sponsor", "document_id": 8011139, "output_format": {"ner_service_req_output_format": {"text_field_name": "Sponsor", "label": ["ORGANIZATION"]}}}, {"id": 3003775, "key": ["Seller:"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": false, "end_identifier": ["Senior Certificates", "“Servicer", "Senior"], "start_identifier": [""], "field_id": 3003775, "field_name": "<PERSON><PERSON>", "document_id": 8011139, "output_format": {"ner_service_req_output_format": {"text_field_name": "<PERSON><PERSON>", "label": ["ORGANIZATION"]}}}, {"id": 3003745, "key": ["controlling holder:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Controlling Holder Optional Purchase", "Corresponding", "Corporate Trust Office", "Cooperative Corporation", "Cooperative", "Corresponding Class of Certificates"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003745, "field_name": "Controlling_Holder", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Sponsor", "found_str": ["Sponsor"]}, "llm_operations_output_format": {"custom": false}}}, {"id": 3003743, "key": ["Cut-Off Date:", "Cutoff Date:", "Cut Off Date:"], "direction": "right", "type": "date", "return_type": "date", "probable_place": "individual", "include_key": false, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003743, "field_name": "Cut-Off_Date", "document_id": 8011139}, {"id": 3003746, "key": ["Custodian:", "<PERSON><PERSON><PERSON>:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["<PERSON><PERSON><PERSON><PERSON>", "Computershare Custodian Annual Cap", "Condemnation Proceeds", "Controlling Holder", "Cut-off Date", "Debt Service Reduction"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003746, "field_name": "Custodian_Names", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"ner_service_req_output_format": {"text_field_name": "Custodian_Names", "label": ["ORGANIZATION"]}}}, {"id": 3003749, "key": ["Delaware Trustee:"], "type": "text", "return_type": "text", "direction": "right", "multi_line_value": false, "probable_place": "individual", "end_identifier": ["Delegated Authority", "Delinquent", "Demand", "Delinquency Trigger"], "start_identifier": [""], "field_id": 3003749, "field_name": "Delaware_Trustee", "document_id": 8011139, "output_format": {"ner_service_req_output_format": {"text_field_name": "Delaware_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3003751, "key": ["Depositor:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "multi_line_value": false, "probable_place": "individual", "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3003751, "field_name": "Depositor", "document_id": 8011139, "output_format": {"ner_service_req_output_format": {"text_field_name": "Depositor", "label": ["ORGANIZATION"]}}}, {"id": 3003752, "key": ["Determination Date:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Directing Noteholders", "Directed Review", "Due Date", "Due period", "Distributed Ledger Agent", "Disqualified Organization"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003752, "field_name": "Determination_Date", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003753, "key": ["Distribution Date:", "Payment Date:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Distribution Date Statement", "Permitted Investments", "Payment Date Statement", "Percentage Interest", "Permanent Regulation", "Distribution Compliance Period", "Due Date", "Due Period"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003753, "field_name": "Distribution_Date/Payment_Date", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"remove_till_from_end": ["commencing"]}}}, {"id": 3003754, "key": ["Due period:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "include_key": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["eCommerce Laws", "Eligible", "eligible account", "edgar"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003754, "field_name": "Due_Period", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003755, "key": ["Eligible Institution:", "Qualified Depository:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 3, "multi_line_value": true, "end_identifier": ["Eligible Investments", "Eligible Deposit Account", "<PERSON>scrow Account", "EU Securitization", "Eligible Substitute Mortgage Loan", "EPD event", "erisa"], "start_identifier": ["Eligible Account"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003755, "field_name": "Eligible_Institution_Language", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "alternate_locations": [{"id": 3003755, "key": ["Eligible Account:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 3, "multi_line_value": true, "end_identifier": ["Eligible Investments", "Eligible Deposit Account", "<PERSON>scrow Account", "EU Securitization", "Eligible Substitute Mortgage Loan", "EPD event", "erisa"], "start_identifier": ["Eligible Account"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003755, "field_name": "Eligible_Institution_Language", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}]}, {"id": 3003756, "key": ["Indenture Trustee:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_line_value": false, "end_identifier": [", a", "Indenture Trustee Fee"], "start_identifier": [""], "field_id": 3003756, "field_name": "Indenture_Trustee", "document_id": 8011139, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Indenture_Trustee", "is_empty": true}, "ner_service_req_output_format": {"text_field_name": "Indenture_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3003757, "key": ["Issuer:", "Issuing Entity:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Issuer Request", "Item 1123", "KBRA", "Latest Possible Maturity Date", "Liquidat"], "start_identifier": [""], "field_id": 3003757, "field_name": "Issuer", "document_id": 8011139, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Issuer", "is_empty": true}, "ner_service_req_output_format": {"text_field_name": "Issuer", "label": ["ORGANIZATION"]}}}, {"id": 3003759, "key": ["Loan Data Agent:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Fee", "Loan-To-Value Ratio", "Loan Data Agent Agreement", "Loan Data Agent Annual Expense Cap", "Loan Data Agent <PERSON>e", "Loan Data Agreement", "Lower-Tier"], "start_identifier": [""], "field_id": 3003759, "field_name": "Loan_Data_Agent", "document_id": 8011139, "output_format": {"ner_service_req_output_format": {"text_field_name": "Loan_Data_Agent", "label": ["ORGANIZATION"]}}}, {"id": 3003760, "key": ["Lender Paid Mortgage Insurance:", "LPMI:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Master Servicer Collection Account", "Master Servicer Remittance Date", "Master Servicer Remittance Date", "LT-R Interest"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003760, "field_name": "LPMI_Insurer", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"llm_operations_output_format": {"custom": false}}}, {"id": 3003761, "key": ["LPMI_Paid_By"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "max_page_to_search": 1, "end_identifier": [""], "start_identifier": [""], "field_id": 3003761, "field_name": "LPMI_Paid_By", "document_id": 8011139, "output_format": {"llm_operations_output_format": {"text_field_name": "LPMI_Insurer"}}}, {"id": 3003762, "key": ["Master Servicer Fee Rate:", "Master Servicing Fee Rate:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Master Servicing Transfer Costs", "Material Breach", "Moody’s", "MAXEX", "Maximum Rate", "MBA Method", "MERS", "Master Servicer Remittance Date", "Maturity"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003762, "field_name": "Master_Servicing_Fee_Definition_MS_Rate", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 11, "key": ["Compensating Interest Payments:", "Compensating Interest Payment:", "Compensating Interest:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Compliance Certificate", "Confidentiality Agreement", "Condemnation Proceeds"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 11, "field_name": "Temporary_Compensating_Int_Payments", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003763, "key": ["Master Servicer Compensating Interest Payments:", "MS Compensating Interest Payments:", "Master Servicer Compensating Interest Payment:", "MS Compensating Interest Payment:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Master Servicer Remittance Date", "Master Servicer Event of Default", "Master Servicer Report Date", "Master Servicer Remittance Date", "Maturity"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003763, "field_name": "MS_Compensating_Int_Payments", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Compensating_Int_Payments", "is_empty": true}, "string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003764, "key": ["Owner:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Owner Trustee", "Paying Agent", "Ownership Interest", "Payahead"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003764, "field_name": "Owner", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"ner_service_req_output_format": {"text_field_name": "Owner", "label": ["ORGANIZATION"]}, "copy_extraction_items_output_format": {"swap_with": "Temporary_Owner", "is_empty": true}}}, {"id": 3003767, "key": ["Owner Trustee:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Owner <PERSON>ee <PERSON><PERSON>", "P&I Advance"], "start_identifier": [""], "field_id": 3003767, "field_name": "Owner_Trustee", "document_id": 8011139, "output_format": {"ner_service_req_output_format": {"text_field_name": "Owner_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3003768, "key": ["Eligible Investments:", "Permitted Investments:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 3, "multi_line_value": true, "end_identifier": ["ERISA", "Escrow", "EPD event", "Enforcement Expenses", "EPD", "Eminent Domain Expenses", "Event of Default", "Permitted Transferee", "Prepayment", "Preliminary Private Placement Memorandum", "Plan", "Person"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003768, "field_name": "Permitted_Investments", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003769, "key": ["Prepayment Interest Shortfall:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Prepayment Premium", "Principal Remittance Amount", "Primary Mortgage Insurance Policy", "Prepayment Period"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003769, "field_name": "Prepayment_Interest_Shortfall", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No Requirement"}}}, {"id": 12, "key": ["Collection Period:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "include_key": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Commission", "Compliance certificate", "Condemnation Proceeds", "eCommerce Laws", "eligible account", "edgar"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 12, "field_name": "Temporary_Collection_Period", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003771, "key": ["Prepayment Period:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "include_key": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Primary Mortgage Insurance", "Principal", "Principal Prepayment", "Principal Remittance Amount", "Principal Balance Shortfall", "Principal Distribution Amount"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003771, "field_name": "Prepayment_Period_Language", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Temporary_Collection_Period", "is_empty": true}, "string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003770, "key": ["Prepayment_Period_Date_Range"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "max_page_to_search": 1, "end_identifier": [""], "start_identifier": [""], "field_id": 3003770, "field_name": "Prepayment_Period_Date_Range", "document_id": 8011139, "output_format": {"llm_operations_output_format": {"text_field_name": "Prepayment_Period_Language"}}}, {"id": 3003772, "key": ["Rating Agency:"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": true, "end_identifier": [", or any", "Rating Agency Condition", "rating agency information", "rating agency confirmation"], "start_identifier": [""], "field_id": 3003772, "field_name": "Rating_Agencies", "document_id": 8011139, "output_format": {"llm_operations_output_format": {"custom": false}}}, {"id": 3003777, "key": ["Realized Loss:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Record Date", "Reconciled Market Value", "Redemption Price", "Redemption Notice", "Registered Holder", "Regular Interest"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3003777, "field_name": "Realized_Loss_Definition", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003801, "key": ["SOX", "SSAE", "<PERSON><PERSON><PERSON>", "Statements on Standards for Attestation Engagements"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003801, "field_name": "Annual_SOX/SSAE_Due?", "document_id": 8011139, "output_format": {"string_operations_output_format": {"replace_with_custom_value": "Yes", "set_default_if_empty": "No"}}}, {"id": 3003780, "key": ["Entity_Due_Recoveries_if_Loan_is_Charged_Off"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003780, "field_name": "Entity_Due_Recoveries_if_Loan_is_Charged_Off", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Trust"}}}, {"id": 3003816, "key": ["Modifications-does_MS_have_to_purchase"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003816, "field_name": "Modifications-does_MS_have_to_purchase", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No"}}}, {"id": 3003817, "key": ["Modifications-does_MS_have_to_purchase_Language"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003817, "field_name": "Modifications-does_MS_have_to_purchase_Language", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003818, "key": ["VA-buy_down_language_Trust"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003818, "field_name": "VA-buy_down_language_Trust", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No Requirement"}}}, {"id": 3003819, "key": ["Master_Servicing_Other_Purchase_Options"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003819, "field_name": "Master_Servicing_Other_Purchase_Options", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003822, "key": ["Any_Other_Requirements_Out_of_the_Ordinary"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003822, "field_name": "Any_Other_Requirements_Out_of_the_Ordinary", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "None"}}}, {"id": 3003844, "key": ["Additional_Info_Notes"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003844, "field_name": "Additional_Info_Notes", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003896, "key": ["Termination_Notes"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003896, "field_name": "Termination_Notes", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3003895, "key": ["Who_Does_Master_Servicer_Inform_of_a_MS_EOD"], "direction": "right", "type": "text", "return_type": "text", "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003895, "field_name": "Who_Does_Master_Servicer_Inform_of_a_MS_EOD", "document_id": 8011139, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 13, "key": ["TABLE OF CONTENTS"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 13, "field_name": "Temporary_Table_of_contents_Starts_Field", "document_id": 8011139}, {"id": 13, "key": ["ARTICLE II", "ARTICLE TWO"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "page_range_before": -2, "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 13, "field_name": "Temporary_Article_II_Starts_Field", "document_id": 8011139}, {"id": 14, "key": ["ARTICLE IX", "ARTICLE NINE"], "direction": "down", "type": "text", "return_type": "text", "use_match": "exact_match", "probable_place": "individual", "use_prev_field_page_no": true, "save_page_no_for_next_key": true, "include_key": true, "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 14, "field_name": "Temporary_Article_IX_Starts_Field", "document_id": 8011139}, {"id": 15, "key": ["Merger or Consolidation of the Master Servicer", "Merger or Consolidation of Master Servicer", "Merger or Consolidation"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Resignation or Removal of Master Servicer", "Resignation of The Master Servicer", "Resignation of Master Servicer", "Section 9.05", "Section 10.", "Assignment or Delegation of Duties"], "start_identifier": [""], "field_id": 15, "field_name": "Temporary_Merger_or_Consolidation", "document_id": 8011139, "sub_keys": ["mr_cooper_ind_merger_consolidation_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003863, "key": ["Resignation of The Master Servicer", "Resignation or Removal of the Master Servicer", "Resignation or Removal of Master Servicer", "Resignation of Master Servicer"], "direction": "down_multi_page", "use_match": "fuzzy", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Assignment or Delegation of Duties", "Assignment or Delegation of Duties by the Master Servicer", "Section 10.", "Section 9.07"], "start_identifier": [""], "field_id": 3003863, "field_name": "MS_Resignation_Language", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003839, "key": ["Assignment or Delegation of Duties by the Master Servicer"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Limitation on Liability of the Master Servicer", "Section 9.08", "Indemnification"], "start_identifier": [""], "field_id": 3003839, "field_name": "Master_Servicer_Assignment_Language", "document_id": 8011139, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3003836, "key": ["Assign_Rights_to_Master_Servicing_Comp_language"], "direction": "right", "type": "text", "return_type": "text", "use_prev_field_page_no": true, "max_page_to_search": 1, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3003836, "field_name": "Assign_Rights_to_Master_Servicing_Comp_language", "document_id": 8011139, "output_format": {"copy_extraction_items_output_format": {"swap_with": "Master_Servicer_Assignment_Language", "is_empty": true}}}, {"id": 16, "key": ["MISCELLANEOUS PROVISIONS"], "direction": "down_multi_page", "include_key": false, "type": "text", "return_type": "text", "use_match": "exact_match", "multi_page_value": true, "exclude_footer": true, "use_prev_field_page_no": true, "max_page_limit": 8, "multi_line_value": true, "end_identifier": ["Voting Rights", "Provision of Information"], "start_identifier": [""], "field_id": 16, "field_name": "Temporary_Amendment_Restrictions", "document_id": 8011139, "sub_keys": ["mr_cooper_ind_amendment_information"], "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}]