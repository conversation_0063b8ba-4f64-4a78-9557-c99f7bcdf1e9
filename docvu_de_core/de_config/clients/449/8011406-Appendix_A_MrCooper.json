[{"id": 3006878, "key": ["“Commission", "“Master Servicer Compensating Interest Payments”:", "Master Servicer Compensating Interest Payments:", "“MS Compensating Interest Payments”:", "MS Compensating Interest Payments:", "“Master Servicer Compensating Interest Payment”:", "Master Servicer Compensating Interest Payment:", "“MS Compensating Interest Payment”:", "MS Compensating Interest Payment:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Condemnation Proceeds", "Master Servicer Remittance Date", "Master Servicer Event of Default", "Master Servicer Report Date", "Master Servicer Remittance Date", "Maturity"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006878, "field_name": "MS_Compensating_Int_Payments", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3006871, "key": ["“Indenture Trustee”:", "Indenture Trustee:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_line_value": false, "end_identifier": [", a", "Indenture Trustee Fee"], "start_identifier": [""], "field_id": 3006871, "field_name": "Indenture_Trustee", "document_id": 8011406, "output_format": {"ner_service_req_output_format": {"text_field_name": "Indenture_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3006879, "key": ["“Owner”:", "Owner:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Owner Trustee", "Paying Agent", "Ownership Interest", "Payahead"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006879, "field_name": "Owner", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"ner_service_req_output_format": {"text_field_name": "Owner", "label": ["ORGANIZATION"]}}}, {"id": 3006880, "key": ["“Owner Trustee”:", "Owner Trustee:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Owner <PERSON>ee <PERSON><PERSON>", "Ownership Interest"], "start_identifier": [""], "field_id": 3006880, "field_name": "Owner_Trustee", "document_id": 8011406, "output_format": {"ner_service_req_output_format": {"text_field_name": "Owner_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3006886, "key": ["Securities Administrator", "Collateral Trustee and Paying Agent"], "type": "text", "return_type": "text", "direction": "up", "probable_place": "just_above", "use_prev_key_page_no": true, "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006886, "field_name": "Securities_Admin/Paying_Agent", "document_id": 8011406}, {"id": 3006888, "key": ["Servicing Administrator"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3006888, "field_name": "Servicing_Administrator", "document_id": 8011406}, {"id": 3006891, "key": ["Trustee"], "type": "text", "return_type": "text", "use_match": "fuzzy", "direction": "up", "probable_place": "just_above", "multi_line_value": false, "use_prev_key_page_no": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3006891, "field_name": "Trustee (Include_Resident_Trustee)", "document_id": 8011406}, {"id": 3006857, "key": ["“Administrator”:", "Administrator:", "“Trust Administrator”:", "Trust Administrator:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Advance", "Adverse Grantor Trust Event", "Adverse", "Affiliate"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006857, "field_name": "Administrator/Trust_Administrator", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"ner_service_req_output_format": {"text_field_name": "Administrator/Trust_Administrator", "label": ["ORGANIZATION"]}}}, {"id": 3006858, "key": ["“Affiliate”:", "Affiliate:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Annual Cap", "Aggregate Voting Interests", "Aggregate Expense Rate", "Aggregate Servicing Fee", "Agent Member", "Aggregate Stated Principal <PERSON>"], "start_identifier": ["Affiliate"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006858, "field_name": "Definition_of_Affiliates", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3006859, "key": ["“Ancillary Income”:", "Ancillary Income:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Aggregate Voting Interests", "Aggregate Expense Rate", "Aggregate Servicing Fee", "Agent Member", "Aggregate Stated Principal <PERSON>"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006859, "field_name": "Ancillary_Income", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3006860, "key": ["“Closing Date”:", "Closing Date:"], "direction": "inline", "type": "date", "return_type": "date", "include_key": true, "probable_place": "individual", "use_match": "fuzzy", "multi_line_value": false, "end_identifier": [""], "start_identifier": [""], "field_id": 3006860, "field_name": "Closing_Date", "document_id": 8011406}, {"id": 3006863, "key": ["Computershare Custodial <PERSON>e", "“<PERSON>ustodian”:", "Custodian:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Custodian Certification", "Compensating Interest", "<PERSON><PERSON><PERSON><PERSON>", "Cut-off Date", "Debt Service Reduction"], "start_identifier": ["<PERSON><PERSON><PERSON><PERSON>"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006863, "field_name": "Custodian_Names", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"ner_service_req_output_format": {"text_field_name": "Custodian_Names", "label": ["ORGANIZATION"]}}}, {"id": 3006864, "key": ["“Delaware Trustee”:", "Delaware Trustee:"], "type": "text", "return_type": "text", "direction": "right", "multi_line_value": false, "probable_place": "individual", "end_identifier": ["Delegated Authority", "Delinquency Trigger", "Delaware Trustee Fee"], "start_identifier": [""], "field_id": 3006864, "field_name": "Delaware_Trustee", "document_id": 8011406, "output_format": {"ner_service_req_output_format": {"text_field_name": "Delaware_Trustee", "label": ["ORGANIZATION"]}}}, {"id": 3006866, "key": ["“Depositor”:", "Depositor:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "multi_line_value": false, "probable_place": "individual", "use_match": "fuzzy", "end_identifier": ["Determination Date"], "start_identifier": [""], "field_id": 3006866, "field_name": "Depositor", "document_id": 8011406, "output_format": {"ner_service_req_output_format": {"text_field_name": "Depositor", "label": ["ORGANIZATION"]}}}, {"id": 3006867, "key": ["“Determination Date”:", "Determination Date:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": false, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Designated Depository Institution", "Directing Noteholders", "Distributed Ledger Agent", "Disqualified Organization"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006867, "field_name": "Determination_Date", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent", "remove_from_beginning": ["Determination Date"], "remove_special_chars_from_beginning": true}}}, {"id": 3006868, "key": ["“Distribution Date”:", "Distribution Date:", "“Payment Date”:", "Payment Date:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "include_key": true, "multi_page_value": true, "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "multi_line_value": true, "end_identifier": ["Percentage Interest", "PennyMac Servicer", "Distribution Date Statement", "Distribution Compliance Period", "Due Date", "Due Period", "Dodd-Frank Act", "PCAOB"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006868, "field_name": "Distribution_Date/Payment_Date", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["Payment Date"], "remove_special_chars_from_beginning": true}}}, {"id": 3006869, "key": ["“Due period”:", "Due period:"], "direction": "down_multi_page", "type": "text", "return_type": "text", "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "include_key": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Electronic Form", "eCommerce Laws", "Eligible", "eligible account", "edgar", "EC Trust"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006869, "field_name": "Due_Period", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3006870, "key": ["“Eligible Institution:”:", "Eligible Institution:", "Qualified Depository:", "“Qualified Depository”:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 3, "multi_line_value": true, "end_identifier": ["Eligible Investments", "Eligible Deposit Account", "Eligible Substitute Mortgage Loan", "EPD event", "erisa"], "start_identifier": ["Eligible Account"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006870, "field_name": "Eligible_Institution_Language", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "alternate_locations": [{"id": 3006870, "key": ["“Eligible Account”:", "Eligible Account:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 3, "multi_line_value": true, "end_identifier": ["Eligible Investments", "Eligible Deposit Account", "EPD event", "erisa"], "start_identifier": ["Eligible Account"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006870, "field_name": "Eligible_Institution_Language", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}]}, {"id": 3006872, "key": ["Issuer or Trust:", "“Issuer”:", "Issuer:", "“Issuing Entity”:", "Issuing Entity:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Issuer Request", "Item 1123", "KBRA", "Latest Possible Maturity Date", "Liquidat", "Issuing REMIC", "JMAC"], "start_identifier": [""], "field_id": 3006872, "field_name": "Issuer", "document_id": 8011406, "output_format": {"ner_service_req_output_format": {"text_field_name": "Issuer", "label": ["ORGANIZATION"]}}}, {"id": 3006874, "key": ["“Loan Data Agent”:", "Loan Data Agent:"], "direction": "right", "type": "text", "return_type": "text", "include_key": true, "probable_place": "individual", "multi_line_value": true, "end_identifier": ["Fee", "Loan-To-Value Ratio", "Loan Data Agent <PERSON>e", "Loan Data Agreement", "Lower-Tier", "Loan Data Agent Agreement"], "start_identifier": [""], "field_id": 3006874, "field_name": "Loan_Data_Agent", "document_id": 8011406, "output_format": {"ner_service_req_output_format": {"text_field_name": "Loan_Data_Agent", "label": ["ORGANIZATION"]}}}, {"id": 3006877, "key": ["“Master Servicing Fee Rate”:", "“Master Servicer Fee Rate”:", "Master Servicer Fee Rate:", "Master Servicing Fee Rate:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Maximum Rate", "Material Test Failure", "Master Servicing Officer", "Master Servicing Transfer Costs", "Moody’s", "Master Servicer Remittance Date", "Maturity"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006877, "field_name": "Master_Servicing_Fee_Definition/MS_Rate", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3006881, "key": ["“Eligible Investments”:", "Eligible Investments:", "“Permitted Investments”:", "Permitted Investments:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 3, "multi_line_value": true, "end_identifier": ["Eminent Domain Expenses", "ERISA", "Escrow", "EPD event", "Event of Default", "Permitted Transferee", "Person"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006881, "field_name": "Permitted_Investments", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3006882, "key": ["“Prepayment Interest Shortfall”:", "Prepayment Interest Shortfall:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Prepayment Premium", "Primary Mortgage Insurance Policy", "Prepayment Period"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006882, "field_name": "Prepayment_Interest_Shortfall", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "No Requirement"}}}, {"id": 3006884, "key": ["“Prepayment Period”:", "Prepayment Period:", "Collection Period"], "direction": "down_multi_page", "type": "text", "return_type": "text", "probable_place": "individual", "exclude_footer": true, "max_page_limit": 1, "include_key": true, "multi_line_value": true, "multi_page_value": true, "end_identifier": ["Primary Mortgage Insurance Policy", "Prepayment Premium", "Principal <PERSON><PERSON><PERSON><PERSON>", "Primary Mortgage Insurance", "Principal Prepayment", "Principal Remittance Amount", "Principal Balance Shortfall", "Principal Distribution Amount", "Commission"], "start_identifier": [""], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006884, "field_name": "Prepayment_Period_Language", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}, "output_format": {"string_operations_output_format": {"set_default_if_empty": "Silent"}}}, {"id": 3006885, "key": ["Rating Agencies", "“Rating Agency”:", "Rating Agency:"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": true, "end_identifier": [", or any", "rating agency information", "rating agency confirmation", "Rated Notes", "Rating Agency Condition"], "start_identifier": [""], "field_id": 3002428, "field_name": "Rating_Agencies", "document_id": 8011406, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["Rating Agency"], "remove_keywords": ["Rating Agency"]}, "ner_service_req_output_format": {"text_field_name": "Rating_Agencies", "label": ["ORGANIZATION"]}}}, {"id": 3006889, "key": ["17g-5 of the Exchange Act.", "Realized Losses", "“Realized Loss”:", "Realized Loss:"], "direction": "down_multi_page", "include_key": true, "type": "text", "return_type": "text", "multi_page_value": true, "exclude_footer": true, "probable_place": "individual", "max_page_limit": 2, "multi_line_value": true, "end_identifier": ["Realized Loss Allocation Amount", "Record Date", "Reconciled Market Value", "Redemption Price", "Registered Holder", "Record Date:"], "start_identifier": ["Exchange Act", "Realized Losses"], "individual_end_identifier": true, "hpos_aligned_end_identifier": true, "field_id": 3006889, "field_name": "Realized_Loss_Definition", "document_id": 8011406, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3006890, "key": ["“Sponsor”:", "Sponsor:"], "direction": "right", "type": "text", "return_type": "text", "probable_place": "individual", "multi_line_value": false, "end_identifier": ["SPS", "Startup Day", "SR 202", "Stated Principal <PERSON>"], "start_identifier": [""], "field_id": 3006890, "field_name": "Sponsor", "document_id": 8011406, "output_format": {"ner_service_req_output_format": {"text_field_name": "Sponsor", "label": ["ORGANIZATION"]}}}]