[{"id": 71111832, "key": ["PASSPORT"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [], "field_id": 71111832, "field_name": "********* - Bo<PERSON><PERSON> 1 - PassPort Number", "document_id": 9000486}, {"id": 71111833, "key": ["PASSPORT"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [], "field_id": 71111833, "field_name": "71111833 - Borrower 1 - Issue Date", "document_id": 9000486, "output_format": {"date_parser_output_format": {"date_position": 2}}}, {"id": 71111834, "key": ["PASSPORT"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [], "field_id": 71111834, "field_name": "71111834 - Borrower 1 - Expiration Date", "document_id": 9000486, "output_format": {"date_parser_output_format": {"date_position": 3}}}, {"id": 71111835, "key": ["Authority"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["USA"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 71111835, "field_name": "71111835 - Borrower 1 - Issuing Authority", "document_id": 9000486}]