{"max_upper_block": 3, "use_upper_split_percentage": 0.45, "max_lines_for_header": 5, "max_upper_lines_for_key_search": 8, "debug": true, "default_return": "9000671-note_SEQ.json", "document_types": {"new_form": {"return": "9000671-note_SEQ1.json", "header": {"include_strings": ["REAL ESTATE LIEN NOTE"], "exclude_strings": [], "length_comparison": false}, "body": {"include_strings": ["REAL ESTATE LIEN NOTE"], "exclude_strings": [], "length_comparison": false}}}}