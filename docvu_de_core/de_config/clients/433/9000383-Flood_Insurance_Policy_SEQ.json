[{"id": 71111850, "key": ["Policy Period", "Foundation"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Insured"], "start_identifier": [""], "field_id": 71111850, "field_name": "Expiration_Date_SQ_318744", "document_id": 9000383, "output_format": {"date_parser_output_format": {"date_position": 2}}}, {"id": 71111851, "key": ["Policy Period", "Foundation"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Insured"], "start_identifier": [""], "field_id": 71111851, "field_name": "Issue_Date_SQ_318745", "document_id": 9000383, "output_format": {"date_parser_output_format": {"date_position": 1}}}, {"id": 71111852, "key": ["Flood Risk:", "Flood Zone:", "Flood Zone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["For payment"], "start_identifier": [""], "field_id": 71111852, "field_name": "Flood_Zone_code", "document_id": 9000383}, {"id": 71111853, "key": ["First Mortgagee", "Additional Insured"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Dear Mortgagee", "Reference", "<PERSON>"], "start_identifier": [""], "field_id": 71111853, "field_name": "First_Mortgagee_SQ_318747", "document_id": 9000383, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["Loan 8014011939"]}}}, {"id": 71111854, "key": ["Second Mortgagee"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 71111854, "field_name": "Second_Mortgagee_SQ_318748", "document_id": 9000383}]