[{"id": 71112611, "key": ["COLLATERAL DESCRIPTION"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>"], "start_identifier": [""], "field_id": 71112611, "field_name": "71112611 - Property address - Street", "document_id": 9000377, "output_format": {"address_parser_output_format": {"get_line1": true, "from_field": "71112611 - Property address - Street"}}}, {"id": 71112612, "key": ["COLLATERAL DESCRIPTION"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>"], "start_identifier": [""], "field_id": 71112612, "field_name": "71112612 - Property address - City", "document_id": 9000377, "output_format": {"address_parser_output_format": {"get_city": true, "from_field": "71112612 - Property address - City"}}}, {"id": 71112613, "key": ["COLLATERAL DESCRIPTION"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>"], "start_identifier": [""], "field_id": 71112613, "field_name": "71112613 - Property address - State", "document_id": 9000377, "output_format": {"address_parser_output_format": {"get_state": true, "from_field": "71112613 - Property address - State"}}}, {"id": 71112614, "key": ["COLLATERAL DESCRIPTION"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>"], "start_identifier": [""], "field_id": 71112614, "field_name": "71112614 - Property address - Zip", "document_id": 9000377, "output_format": {"address_parser_output_format": {"get_zip_code": true, "from_field": "71112614 - Property address - Zip"}}}, {"id": 71110934, "key": ["<PERSON><PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 71110934, "field_name": "71110934 - <PERSON><PERSON>er Name", "document_id": 9000377}, {"id": 71110650, "key": ["flood zone", "<PERSON>"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["C. FEDERAL", "Date"], "start_identifier": [""], "field_id": 71110650, "field_name": "71110650 - Flood Zone Code", "document_id": 9000377, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^\\d{1,2}/\\d{1,2}/\\d{2,4}"], "remove_special_chars_from_beginning": true}}}]