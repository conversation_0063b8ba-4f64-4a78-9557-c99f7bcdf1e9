[{"id": 71111844, "key": ["Personal Email Address"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 71111844, "field_name": "71111844 - Bo<PERSON>er 1 - Drivers License Number", "document_id": 9000007}, {"id": 71111847, "key": ["Work Email Address", "Work Email"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 71111847, "field_name": "71111847 - <PERSON><PERSON><PERSON> 1 - <PERSON>", "document_id": 9000007}, {"id": 71111845, "key": ["Personal Email"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 71111845, "field_name": "71111845 - Borrower 1 - Issue Date", "document_id": 9000007}, {"id": 71111840, "key": ["certifications required"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 71111840, "field_name": "71111840 - <PERSON><PERSON>er Signature", "document_id": 9000007, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 71111841, "key": ["certifications required"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [], "field_id": 71111841, "field_name": "71111841 - Co-Borrower Signature", "document_id": 9000007, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 71111843, "key": ["Issue Date ("], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON> Maiden", "<PERSON>'s Maiden"], "start_identifier": [], "field_id": 71111843, "field_name": "71111843 - <PERSON><PERSON><PERSON><PERSON><PERSON>'s Mother Maiden Name", "document_id": 9000007}, {"id": 71111846, "key": ["Personal Email"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 71111846, "field_name": "71111846 - Borrower 1 - Expiration Date", "document_id": 9000007}, {"id": 71111842, "key": ["Personal Email"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 71111842, "field_name": "71111842 - <PERSON><PERSON><PERSON>'s Mother Maiden Name", "document_id": 9000007}]