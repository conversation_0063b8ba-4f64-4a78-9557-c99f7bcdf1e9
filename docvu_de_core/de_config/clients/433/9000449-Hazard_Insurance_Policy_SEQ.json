[{"id": 71110938, "key": ["Policy Effective Date"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Company Name"], "start_identifier": [], "possible_page_numbers": [], "field_id": 71110938, "field_name": "71111855 - Issue Date", "document_id": 9000449}, {"id": 71111855, "key": ["Policy Expiration Date"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [], "possible_page_numbers": [], "field_id": 71111855, "field_name": "71110938 - Expiration Date", "document_id": 9000449}, {"id": 71111856, "key": ["MORTGAGEE INFORMATION"], "type": "table_new", "return_type": "table", "end_identifier": ["ADDITIONAL INSURED INFORMATION"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 71111856, "field_name": "71111856 - First Mortgagee", "document_id": 9000449, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "First", "field_string_col": "Mortgagee Name", "probable_field_value_col": "Mortgagee Name", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Number", "Mortgagee Name", "Address", "City, State, Zip", "Loan Number", "Billed"], "table_structure": "vertical"}, {"id": 71111857, "key": ["MORTGAGEE INFORMATION"], "type": "table_new", "return_type": "table", "end_identifier": ["ADDITIONAL INSURED INFORMATION"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 71111857, "field_name": "71111857 - Second Mortgagee", "document_id": 9000449, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Second", "field_string_col": "Mortgagee Name", "probable_field_value_col": "Mortgagee Name", "probable_place_in_table": "1", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Number", "Mortgagee Name", "Address", "City, State, Zip", "Loan Number", "Billed"], "table_structure": "horizontal"}]