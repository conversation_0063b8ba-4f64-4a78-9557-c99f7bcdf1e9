[{"id": ********, "key": ["Loan#:", "Loan#", "Loan", "Lcan#:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "possible_page_numbers": [], "field_id": ********, "field_name": "******** - Loan Number", "document_id": 9000019}, {"id": ********, "key": ["Name on Account:", "Name on Account"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["["], "start_identifier": [], "possible_page_numbers": [], "field_id": ********, "field_name": "******** - Name on Account", "document_id": 9000019, "output_format": {"name_parser_output_format": {"get_account_name": true, "from_field": "******** - Name on Account"}}}, {"id": ********, "key": ["Bank Name:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "possible_page_numbers": [], "field_id": ********, "field_name": "******** - Bank Name", "document_id": 9000019, "output_format": {"name_parser_output_format": {"get_account_name": true, "from_field": "******** - Bank Name"}}}, {"id": ********, "key": ["Routing Number:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Routing Number", "Bank"], "start_identifier": [], "possible_page_numbers": [], "field_id": ********, "field_name": "******** - Routing Number", "document_id": 9000019}, {"id": ********, "key": ["Account Number:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Payment"], "start_identifier": [], "possible_page_numbers": [], "field_id": ********, "field_name": "******** - Account Number", "document_id": 9000019}, {"id": ********, "key": ["Payment Amount Due:"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "possible_page_numbers": [], "field_id": ********, "field_name": "******** - Amount <PERSON>", "document_id": 9000019, "alternate_locations": [{"id": ********, "key": ["Amount Due:"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "possible_page_numbers": [], "field_id": ********, "field_name": "******** - Amount <PERSON>", "document_id": 9000019, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"], "remove_spaces": true}}}]}, {"id": ********, "key": ["Additional Principal:", "Additional Principal"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "possible_page_numbers": [], "field_id": ********, "field_name": "******** - Additional Principal", "document_id": 9000019}, {"id": 71111886, "key": ["TOTAL PAYMENT:", "TOTAL PAYMENT"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "possible_page_numbers": [], "field_id": 71111886, "field_name": "71111886 - Total Payment", "document_id": 9000019, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"], "remove_spaces": true}}}, {"id": 100, "key": ["Sign Up For Autopay"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Amount Due:", "Amount Due"], "start_identifier": [], "possible_page_numbers": [], "field_id": 100, "field_name": "ACH_Authorization_form1", "document_id": 9000019, "sub_keys": ["ACH_Authorization_form_save1"]}]