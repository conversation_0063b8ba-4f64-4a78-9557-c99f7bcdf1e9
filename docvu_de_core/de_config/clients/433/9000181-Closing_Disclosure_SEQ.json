[{"id": 71111827, "key": ["Phone", "Email"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 71111827, "field_name": "71111827 - Borrower 1 - B/C Signature", "document_id": 9000181, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 71110501, "key": ["Date Issued"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 71110501, "field_name": "71110501 - Borrower 1 - Date Issued", "document_id": 9000181, "output_format": {"date_parser_output_format": {"date_position": 1}}}, {"id": 71110502, "key": ["Closing Date"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Settlement Agent"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 71110502, "field_name": "71110502 - <PERSON><PERSON><PERSON> 1 - Closing Date", "document_id": 9000181, "output_format": {"date_parser_output_format": {"date_position": 1}}}, {"id": 71110500, "key": ["Closing Date"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Settlement Agent"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 71110500, "field_name": "71110500 - Borrower 1 - Disbursement Date", "document_id": 9000181, "output_format": {"date_parser_output_format": {"date_position": 2}}}]