[{"id": 80003316, "key": ["Based on a complete visual", "Basedonacompletevisual", "Sated on acomplete visual Inspection", "Bated on acotnplete visual", "the subject property, defined", "Appraisal Update and/or Completion Report", "REPORT) TO BE"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": true, "end_identifier": [", as of", "asof", " as of", " ,as of", "APPRAISER:"], "start_identifier": [], "possible_page_numbers": [3], "field_id": 80003316, "field_name": "Property_Origination_Appraisal_Value_VA_325502", "document_id": 9000069, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true}}}, {"id": 80003315, "key": ["Based on a complete visual", "Basedonacompletevisual", "Sated on acomplete visual Inspection", "Bated on acotnplete visual", "SUBJECT OF THIS REPORT, AS OF", "subject of this report is", "real property"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "end_identifier": ["<PERSON>"], "start_identifier": [""], "field_id": 80003315, "field_name": "property_origination_appraisal_date_VA_319089", "document_id": 9000069}, {"id": 80011783, "key": ["Project Name and", "Verification Source(s)"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": ["Above Grade Total", "Above Grade TotalBdrms", "Above GradeTotal", "Above Grade Total Bdrms"], "start_identifier": ["ConditionC4", "Condition"], "additional_search_criteria": {"expected_format": "C[0-9]"}, "possible_page_numbers": [], "field_id": 80011783, "field_name": "Property_Condition_DFCU_326124", "document_id": 9000069, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^ConditionC?"]}}}, {"id": 80001102, "key": ["Based on a complete visual"], "direction": "right", "probable_type": "Footer", "type": "amount", "return_type": "text", "multi_line_value": true, "end_identifier": ["which is the", ", as of", ", asof"], "start_identifier": ["subject of this report is"], "possible_page_numbers": [], "field_id": 80001102, "field_name": "Current_Appraised_Amount_DFCU_326126", "document_id": 9000069, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^[^0-9]+", "\\s*\\$", "^\\$"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*,?\\s*as\\s*of.*", "\\s*,?\\s*asof.*", "[,\\s]+$"]}}}, {"id": 80001102, "key": ["Based on a complete visual"], "direction": "right", "probable_type": "Footer", "type": "date", "return_type": "text", "multi_line_value": true, "end_identifier": ["effective date of this appraisal"], "start_identifier": [], "possible_page_numbers": [], "field_id": 80001102, "field_name": "Current_Appraised_Date_DFCU_326125", "document_id": 9000069}, {"id": 80000828, "key": ["Date of Signature and Report"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": true, "end_identifier": ["LENDER/CLIENT"], "start_identifier": ["APPRAISED VALUE OF SUB<PERSON>ECT PROPERTY"], "possible_page_numbers": [], "field_id": 80000828, "field_name": "PROPERTY VALUE DFCU326123", "document_id": 9000069, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^[^0-9]+", "\\s*\\$", "^\\$"], "remove_special_chars_from_beginning": true, "remove_from_end": ["[^0-9]+$", "[,\\s]+$"]}}}]