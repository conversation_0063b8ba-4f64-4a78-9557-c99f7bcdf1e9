[{"id": 80000964, "key": ["MIN:", "NUN:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["NOTE", "Loan Number", "Property Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000964, "field_name": "MERS/MIN #", "document_id": 9000671}, {"id": 80012037, "key": ["Note"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER'S PROMISE TO PAY", "<PERSON><PERSON><PERSON>'s Promise"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80012037, "field_name": "Origination_Date_VA_326034", "document_id": 9000671, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 80000849, "key": ["Note"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER'S PROMISE TO PAY", "<PERSON><PERSON><PERSON>'s Promise"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000849, "field_name": "note_document_date_VA_319119", "document_id": 9000671, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 80000852, "key": ["Property Address:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["whose address is"], "start_identifier": [""], "field_id": 80000852, "field_name": "lender_originator_company_name_VA_319118", "document_id": 9000671, "output_format": {"ner_service_req_output_format": {"text_field_name": "lender_originator_company_name_VA_319118", "label": ["ORGANIZATION"]}}}, {"id": 80000851, "key": ["1. Interest"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["interest basis"], "start_identifier": ["Percent", "Pcrccnt"], "field_id": 80000851, "field_name": "Original_Interest_Rate_VA_325503", "document_id": 9000671, "output_format": {"number_parser_output_format": {"extract_multiple": false, "pattern_keys": ["percent_or_rate"]}, "string_operations_output_format": {"remove_spaces": true}}}, {"id": 80000852, "key": ["3.PAYMENTS", "3. PAYMENTS", "2. Payment Obligations", "2.Payment Obligations"], "direction": "down_block", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["4. B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4.B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4. BORROWER'S FAILURE", "4.BORROWER'S FAILURE", "4. <PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY", "4.<PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY", "2.2. Balloon Payment"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000852, "field_name": "First_Payment_Date_VA_325505", "document_id": 9000671, "output_format": {"date_parser_output_format": {"date_position": 1}}}, {"id": 80000850, "key": ["Property Address:"], "direction": "down_block", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["1. Interest", "1.Interest"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000850, "field_name": "Original_Loan_Amount_VA_326037", "document_id": 9000671, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S", "\\)"], "remove_spaces": true}}}, {"id": 80001196, "key": ["2. Payment Obligations"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["interest due"], "start_identifier": [], "field_id": 80001196, "field_name": "Original_P_I_VA_326038", "document_id": 9000671, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"], "remove_spaces": true}}}, {"id": 80000853, "key": ["3.PAYMENTS", "3. PAYMENTS", "2. Payment Obligations", "2.Payment Obligations"], "direction": "down_block", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["4. B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4.B<PERSON><PERSON><PERSON>ER'S RIGIFT", "4. BORROWER'S FAILURE", "4.BORROWER'S FAILURE", "4. <PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY", "4.<PERSON><PERSON><PERSON><PERSON><PERSON>'S RIGHT TO PREPAY", "2.2. Balloon Payment"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 80000853, "field_name": "Original_Maturity_Date_VA_325504", "document_id": 9000671, "output_format": {"date_parser_output_format": {"date_position": 2}}}, {"id": 80011111, "key": ["Property Address:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["1. Interest."], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80011111, "field_name": "Property_Address_1_VA_326031", "document_id": 9000671, "output_format": {"address_parser_output_format": {"get_line1_line2_city_state_zip": true, "from_field": "Property_Address_1_VA_326031"}}}, {"id": 80000856, "key": ["B<PERSON>ROWER'S FAILURE TO PAY AS REQUIRED"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>", "(B) De<PERSON>ult"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 80000856, "field_name": "Late_Charge_Rate_VA_326040", "document_id": 9000671, "output_format": {"string_operations_output_format": {"contains": ["(\\d+\\.?\\d*)\\s*%"], "remove_from_end": ["\\s*%"], "remove_spaces": true}}}, {"id": 80000855, "key": ["if any payment"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Late Charge"], "start_identifier": [""], "field_id": 80000855, "field_name": "Late Charge Grace Days", "document_id": 9000671, "output_format": {"word_to_number_output_format": {}, "string_operations_output_format": {"contains": ["\\b(\\d+)\\b"], "remove_from_end": []}}}, {"id": 80001186, "key": ["if any payment"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Late Charge"], "start_identifier": [""], "field_id": 80001186, "field_name": "Late Charge Type", "document_id": 9000671, "output_format": {"Latechargetypeoutputformat": {"from_field": "Late Charge Type", "get_percentage": true, "get_amount": true}}}, {"id": 80012036, "key": ["Assignee"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": ["beginning on"], "possible_page_numbers": [1], "field_id": 80012036, "field_name": "Assignee_VA_326033", "document_id": 9000671}, {"id": 80012039, "key": ["Note Has Endorsement"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80012039, "field_name": "Note_Has_Endorsement_VA_326035", "document_id": 9000671}, {"id": 80012040, "key": ["Trustee"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["of"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80012040, "field_name": "Trustee_VA_326036", "document_id": 9000671}]