{"max_upper_block": 3, "use_upper_split_percentage": 0.45, "max_lines_for_header": 5, "max_upper_lines_for_key_search": 8, "debug": true, "_comment": "Enhanced multi-variant classification for Client 440", "enable_multi_variant": true, "confidence_threshold": 0.6, "enable_confidence_scoring": true, "case_sensitive_matching": false, "partial_string_matching": true, "use_legacy_mode": false, "_comment_priority": "Priority order for document classification", "variant_priority_order": ["1003_application_new", "1003_application_old", "closing_disclosure", "appraisal_report", "deed_of_trust", "note_document"], "default_return": "9000001-1003_de_old.json", "document_types": {"1003_application_new": {"return": "9000001-1003_de.json", "header": {"include_strings": ["1003", "UNIFORM RESIDENTIAL LOAN APPLICATION", "personal information", "financial information", "SECTION I", "BORROWER INFORMATION"], "exclude_strings": ["closing disclosure", "appraisal", "deed of trust", "note"]}, "body": {"include_strings": ["personal information", "financial information", "<PERSON><PERSON><PERSON>", "Co-<PERSON><PERSON><PERSON>", "Property Address", "<PERSON><PERSON>", "Purpose of <PERSON>an"], "exclude_strings": []}, "big_font": {"include_strings": ["1003", "LOAN APPLICATION", "UNIFORM RESIDENTIAL"], "exclude_strings": [], "height_threshold": 0.6, "num_clusters": 3}}, "1003_application_old": {"return": "9000001-1003_de_old.json", "header": {"include_strings": ["1003", "loan application", "personal information", "OLD VERSION", "PREVIOUS FORM"], "exclude_strings": ["NEW VERSION", "REVISED", "closing disclosure"]}, "body": {"include_strings": ["personal information", "borrower name", "property information"], "exclude_strings": []}}, "closing_disclosure": {"return": "9000181-closing_disclosure_de.json", "header": {"include_strings": ["Closing Disclosure", "closing information", "Final Terms", "Loan <PERSON>", "Projected Payments", "CLOSING DISCLOSURE"], "exclude_strings": ["1003", "loan application", "appraisal", "deed of trust"]}, "body": {"include_strings": ["closing information", "<PERSON><PERSON>", "Interest Rate", "Monthly Principal", "Closing Costs", "Cash to Close", "closing costs"], "exclude_strings": []}}, "appraisal_report": {"return": "9000069-appraisal_de.json", "header": {"include_strings": ["Appraisal Report", "Property Valuation", "Market Value", "URAR", "APPRAISAL", "property appraisal"], "exclude_strings": ["1003", "loan application", "closing disclosure", "deed of trust"]}, "body": {"include_strings": ["Subject Property", "Comparable Sales", "Market Analysis", "Property Description", "Appraised Value", "property value"], "exclude_strings": []}}, "deed_of_trust": {"return": "9000253-deed_of_trust_de.json", "header": {"include_strings": ["Deed of Trust", "DEED OF TRUST", "Security Instrument", "SECURITY INSTRUMENT", "deed of trust"], "exclude_strings": ["1003", "loan application", "closing disclosure", "appraisal"]}, "body": {"include_strings": ["Trustor", "Trustee", "Beneficiary", "Property Description", "security instrument", "deed of trust"], "exclude_strings": []}}, "note_document": {"return": "9000671-note_de.json", "header": {"include_strings": ["Promissory Note", "NOTE", "PROMISSORY NOTE", "note document"], "exclude_strings": ["1003", "loan application", "closing disclosure", "appraisal", "deed of trust"]}, "body": {"include_strings": ["Principal Amount", "Interest Rate", "Payment Terms", "Maturity Date", "promissory note"], "exclude_strings": []}}}}