[{"id": 101, "key": ["Legal"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["original cost"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 103, "field_name": "occupancy_checkbox_save_section", "sub_keys": ["occupancy_old_omr"], "document_id": 9000001}, {"id": 20000, "key": ["<PERSON><PERSON><PERSON>'s Name"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Social Security Number"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 20000, "field_name": "Borrower_Name", "document_id": 9000001}, {"id": 3006851, "key": ["<PERSON><PERSON><PERSON>'s Name"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Social Security Number"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3006851, "field_name": "borrower_entity_first_name", "document_id": 9000001, "output_format": {"name_parser_output_format": {"get_first_name": true, "from_field": "Borrower_Name"}}}, {"id": 40000, "key": ["Co-<PERSON><PERSON><PERSON>'s Name"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Social Security"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 40000, "field_name": "Co_Borrower_Name", "document_id": 9000001}, {"id": 3006852, "key": ["Co-<PERSON><PERSON><PERSON>'s Name"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Social Security"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3006852, "field_name": "co-borrower_entity_first_name", "document_id": 9000001, "output_format": {"name_parser_output_format": {"get_first_name": true, "from_field": "Co_Borrower_Name"}}}, {"id": 80001336, "key": ["DOB", "<PERSON><PERSON><PERSON>'s Name", "Home Phone"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Married"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80001336, "field_name": "Borrower Date of Birth", "document_id": 9000001}, {"id": 80000817, "key": ["DOB", "<PERSON><PERSON><PERSON>'s Name", "Home Phone"], "direction": "down", "type": "number", "return_type": "number", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Married"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000817, "field_name": "Borrower_SSN", "document_id": 9000001, "output_format": {"number_parser_output_format": {"extract_multiple": false, "pattern_keys": ["ssn"]}}}, {"id": 80000818, "key": ["DOB", "<PERSON><PERSON><PERSON>'s Name", "Home Phone"], "direction": "down", "type": "number", "return_type": "number", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Married"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000818, "field_name": "Co-Borrower_SSN", "document_id": 9000001, "post_process": {"ssn_post_processor": {"extract_ssns": true}}}, {"id": 80003214, "key": ["DOB", "<PERSON><PERSON><PERSON>'s Name", "Home Phone"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Married"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80003214, "field_name": "Co-Borrower Date of Birth", "document_id": 9000001, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 80000982, "key": ["ACKNOWLEDGEMENT AND AGREEMENT"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["INFORMATION FOR GO<PERSON><PERSON><PERSON>MENT MONITORING PURPOSES"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000982, "field_name": "Application Date", "document_id": 9000001, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 9000, "key": ["Lender must"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Loan Originator"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 9000, "field_name": "demographic_checkbox_save_section", "sub_keys": ["Valon_demographic_checkbox_save_section"], "document_id": 9000001}, {"id": 7000, "key": ["Lender must"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON>an Originator's name"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 7000, "field_name": "co_borrower_demographic_checkbox_save_section", "sub_keys": ["<PERSON><PERSON>_co_borrower_demographic_checkbox_save_section"], "document_id": 9000001}]