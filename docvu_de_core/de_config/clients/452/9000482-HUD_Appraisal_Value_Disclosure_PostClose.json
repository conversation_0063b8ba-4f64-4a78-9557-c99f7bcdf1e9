[{"id": 3000483, "key": ["seller will not"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000483, "field_name": "hud_appraisal_value_disclosure_borrower_sign", "document_id": 9000482, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000486, "key": ["seller will not"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000486, "field_name": "hud_appraisal_value_disclosure_coborrower_sign", "document_id": 9000482, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000484, "key": ["seller will not"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000484, "field_name": "hud_appraisal_value_disclosure_borrower_date", "document_id": 9000482}, {"id": 3000487, "key": ["seller will not"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000487, "field_name": "hud_appraisal_value_disclosure_coborrower_date", "document_id": 9000482, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]