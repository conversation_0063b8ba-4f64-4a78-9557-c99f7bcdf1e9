[{"id": 3000220, "key": ["Appraisal Receipt Form", "WAIVER"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000220, "field_name": "appraisal_waiver_borrower_sign", "document_id": 9000075, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000223, "key": ["Co-Borrower Signature", "Co-<PERSON><PERSON><PERSON>"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000223, "field_name": "appraisal_waiver_coborrower_sign", "document_id": 9000075, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000221, "key": ["Appraisal Receipt Form", "WAIVER", "appraisal_waiver", "Additionally"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000221, "field_name": "appraisal_waiver_borrower_date", "document_id": 9000075, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000224, "key": ["Co-Borrower Signature", "Co-<PERSON><PERSON><PERSON>"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000224, "field_name": "appraisal_waiver_coborrower_date", "document_id": 9000075, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]