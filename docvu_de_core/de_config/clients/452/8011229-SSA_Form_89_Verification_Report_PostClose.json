[{"id": 3000474, "key": ["this consent is valid for"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000474, "field_name": "ssa89_form_for_coborrower_sign", "document_id": 8011229, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000475, "key": ["this consent is valid for"], "direction": "down", "type": "text", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Relationship"], "start_identifier": [""], "field_id": 3000475, "field_name": "ssa89_form_for_coborrower_date", "document_id": 8011229, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]