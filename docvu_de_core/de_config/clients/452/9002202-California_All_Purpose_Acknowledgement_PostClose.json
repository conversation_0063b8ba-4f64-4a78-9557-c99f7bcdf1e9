[{"id": 3000198, "key": ["I certify under penalty", "foregoing paragraph", "witness my hand", "witness my hand and official seal"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000198, "field_name": "ca_all_purpose_ackn_notary_commission_expires_date", "document_id": 9002202, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000197, "key": ["before me", "foregoing paragraph"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000197, "field_name": "ca_all_purpose_ackn_notary_seal", "document_id": 9002202, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3000192, "key": ["before me", "beforeme"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Notary", "personally appeared"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000192, "field_name": "ca_all_purpose_ackn_appeared_before", "document_id": 9002202, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "owners_affidavit_appeared_before", "label": ["name"]}}}, {"id": 3000193, "key": ["STATE OF", "State of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000193, "field_name": "ca_all_purpose_ackn_state_filed", "document_id": 9002202, "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true, "remove_from_end": ["}"]}}}, {"id": 3000194, "key": ["COUNTYOF", "County of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000194, "field_name": "ca_all_purpose_ackn_county", "document_id": 9002202, "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true, "remove_from_end": ["}"]}}}, {"id": 3000195, "key": ["COUNTYOF", "County of"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["by", "proved"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000195, "field_name": "ca_all_purpose_ackn_execution_date", "document_id": 9002202, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000196, "key": ["Signature"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000196, "field_name": "ca_all_purpose_ackn_notary_acknowledgment", "document_id": 9002202, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}]