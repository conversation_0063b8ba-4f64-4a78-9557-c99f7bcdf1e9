[{"id": 3000651, "key": ["My commission expires", "This instrument was", "Given under my hand", "Signed and sworn to"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "probable_place": "Individual", "field_id": 3000651, "field_name": "affidavit_of_occupancy_appeared_before", "document_id": 9000041, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "affidavit_of_occupancy_appeared_before", "label": ["name"]}}}, {"id": 3005910, "key": ["sworn to and subscribed before", "Signed and sworn to", "Signed and sworn"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3005910, "field_name": "occupancy_affidavit_printed_commission_expires_date", "document_id": 9000041, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000655, "key": ["Notary Public Signature", "Signature"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000655, "field_name": "affidavit_of_occupancy_notary_acknowledgment", "document_id": 9000041, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000656, "key": ["commission expires", "under my hand", "This instrument", "Signature"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000656, "field_name": "affidavit_of_occupancy_notary_seal", "document_id": 9000041, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3000646, "key": ["agreements"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Page 1"], "start_identifier": [""], "field_id": 3000646, "field_name": "affidavit_of_occupancy_borrower_sign", "document_id": 9000041, "alternate_locations": [{"id": 3000646, "key": ["WAS BASED"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000646, "field_name": "affidavit_of_occupancy_borrower_sign", "document_id": 9000041}], "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000647, "key": ["agreements", "WAS BASED"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Page 1", "State of"], "start_identifier": [], "field_id": 3000647, "field_name": "affidavit_of_occupancy_borrower_date", "document_id": 9000041}, {"id": 3000649, "key": ["agreements"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Page 1"], "start_identifier": [""], "field_id": 3000649, "field_name": "affidavit_of_occupancy_coborrower_sign", "document_id": 9000041, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000650, "key": ["agreements"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Page 1", "State of"], "start_identifier": [""], "field_id": 3000650, "field_name": "affidavit_of_occupancy_coborrower_date", "document_id": 9000041, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 3000652, "key": ["State of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [")", "}", "} SS", ") SS", "County of"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000652, "field_name": "affidavit_of_occupancy_state_filed", "document_id": 9000041}, {"id": 3000653, "key": ["County of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [")", "} SS", ") SS", "SS", "On this"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000653, "field_name": "affidavit_of_occupancy_county", "document_id": 9000041}, {"id": 3000654, "key": ["Office this", "Before me this", "my hand this", "Before me on", "on this", "Signed and sworn to"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["My commission expires", "MY COMM. EXP."], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000654, "field_name": "affidavit_of_occupancy_execution_date", "document_id": 9000041}, {"id": 3000658, "key": ["Commission Expires", "MY COMM. EXP."], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000658, "field_name": "affidavit_of_occupancy_commission_expires_date_validation", "document_id": 9000041}, {"id": 3000657, "key": ["sworn to and subscribed before", "Signed and sworn to", "Signed and sworn"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000657, "field_name": "affidavit_of_occupancy_notary_commission_expires_date", "document_id": 9000041, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]