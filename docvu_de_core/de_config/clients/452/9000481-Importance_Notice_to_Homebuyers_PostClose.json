[{"id": 3000457, "key": ["I acknowledge that I have read and received"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "possible_page_numbers": [1], "field_id": 3000457, "field_name": "hud92900b_important_notice_to_homebuyers_borrower_sign", "document_id": 9000481, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000458, "key": ["I acknowledge that I have read and received"], "direction": "down", "type": "text", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ICE Mortgage Technology Inc"], "start_identifier": [""], "field_id": 3000458, "field_name": "hud92900b_important_notice_to_homebuyers_borrower_date", "document_id": 9000481, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000460, "key": ["I acknowledge that I have read and received"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000460, "field_name": "hud92900b_important_notice_to_homebuyers_coborrower_sign", "document_id": 9000481, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000461, "key": ["I acknowledge that I have read and received"], "direction": "down", "type": "text", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ICE Mortgage Technology Inc"], "start_identifier": [""], "field_id": 3000461, "field_name": "hud92900b_important_notice_to_homebuyers_coborrower_date", "document_id": 9000481, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]