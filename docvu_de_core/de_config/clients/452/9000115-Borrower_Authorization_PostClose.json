[{"id": 3000238, "key": ["duplicate original", "By signing below", "DO NOT"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER'S AUTHORIZATION"], "start_identifier": [""], "field_id": 3000238, "field_name": "borrower_authorization_borrower_sign", "document_id": 9000115, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000241, "key": ["duplicate original", "By signing below", "DO NOT"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER'S AUTHORIZATION"], "start_identifier": [""], "field_id": 3000241, "field_name": "borrower_authorization_coborrower_sign", "document_id": 9000115, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000239, "key": ["duplicate original", "By signing below", "DO NOT"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER'S AUTHORIZATION"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000239, "field_name": "borrower_authorization_borrower_date", "document_id": 9000115, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000242, "key": ["duplicate original", "By signing below", "DO NOT"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["BORROWER'S AUTHORIZATION"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000242, "field_name": "borrower_authorization_coborrower_date", "document_id": 9000115, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]