[{"id": 3000419, "key": ["my/our"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "possible_page_numbers": [1], "field_id": 3000419, "field_name": "hold_harmless_agreement_borrower_sign", "document_id": 9000461, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000420, "key": ["my/our"], "direction": "down", "type": "text", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ENCROACHMENT/HOLD"], "start_identifier": [""], "field_id": 3000420, "field_name": "hold_harmless_agreement_borrower_date", "document_id": 9000461, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000422, "key": ["my/our execution"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000422, "field_name": "hold_harmless_agreement_coborrower_sign", "document_id": 9000461, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000423, "key": ["my/our"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000423, "field_name": "hold_harmless_agreement_coborrower_date", "document_id": 9000461, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]