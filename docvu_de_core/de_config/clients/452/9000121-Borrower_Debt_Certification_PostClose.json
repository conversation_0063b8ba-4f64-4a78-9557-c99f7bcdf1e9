[{"id": 3000451, "key": ["By signing"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000451, "field_name": "borrowers_employment_and_debt_certification_borrower_sign", "document_id": 9000121, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000452, "key": ["By signing"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000452, "field_name": "borrowers_employment_and_debt_certification_borrower_date", "document_id": 9000121}, {"id": 3000454, "key": ["By signing"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000454, "field_name": "borrowers_employment_and_debt_certification_coborrower_sign", "document_id": 9000121, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000455, "key": ["By signing"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000455, "field_name": "borrowers_employment_and_debt_certification_coborrower_date", "document_id": 9000121}]