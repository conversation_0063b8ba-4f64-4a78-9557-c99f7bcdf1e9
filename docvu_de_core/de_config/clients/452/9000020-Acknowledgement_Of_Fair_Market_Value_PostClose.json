[{"id": 3000166, "key": ["RECEIVE A COPY OF"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000166, "field_name": "ackn_fair_market_value_borrower_sign", "document_id": 9000020, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000167, "key": ["RECEIVE A COPY OF"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000167, "field_name": "ackn_fair_market_value_borrower_date", "document_id": 9000020}, {"id": 3000169, "key": ["RECEIVE A COPY OF"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000169, "field_name": "ackn_fair_market_value_coborrower_sign", "document_id": 9000020, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000170, "key": ["RECEIVE A COPY OF"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000170, "field_name": "ackn_fair_market_value_coborrower_date", "document_id": 9000020}]