[{"id": 3000561, "key": ["SIGNATURE OF VETERAN", "VETERAN (Do NOT print)"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000561, "field_name": "va_certificate_of_eligibility_borrower_sign", "document_id": 9000830, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000562, "key": ["SIGNATURE OF VETERAN", "VETERAN (Do NOT print)"], "direction": "down", "type": "text", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["SEVERE PENALTIES"], "start_identifier": [""], "field_id": 3000562, "field_name": "va_certificate_of_eligibility_borrower_date", "document_id": 9000830, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]