[{"id": 3000305, "key": ["witness whereof", "WITNESS my hand and official seal.", "notary public", "notary"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000305, "field_name": "compliance_agreement_appeared_before", "document_id": 9000205, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "compliance_agreement_appeared_before", "label": ["name"]}}}, {"id": 3000311, "key": ["witness whereof", "WITNESS my hand and official seal.", "notary public", "notary"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000311, "field_name": "compliance_agreement_notary_commission_expires_date", "document_id": 9000205, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000307, "key": ["COUNTY OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["certify", "I certify", "Signed", "on this", "on", "subscribed", "certificate", "this", "that", "and", "being"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000307, "field_name": "compliance_agreement_county", "document_id": 9000205, "output_format": {"string_operations_output_format": {"remove_from_end": ["ss.", "O14d 6ab", " 13", "} SS", ":SS", ": SS"], "remove_special_chars_from_end": true}}}, {"id": 3000306, "key": ["STATE OF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["county", "this", "certificate", "on this", "that", "and", "being", "my", "commission"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000306, "field_name": "compliance_agreement_state_filed", "document_id": 9000205, "output_format": {"string_operations_output_format": {"remove_from_end": ["ss.", "O14d 6ab", " 13", ":SS", " } SS", "} SS", ": SS"], "remove_special_chars_from_end": true}}}, {"id": 3000308, "key": ["before me", "county of"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["notary", "signature"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000308, "field_name": "compliance_agreement_execution_date", "document_id": 9000205, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000309, "key": ["WITNESS my hand and official seal.", "notary public", "notary"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000309, "field_name": "compliance_agreement_notary_acknowledgment", "document_id": 9000205, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000310, "key": ["witness whereof", "WITNESS my hand and official seal.", "notary public", "notary"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000310, "field_name": "compliance_agreement_notary_seal", "document_id": 9000205, "post_process": {"check_for_seal_post_processor": {}}}]