[{"id": 3000315, "key": ["Signed by", "DocuSigned by", "Borrower Signature"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["REMIT"], "start_identifier": [], "field_id": 3000315, "field_name": "consumer_explanation_letter_borrower_date", "document_id": 9002223}, {"id": 3000318, "key": ["Borrower Signature"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["REMIT"], "start_identifier": [], "field_id": 3000318, "field_name": "consumer_explanation_letter_coborrower_date", "document_id": 9002223}, {"id": 3000314, "key": ["Borrower Signature"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000314, "field_name": "consumer_explanation_letter_borrower_sign", "document_id": 9002223, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000317, "key": ["Borrower Signature"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000317, "field_name": "consumer_explanation_letter_coborrower_sign", "document_id": 9002223, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}]