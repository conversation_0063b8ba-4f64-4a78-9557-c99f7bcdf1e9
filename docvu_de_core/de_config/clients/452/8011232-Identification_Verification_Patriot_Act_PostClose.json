[{"id": 3000777, "key": ["State/Foreign Driver's License"], "direction": "right", "type": "number", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State/Foreign ID Card"], "start_identifier": [""], "field_id": 3000777, "field_name": "patriot_act_identification_coborrower_id1", "document_id": 8011232, "output_format": {"id_number_Extractor_output_format": {}, "string_operations_output_format": {"remove_alpha_from_beginning": true, "remove_alpha_from_end": true, "remove_spaces": true}}}, {"id": 3000779, "key": ["U.S./Foreign Passport"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Military ID"], "start_identifier": [""], "field_id": 3000779, "field_name": "patriot_act_identification_coborrower_id2", "document_id": 8011232, "output_format": {"id_number_Extractor_output_format": {}, "string_operations_output_format": {"remove_alpha_from_beginning": true, "remove_alpha_from_end": true, "remove_spaces": true}}}, {"id": 3000778, "key": ["State/Foreign Driver's License"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Military ID"], "start_identifier": [""], "field_id": 3000778, "field_name": "patriot_act_identification_coborrower_id1_expiration_date", "document_id": 8011232, "output_format": {"date_parser_output_format": {"date_position": 2}}}, {"id": 3000780, "key": ["U.S./Foreign Passport"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Military ID"], "start_identifier": [""], "field_id": 3000780, "field_name": "patriot_act_identification_coborrower_id2_expiration_date", "document_id": 8011232, "output_format": {"date_parser_output_format": {"date_position": 2}}}, {"id": 3000786, "key": ["Signature"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["name", "Name and Title", "notary", "Notary", "Attorm", "Loan", "Green"], "start_identifier": [""], "field_id": 3000786, "field_name": "patriot_act_identification_coborrower_notary_name", "document_id": 8011232, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["Date"]}}}, {"id": 3000788, "key": ["CERTIFICATION"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Public CUSTOMER IDENTIFICATION", "CUSTOMER IDENTIFICATION"], "start_identifier": [""], "field_id": 3000788, "field_name": "patriot_act_identification_coborrower_notary_date", "document_id": 8011232, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000787, "key": ["signature"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["name", "CUSTOMER", "CUSTOMER IDENTIFICATION VERIFICATION"], "start_identifier": [""], "field_id": 3000787, "field_name": "patriot_act_identification_coborrower_notary_title", "document_id": 8011232, "output_format": {"name_parser_output_format": {"get_identification_Name_Matcher": true, "from_field": "patriot_act_identification_coborrower_notary_title"}}}, {"id": 3000785, "key": ["CERTIFICATION"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["CUSTOMER IDENTIFICATION", "Public CUSTOMER IDENTIFICATION"], "start_identifier": [], "field_id": 3000785, "field_name": "patriot_act_identification_coborrower_notary_sign", "document_id": 8011232, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}]