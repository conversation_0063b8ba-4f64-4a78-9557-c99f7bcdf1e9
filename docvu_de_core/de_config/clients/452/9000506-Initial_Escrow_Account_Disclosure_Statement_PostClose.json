[{"id": 3000573, "key": ["PLEASE KEEP THIS STATEMENT", "SIGNATURE ADDENDUM", "LOAN #", "I/WE acknowledge receipt"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "probable_place": "individual", "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000573, "field_name": "initial_escrow_account_disclosure_statement_borrower_sign", "document_id": 9000506, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000574, "key": ["PLEASE KEEP THIS STATEMENT", "SIGNATURE ADDENDUM", "CITY TAX payable to", "I/WE acknowledge receipt"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "probable_place": "individual", "use_match": "fuzzy", "end_identifier": ["DATE"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000574, "field_name": "initial_escrow_account_disclosure_statement_borrower_date", "document_id": 9000506, "output_format": {"date_parser_output_format": {"borrower_date": true}}}, {"id": 3000576, "key": ["PLEASE KEEP THIS STATEMENT", "SIGNATURE ADDENDUM", "CITY TAX payable to", "I/WE acknowledge receipt"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "probable_place": "individual", "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000576, "field_name": "initial_escrow_account_disclosure_statement_coborrower_sign", "document_id": 9000506, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000577, "key": ["PLEASE KEEP THIS STATEMENT", "SIGNATURE ADDENDUM", "CITY TAX payable to", "I/WE acknowledge receipt"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "probable_place": "individual", "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000577, "field_name": "initial_escrow_account_disclosure_statement_coborrower_date", "document_id": 9000506, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]