[{"id": 3000623, "key": ["acknowledged before me", "Online Notary", "Commission Expires", "Notary Public", "basis of satisfactory"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "probable_place": "Individual", "field_id": 3000623, "field_name": "indemnity_and_affidavit_to_debts_and_liens_appeared_before", "document_id": 9000501, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "indemnity_and_affidavit_to_debts_and_liens_appeared_before", "label": ["name"]}}}, {"id": 3000628, "key": ["acknowledged before me", "Online Notary", "Commission Expires", "Notary Public", "basis of satisfactory"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000628, "field_name": "indemnity_and_affidavit_to_debts_and_liens_notary_seal", "document_id": 9000501, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3000618, "key": ["conditions hereof", "Under penalties", "include all genders", "successors or"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000618, "field_name": "indemnity_and_affidavit_to_debts_and_liens_borrower_sign", "document_id": 9000501, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000619, "key": ["conditions hereof", "Under penalties", "include all genders", "successors or"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["State of", "Subscribed"], "start_identifier": [""], "field_id": 3000619, "field_name": "indemnity_and_affidavit_to_debts_and_liens_borrower_date", "document_id": 9000501}, {"id": 3000621, "key": ["conditions hereof", "Under penalties", "include all genders", "successors or"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["State of"], "start_identifier": [""], "field_id": 3000621, "field_name": "indemnity_and_affidavit_to_debts_and_liens_coborrower_sign", "document_id": 9000501, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000622, "key": ["conditions hereof", "Under penalties", "include all genders", "successors or"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["State of", "Subscribed"], "start_identifier": [""], "field_id": 3000622, "field_name": "indemnity_and_affidavit_to_debts_and_liens_coborrower_date", "document_id": 9000501, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000624, "key": ["State of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["County of"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000624, "field_name": "indemnity_and_affidavit_to_debts_and_liens_state_filed", "document_id": 9000501}, {"id": 3000625, "key": ["County of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["BEFORE ME", "This instrument", "Sworn to", "On this", "My Commission"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000625, "field_name": "indemnity_and_affidavit_to_debts_and_liens_county", "document_id": 9000501}, {"id": 3000626, "key": ["before me this", "before me on", "online notarization", "On this"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["My commission expires", "personally appeared", "Comm. Expires"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000626, "field_name": "indemnity_and_affidavit_to_debts_and_liens_execution_date", "document_id": 9000501}, {"id": 3000629, "key": ["My Commission Expires", "Commission Expires", "MY COMM. EXP."], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000629, "field_name": "indemnity_and_affidavit_to_debts_and_liens_notary_commission_expires_date", "document_id": 9000501}, {"id": 3000627, "key": ["Notary Public"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000627, "field_name": "indemnity_and_affidavit_to_debts_and_liens_notary_acknowledgment", "document_id": 9000501, "post_process": {"check_for_signature_post_processor": {"signature_present": true, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 70}}}]