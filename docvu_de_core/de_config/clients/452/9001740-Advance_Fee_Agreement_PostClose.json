[{"id": 3000063, "key": ["ACKNOWLEDGEMENT", "commitment"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["LENDER"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 3000063, "field_name": "advance_fee_agreeement_coborrower_date", "document_id": 9001740}, {"id": 3000060, "key": ["ACKNOWLEDGEMENT", "commitment"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["LENDER"], "start_identifier": [""], "possible_page_numbers": [2], "field_id": 3000060, "field_name": "advance_fee_agreeement_borrower_date", "document_id": 9001740}, {"id": 3000059, "key": ["By signing", "disclosure"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000059, "field_name": "advance_fee_agreeement_coborrower_sign", "document_id": 9001740, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 200}}}, {"id": 3000059, "key": ["By signing", "disclosure"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000059, "field_name": "advance_fee_agreeement_borrower_sign", "document_id": 9001740, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 200}}}]