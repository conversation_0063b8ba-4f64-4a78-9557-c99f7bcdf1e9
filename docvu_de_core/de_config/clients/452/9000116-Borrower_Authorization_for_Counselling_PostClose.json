[{"id": 3000232, "key": ["homeownership", "Property inspection", "long-term"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000232, "field_name": "borrower_authorization_for_counseling_borrower_sign", "document_id": 9000116, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000235, "key": ["homeownership", "Property inspection", "long-term"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000235, "field_name": "borrower_authorization_for_counseling_coborrower_sign", "document_id": 9000116, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000233, "key": ["homeownership", "Property inspection", "long-term"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 9000116, "field_name": "borrower_authorization_for_counseling_borrower_date", "document_id": 9000050, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000236, "key": ["homeownership", "Property inspection", "long-term"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 9000116, "field_name": "borrower_authorization_for_counseling_coborrower_date", "document_id": 9000050, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]