[{"id": 3000685, "key": ["lender, in the present"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000685, "field_name": "survey_affidavit_notary_commission_expires_date", "document_id": 9002213, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000679, "key": ["lender, in the present"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000679, "field_name": "survey_affidavit_appeared_before", "document_id": 9002213, "post_process": {"check_for_seal_post_processor": {"seal_appeared_before": true}}, "output_format": {"ner_service_req_output_format": {"text_field_name": "survey_affidavit_appeared_before", "label": ["name"]}}}, {"id": 3005913, "key": ["sworn to and subscribed before me", "signature", "appeared before me"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3005913, "field_name": "survey_affidavit_printed_commission_expires_date", "document_id": 9002213, "post_process": {"check_for_seal_date_post_processor": {}}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000686, "key": ["Commission Expires"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000686, "field_name": "survey_affidavit_commission_expires_date_validation", "document_id": 9002213}, {"id": 3000684, "key": ["gazebos", "room additions"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000684, "field_name": "survey_affidavit_notary_seal", "document_id": 9002213, "post_process": {"check_for_seal_post_processor": {}}}, {"id": 3000682, "key": ["before me this"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Notary Public"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000682, "field_name": "survey_affidavit_execution_date", "document_id": 9002213, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000683, "key": ["NOtary Public"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000683, "field_name": "survey_affidavit_notary_acknowledgment", "document_id": 9002213, "post_process": {"check_for_signature_post_processor": {"signature_present": true, "sign_bbox_direction": "up", "vertical_threshold": 0, "horizontal_threshold": 350}}}, {"id": 3000680, "key": ["State of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000680, "field_name": "survey_affidavit_state_filed", "document_id": 9002213}, {"id": 3000681, "key": ["County of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000681, "field_name": "survey_affidavit_county", "document_id": 9002213}, {"id": 3000675, "key": ["gazebos", "room additions"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Notary Public"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000675, "field_name": "survey_affidavit_borrower_date", "document_id": 9002213, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000678, "key": ["gazebos", "room additions"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Notary Public"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000678, "field_name": "survey_affidavit_coborrower_date", "document_id": 9002213, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 3000674, "key": ["title insurance"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000674, "field_name": "survey_affidavit_borrower_sign", "document_id": 9002213, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000677, "key": ["before me this"], "direction": "inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3000677, "field_name": "survey_affidavit_coborrower_sign", "document_id": 9002213, "post_process": {"check_for_signature_post_processor": {"signature_present": true, "sign_bbox_direction": "right", "vertical_threshold": 37, "horizontal_threshold": 0}}}]