[{"id": 3000567, "key": ["Signature(s) of Borrower(s) - Do not sign", "Do not sign unless this"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000567, "field_name": "hud92900a_addendum_to_urla_borrower_sign", "document_id": 9000480, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000570, "key": ["Signature(s) of Borrower(s) - Do not sign", "Do not sign unless this"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000570, "field_name": "hud92900a_addendum_to_urla_coborrower_sign", "document_id": 9000480, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000568, "key": ["Signature(s) of Borrower(s) - Do not sign", "Do not sign unless this"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000568, "field_name": "hud92900a_addendum_to_urla_borrower_date", "document_id": 9000480, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000571, "key": ["Signature(s) of Borrower(s) - Do not sign", "Do not sign unless this"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000571, "field_name": "hud92900a_addendum_to_urla_coborrower_date", "document_id": 9000480, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]