[{"id": 3000278, "key": ["property locat"], "direction": "right", "type": "address", "return_type": "text", "multi_line_value": true, "probable_place": "individual", "end_identifier": ["lender"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000278, "field_name": "addendum_to_cd_street_address_pc", "document_id": 9000029, "alternate_locations": [{"key": ["property"], "direction": "right", "type": "address", "return_type": "text", "multi_line_value": true, "probable_place": "individual", "end_identifier": ["lender"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000278, "field_name": "addendum_to_cd_street_address_pc", "document_id": 9000029}], "sub_keys": ["address_information_PostClose"], "post_process": {"address_splitter_post_processor": {"return_first": true}}}, {"id": 3000274, "key": ["closing date"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "probable_place": "individual", "end_identifier": ["disbursement date"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000274, "field_name": "addendum_to_cd_closing_date", "document_id": 9000029}, {"id": 3000267, "key": ["Confirm Receipt", "By signing, you are only confirming", "correct taxpayer identification number"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000267, "field_name": "addendum_to_cd_borrower_sign", "document_id": 9000029, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000270, "key": ["Confirm Receipt", "By signing, you are only confirming"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000270, "field_name": "addendum_to_cd_coborrower_sign", "document_id": 9000029, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000268, "key": ["correct taxpayer identification number"], "direction": "down", "type": "date", "return_type": "text", "multi_line_value": true, "probable_place": "individual", "end_identifier": ["The Closing Disclosure which I have prepared"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000268, "field_name": "addendum_to_cd_borrower_date", "document_id": 9000029, "alternate_locations": [{"key": ["Confirm Receipt", "By signing, you are only confirming"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000268, "field_name": "addendum_to_cd_borrower_date", "document_id": 9000029}], "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000271, "key": ["Confirm Receipt", "By signing, you are only confirming"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000271, "field_name": "addendum_to_cd_coborrower_date", "document_id": 9000029, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}, {"id": 3000272, "key": ["<PERSON><PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>", "lender"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000272, "field_name": "addendum_to_cd_borrower_name", "document_id": 9000029}, {"id": 3000273, "key": ["<PERSON><PERSON><PERSON>"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>", "lender"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000273, "field_name": "addendum_to_cd_coborrower_name", "document_id": 9000029, "output_format": {"name_parser_output_format": {"get_addendum_co_borrower_name": true, "from_field": "addendum_co_borrower_name"}}}, {"id": 3000277, "key": ["Case #:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["I have provided", "I have provided a Closing Disclosure"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000277, "field_name": "addendum_to_cd_case_number", "document_id": 9000029}]