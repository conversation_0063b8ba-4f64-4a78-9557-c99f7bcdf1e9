[{"id": 3000019, "key": ["Date"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>"], "start_identifier": [""], "possible_page_numbers": [1, 2], "field_id": 3000019, "field_name": "atr_borrower_date", "document_id": 9000017}, {"id": 3000022, "key": ["co-<PERSON><PERSON><PERSON>", "CoBorrower"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Property Information"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000022, "field_name": "atr_coborrower_date", "document_id": 9000017}, {"id": 3000018, "key": ["Applicant Signature"], "direction": "up", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000018, "field_name": "atr_borrower_sign", "document_id": 9000017, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000021, "key": ["Applicant Signature"], "direction": "up", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000021, "field_name": "atr_coborrower_sign", "document_id": 9000017, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}]