[{"id": 3000367, "key": ["joint credit"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000367, "field_name": "evidence_of_joint_application_borrower_sign", "document_id": 9000297, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000368, "key": ["joint credit"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Evidence of"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000368, "field_name": "evidence_of_joint_application_borrower_date", "document_id": 9000297}, {"id": 3000370, "key": ["joint credit"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000370, "field_name": "evidence_of_joint_application_coborrower_sign", "document_id": 9000297, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000371, "key": ["joint credit"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Evidence of"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3000371, "field_name": "evidence_of_joint_application_coborrower_date", "document_id": 9000297, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]