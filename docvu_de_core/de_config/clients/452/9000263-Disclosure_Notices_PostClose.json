[{"id": 3000343, "key": ["fully understand all of the above", "all of the above and acknowledge receipt"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000343, "field_name": "disclosure_notices_borrower_sign", "document_id": 9000263, "post_process": {"check_for_signature_post_processor": {"check_closeness": false, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000346, "key": ["fully understand all of the above", "all of the above and acknowledge receipt"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000346, "field_name": "disclosure_notices_coborrower_sign", "document_id": 9000263, "post_process": {"check_for_signature_post_processor": {"check_closeness": true, "sign_bbox_direction": "down", "vertical_threshold": 0, "horizontal_threshold": 50}}}, {"id": 3000344, "key": ["fully understand all of the above", "all of the above and acknowledge receipt"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ICE Mortgage Technology"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000344, "field_name": "disclosure_notices_borrower_date", "document_id": 9000263, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000347, "key": ["fully understand all of the above", "all of the above and acknowledge receipt"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["ICE Mortgage Technology"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000347, "field_name": "disclosure_notices_coborrower_date", "document_id": 9000263, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}]