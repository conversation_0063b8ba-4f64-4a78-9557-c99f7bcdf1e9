[{"id": 3000711, "key": ["BY SIGNING BELOW", "<PERSON><PERSON><PERSON> accepts"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000711, "field_name": "rider_1_to_4_family_borrower_sign", "document_id": 9000010, "post_process": {"check_for_signature_post_processor": {"check_closeness": false}}}, {"id": 3000714, "key": ["BY SIGNING BELOW", "<PERSON><PERSON><PERSON> accepts"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000714, "field_name": "rider_1_to_4_family_coborrower_sign", "document_id": 9000010, "post_process": {"check_for_signature_post_processor": {"check_closeness": true}}}, {"id": 3000712, "key": ["BY SIGNING BELOW", "<PERSON><PERSON><PERSON> accepts"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000712, "field_name": "rider_1_to_4_family_borrower_date", "document_id": 9000010, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3000715, "key": ["BY SIGNING BELOW", "<PERSON><PERSON><PERSON> accepts"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3000715, "field_name": "rider_1_to_4_family_coborrower_date", "document_id": 9000010, "output_format": {"date_parser_output_format": {"coborrower_date": true}}}]