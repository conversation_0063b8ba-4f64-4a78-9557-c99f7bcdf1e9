[{"id": 300069100, "key": ["EXHIBIT A", "SCHEDULE A", "LEGAL DESCRIPTION", "SCHEDULE C", "PROPERTY DESCRIPTION"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 300069100, "field_name": "test_01", "document_id": 9000298, "output_format": {"ner_service_req_output_format": {"text_field_name": "test_01", "label": ["COUNTY"]}}}, {"id": 3000691, "key": ["test_01"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": [""], "start_identifier": [""], "field_id": 3000691, "field_name": "legal_desc_county", "document_id": 9000298, "output_format": {"copy_extraction_items_output_format": {"swap_with": "test_01", "is_empty": true}, "string_operations_output_format": {"remove_from_beginning": ["COUNTY OF", "COUNTY", "OF", "county of", "county"], "remove_from_end": ["COUNTY OF", "COUNTY", "OF", "county of", "county"]}}}, {"id": 300069011, "key": ["EXHIBIT A", "SCHEDULE A", "LEGAL DESCRIPTION", "SCHEDULE C", "PROPERTY DESCRIPTION"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 300069011, "field_name": "test_02", "document_id": 9000298, "output_format": {"ner_service_req_output_format": {"text_field_name": "test_02", "label": ["STATE"]}}}, {"id": 3000690, "key": ["test_02"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3000690, "field_name": "legal_desc_state", "document_id": 9000298, "output_format": {"copy_extraction_items_output_format": {"swap_with": "test_02", "is_empty": true}, "string_operations_output_format": {"remove_from_beginning": ["STATE OF", "STATE", "OF", "state of", "state"], "remove_from_end": ["STATE OF", "STATE", "OF", "state of", "state"]}}}]