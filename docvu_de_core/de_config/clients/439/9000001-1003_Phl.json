[{"id": 101, "key": ["borrower information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 101, "field_name": "dummy_section1_1", "document_id": 9000001}, {"id": 80000910, "key": ["Name (", "<PERSON><PERSON> ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "field_id": 80000910, "field_name": "Application Borrower Name 1", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000912, "key": ["Work Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Ext.", "Ert", "Ext"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000912, "field_name": "Work Phone Borrower 1", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000909, "key": ["Total Number of Borrowers"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Each Borrower intends"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000909, "field_name": "Total Number of Borrowers", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_end": ["\\D*"], "remove_special_chars_from_end": true, "retain_only_numbers": true}}, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000821, "key": ["Email"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000821, "field_name": "<PERSON><PERSON><PERSON> Email Address", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000808, "key": ["Home Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Separated"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000808, "field_name": "Home Phone Borrower 1", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000817, "key": ["Social Security Number"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Date of Birth"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000817, "field_name": "Borrower SSN", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000819, "key": ["Date of Birth"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Non-Permanent Resident Alien", "type of credit"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000819, "field_name": "Borrower Date of Birth", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000809, "key": ["Cell Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unmarried"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000809, "field_name": "Cell Phone Borrower 1", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 102, "key": ["Section 1"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 102, "field_name": "Borrower_marital_omr_field", "sub_keys": ["Phl_marital_status"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 103, "key": ["Mailing Address"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Employer or Business Name"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 103, "field_name": "borrower_mailing_address_save", "sub_keys": ["Phl_mailling_address_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 107, "key": ["how long in this line"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TOTAL"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 107, "field_name": "Borrow<PERSON>_Self_employed_save_section", "sub_keys": ["<PERSON><PERSON>_<PERSON><PERSON>er_Self_employed"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 104, "key": ["Loan and Property Information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 104, "field_name": "dummy_section4_4", "document_id": 9000001}, {"id": 105, "key": ["Section 4"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Creditor Name"], "start_identifier": [""], "possible_page_numbers": [3], "field_id": 104, "field_name": "phl_occupancy_checkbox_save_section", "sub_keys": ["Phl_occupancy_old_omr"], "document_id": 9000001}, {"id": 108, "key": ["Section 5"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 108, "field_name": "dummy_section5_1", "document_id": 9000001}, {"id": 109, "key": ["Declarations"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["secondary residence"], "start_identifier": [""], "field_id": 109, "field_name": "<PERSON><PERSON>er_Homebuyer_save_section", "sub_keys": ["Phl_Section5_<PERSON><PERSON><PERSON>_Homebuyer"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 110, "key": ["Mortgage Type"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Loan Features"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 110, "field_name": "Lien_type_save_section", "sub_keys": ["Phl_Lien_Type_omr"], "document_id": 9000001}, {"id": 111, "key": ["Citizenship"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 111, "field_name": "dummy_section_11", "document_id": 9000001}, {"id": 80000915, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000915, "field_name": "Application Borrower Name 2", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000820, "key": ["Date of Birth"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Non-Permanent Resident Alien", "type of credit"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000820, "field_name": "Co-Borrower Date of Birth", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 80000810, "key": ["Home Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Separated"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000810, "field_name": "Home Phone Borrower 2", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000811, "key": ["Cell Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unmarried"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000811, "field_name": "Cell Phone Borrower 2", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000917, "key": ["Work Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Ext."], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000917, "field_name": "Work Phone Borrower 2", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"string_operations_output_format": {"remove_from_end": ["Ext .", "Ext"], "remove_special_chars_from_end": true}}}, {"id": 80000818, "key": ["Social Security Number"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Date of Birth"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000818, "field_name": "Co-Borrower SSN", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000822, "key": ["Email"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000822, "field_name": "Coborrower <PERSON>ail Address", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 112, "key": ["Name", "Social Security Number"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 112, "field_name": "<PERSON>_<PERSON>rrower_Section1_save_section", "sub_keys": ["Phl_<PERSON>_Borrower_marital_status"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}]