[{"id": 80000864, "key": ["<PERSON>. COMMENTS (Optional)"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["this flood determination"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80000864, "field_name": "Flood Contract Type", "document_id": 9000377, "alternate_locations": [{"id": 80000864, "key": ["FloodCert #"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["FEMA FORM"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80000864, "field_name": "Flood Contract Type", "document_id": 9000377}], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^.*?(?=LIFE[- ]OF[- ]LOAN(?: DETERMINATION)?)"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 80000863, "key": ["DATE OF DETERMINATION"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["May", "PO Box"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80000863, "field_name": "Flood Company", "document_id": 9000377, "post_process": {"name_post_processor": {"extract_flood_company": true}}}, {"id": 80001155, "key": ["DATE OF DETERMINATION"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["DETERMINATION ID"], "start_identifier": [""], "field_id": 80001155, "field_name": "Flood Certificate Date", "document_id": 9000377}, {"id": 80003317, "key": ["flood zone"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["C. FEDERAL"], "start_identifier": [], "possible_page_numbers": [1], "field_id": 80003317, "field_name": "Flood Zone", "document_id": 9000377}, {"id": 80000862, "key": ["ORDER NUMBER", "DETERMINATION NUMBER"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["SFHDF", "FEMA FORM", "MtIA"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80000862, "field_name": "Flood Certificate Number", "document_id": 9000377, "alternate_locations": [{"id": 80000862, "key": ["FloodCert #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["SFHDF"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80000862, "field_name": "Flood Certificate Number", "document_id": 9000377}], "post_process": {"number_post_processor": {"extract_flood_certificate_number": true}}}, {"id": 80000865, "key": ["NFIP Community Number"], "direction": "down_inline", "type": "number", "return_type": "text", "multi_line_value": true, "end_identifier": ["B. NATIONAL FLOOD"], "start_identifier": [], "possible_page_numbers": [], "field_id": 80000865, "field_name": "Flood Community Number", "document_id": 9000377, "output_format": {"string_operations_output_format": {"remove_alpha_from_beginning": true}}}, {"id": 80003318, "key": ["MSA/MD", "MSA"], "direction": "right", "type": "number", "return_type": "text", "multi_line_value": false, "end_identifier": ["TRACT:", "CENSUS TRACT"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80003318, "field_name": "SMSA Code", "document_id": 9000377, "output_format": {"string_operations_output_format": {"remove_from_end": [","], "remove_special_chars_from_end": true}}}, {"id": 80000859, "key": ["Community name,if not the same as"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["4. Flood Zone", "MapNumber"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80000859, "field_name": "Map Panel Number", "document_id": 9000377, "output_format": {"string_operations_output_format": {"extract_panel_number": true}}}]