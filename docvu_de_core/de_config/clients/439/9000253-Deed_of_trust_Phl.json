[{"id": 80000927, "key": ["by signing below", "in witness whereof"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["state of", "(Sign Original Only)"], "start_identifier": ["Rider executed", "mailed to"], "field_id": 80000927, "field_name": "Borrower 1 Name", "document_id": 9000253, "post_process": {"name_post_processor": {"get_dot_borrower_name": true}}, "output_format": {"string_operations_output_format": {"remove_from_end": ["<PERSON><PERSON><PERSON>"], "remove_from_beginning": ["Witness Witness"]}}}, {"id": 80000928, "key": ["by signing below", "in witness whereof"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["state of", "(Sign Original Only)"], "start_identifier": ["Rider executed", "mailed to"], "field_id": 80000928, "field_name": "Borrower 2 Name", "document_id": 9000253, "post_process": {"name_post_processor": {"get_dot_coborrower_name": true}}}, {"id": 80000929, "key": ["by signing below", "in witness whereof"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["state of", "(Sign Original Only)"], "start_identifier": ["Rider executed", "mailed to"], "field_id": 80000929, "field_name": "Borrower 3 Name", "document_id": 9000253, "post_process": {"name_post_processor": {"get_dot_c1_name": true}}}, {"id": 80000930, "key": ["by signing below", "in witness whereof"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["state of", "(Sign Original Only)"], "start_identifier": ["Rider executed", "mailed to"], "field_id": 80000930, "field_name": "Borrower 4 Name", "document_id": 9000253, "post_process": {"name_post_processor": {"get_dot_c2_name": true}}}, {"id": 80000844, "key": ["MIN"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "end_identifier": ["MERS"], "start_identifier": [], "field_id": 80000844, "field_name": "MERS/MIN Number", "document_id": 9000253}, {"id": 80000845, "key": ["has the address of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TOGETHER with"], "start_identifier": [""], "field_id": 80000845, "field_name": "Property Address", "document_id": 9000253, "output_format": {"string_operations_output_format": {"remove_from_end": ["]"], "remove_special_chars_from_end": true}, "address_parser_output_format": {"get_line1_and_line2": true, "from_field": "Property Address"}}}, {"id": 80000846, "key": ["has the address of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TOGETHER with"], "start_identifier": [""], "field_id": 80000846, "field_name": "Property City", "document_id": 9000253, "output_format": {"string_operations_output_format": {"remove_from_end": ["[street]"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}, "date_remover_output_format": {"custom": false}, "address_parser_output_format": {"get_city": true, "from_field": "Property City"}}}, {"id": 80000847, "key": ["has the address of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TOGETHER WITH"], "start_identifier": [""], "field_id": 80000847, "field_name": "Property State", "document_id": 9000253, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["[street]", "[city]"], "remove_from_end": ["Property Address"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}, "date_remover_output_format": {"custom": false}, "address_parser_output_format": {"get_state": true, "from_field": "Property State"}}}, {"id": 80000848, "key": ["has the address of"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TOGETHER WITH"], "start_identifier": [""], "field_id": 80000848, "field_name": "Property Zip", "document_id": 9000253, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["[street]", "[city]"], "remove_from_end": ["Property Address"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}, "date_remover_output_format": {"custom": false}, "address_parser_output_format": {"get_zip_code": true, "from_field": "Property Zip"}}}]