[{"id": 3000913, "key": ["Loan Identifier"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "|", "I", "Agency Case"], "start_identifier": [""], "field_id": 3000913, "field_name": "Loan Number  - 3000913", "document_id": 9000001}, {"id": 101, "key": ["borrower information"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 101, "field_name": "dummy_section1_1", "document_id": 9000001}, {"id": 3000011, "key": ["Name (", "<PERSON><PERSON> ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "field_id": 3000011, "field_name": "Borrower Name  - 3000011", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004735, "key": ["Borrower information"], "direction": "right", "type": "number", "return_type": "text", "multi_line_value": true, "start_identifier": [], "end_identifier": ["Date of Birth"], "field_id": 3004735, "field_name": "Social Security Number  - 3004735", "document_id": 9000001, "output_format": {"number_parser_output_format": {"extract_multiple": false, "pattern_keys": ["ssn"]}}, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004736, "key": ["Date of Birth"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Non-Permanent Resident Alien", "type of credit"], "start_identifier": [""], "field_id": 3004736, "field_name": "Date of birth - 3004736", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000821, "key": ["Section 1"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000821, "field_name": "Borrower_Section1_omr_fields", "sub_keys": ["UW_Section1_omr_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004744, "key": ["Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit #"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3004744, "field_name": "Street Address - 3004744", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004745, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State", "Stale", "Statd", "Statre"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3004745, "field_name": "City - 3004745", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004746, "key": ["State", "Stale", "Statd", "Statre"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP", "LIP", "Zip"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3004746, "field_name": "State - 3004746", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004747, "key": ["ZIP", "LIP"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3004747, "field_name": "Zip Code - 3004747", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 101010, "key": ["current address"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["If at Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 101010, "field_name": "current_address_save", "sub_keys": ["UW_current_address_section"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004766, "key": ["Employer or Business Name", "Business Name"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Phone"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3004766, "field_name": "Business Name: - 3004766", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004768, "key": ["Start Date"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["(mm/", "am employed ", "How long"], "start_identifier": [""], "field_id": 3004768, "field_name": "Employment Start Date: - 3004768", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 101011, "key": ["Gross Monthly Income"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 101011, "field_name": "Gross_monthly_total", "sub_keys": ["UW_Gross_monthly_income_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 101012, "key": ["how long in this line"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["TOTAL"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 101012, "field_name": "Employer_or_Business_save", "sub_keys": ["UW_Ownership_Interest_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 5000, "key": ["1c. IF APPLICABLE", "1c. IF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["GURLA", "1d. IF APPLICABLE", "1d. IF"], "start_identifier": [""], "field_id": 5000, "field_name": "Borrower_additional_info_save", "sub_keys": ["UW_<PERSON>rrow<PERSON>_additional_info_OMR_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 2000, "key": ["1d. IF", "1d. IF APPLICABLE"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["1e. Income"], "start_identifier": [""], "field_id": 2000, "field_name": "Employement_EndDate_save", "sub_keys": ["UW_Employment_EndDate_save1"], "document_id": 9000001}, {"id": 100001, "key": ["Section 2"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["2a."], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 100001, "field_name": "dummy_section2_2", "document_id": 9000001}, {"id": 3004781, "key": ["Money Market"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "2b. Other Assets", "Does not apply"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004781, "field_name": "Account Type - 3004781", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Account Type", "field_string_col": "Account Type", "probable_field_value_col": "Account Type", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Account Type - use list above", "Financial Institution", "Account Number", "Cash or Market Value"], "table_structure": "vertical"}, {"id": 3004783, "key": ["Money Market"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "2b. Other Assets", "Does not apply"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004783, "field_name": "Financial Institute - 3004783", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Financial Institution", "field_string_col": "Financial Institution", "probable_field_value_col": "Financial Institution", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Account Type - use list above", "Financial Institution", "Account Number", "Cash or Market Value"], "table_structure": "vertical"}, {"id": 3004784, "key": ["Money Market"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "2b. Other Assets", "Does not apply"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004784, "field_name": "Account Number - 3004784", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Account Number", "field_string_col": "Account Number", "probable_field_value_col": "Account Number", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": ["Provide TOTAL Amount Here", "Provide TOTAL Amount Hem", "Provide TO I AI Amount Here"], "value_return_as_table": true}}, "table_header_list": ["Account Type - use list above", "Financial Institution", "Account Number", "Cash or Market Value"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["[^\\d,.]+$"], "keep_only_initial_number": true, "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004785, "key": ["Money Market"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "2b. Other Assets", "Does not apply"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004785, "field_name": "Cash or Maket Value - 3004785", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Account Number", "field_string_col": "Account Number", "probable_field_value_col": "Cash or Market Value", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": ["Provide TOTAL", "Provide TOTAL Amount Here", "Amount Here"], "value_return_as_table": true}}, "table_header_list": ["Account Type - use list above", "Financial Institution", "Account Number", "Cash or Market Value"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 3004787, "key": ["Money Market"], "type": "table_new", "end_identifier": ["Borrower Name", "2b. Other Assets", "Does not apply"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004787, "field_name": "3004787 - Total Assets", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "table_processor": {"extract_data_from_table_without_lines": {"field_string": ["Provide TOTAL Amount Here", "Amount Here", "Provide TOTAL Amount Hem", "Provide TO I AI Amount Here"], "field_string_col": ["Provide TOTAL Amount Here", "Amount Here", "Provide TOTAL Amount Hem", "Provide TO I AI Amount Here"], "probable_field_value_col": "Cash or Market Value", "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["Account Type - use list above", "Financial Institution", "Account Number", "Cash or Market Value"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 3004788, "key": ["Secured Borrowed Funds"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "2c Liabilities"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004788, "field_name": "Asset or Credit Type - 3004788", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Asset or Credit Type", "field_string_col": "Asset or Credit Type", "probable_field_value_col": "Asset or Credit Type", "probable_place_in_table": "below_row", "probable_field_value_next_row": ["Provide TOTAL", "TOTAL Amount Here", "Provide TOTAI"], "value_return_as_table": true}}, "table_header_list": ["Asset or Credit Type - use list above", "Cash or Market Value"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004789, "key": ["Secured Borrowed Funds"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "2c Liabilities"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004789, "field_name": "Cash or Market Value - 3004789", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Asset or Credit Type", "field_string_col": "Asset or Credit Type", "probable_field_value_col": "Cash or Market Value", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": ["Provide TOTAL", "Provide TOTAI", "TOTAL Amount Here"], "value_return_as_table": true}}, "table_header_list": ["Asset or Credit Type - use list above", "Cash or Market Value"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$", "S"]}}}, {"id": 3004790, "key": ["Secured Borrowed Funds"], "type": "table_new", "end_identifier": ["Borrower Name", "2c Liabilities"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004790, "field_name": "Total Other Assets - 3004790", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": ["Provide TOTAL Amount", "TOTAL Amount Here", "Provide TOTAL Amount Here", "Provkle TOTAL Amount Here", "Provide TOTAL"], "field_string_col": ["Provide TOTAL Amount", "TOTAL Amount Here", "Provide TOTAL Amount Here", "Provkle TOTAL Amount Here", "Provide TOTAL"], "probable_field_value_col": "Cash or Market Value", "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}, "table_header_list": ["Asset or Credit Type - use list above", "Cash or Market Value"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 3004797, "key": ["Revolving"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "2d. Other Liabilities", "Does not apply"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004797, "field_name": "Account Type - 3004797", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": ["use list above", "above"], "field_string_col": ["use list above", "above"], "probable_field_value_col": ["use list above", "above"], "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["use list above", "Company Name", "Account Number", "Unpaid Balance", "or before closing", "Monthly Payment"], "table_structure": "vertical"}, {"id": 3004798, "key": ["Revolving"], "type": "table_new", "return_type": "table", "end_identifier": ["2d. Other Liabilities", "Does not apply", "Borrower Name"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": ***********, "field_name": "Company Name - 3004798", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Company Name", "field_string_col": "Company Name", "probable_field_value_col": "Company Name", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["use list above", "Company Name", "Account Number", "Unpaid Balance", "or before closing", "Monthly Payment"], "table_structure": "vertical"}, {"id": 3004799, "key": ["Revolving"], "type": "table_new", "return_type": "table", "end_identifier": ["2d. Other Liabilities", "Does not apply", "Borrower Name"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004799, "field_name": "Account Number - 3004799", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Account Number", "field_string_col": "Account Number", "probable_field_value_col": "Account Number", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["use list above", "Company Name", "Account Number", "Unpaid Balance", "or before closing", "Monthly Payment"], "table_structure": "vertical"}, {"id": 3004800, "key": ["Revolving"], "type": "table_new", "return_type": "table", "end_identifier": ["2d. Other Liabilities", "Does not apply", "Borrower Name"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004800, "field_name": "Unpaid Balance - 3004800", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Unpaid Balance", "field_string_col": "Unpaid Balance", "probable_field_value_col": "Unpaid Balance", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["use list above", "Company Name", "Account Number", "Unpaid Balance", "or before closing", "Monthly Payment"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*In", "\\s*O", "\\s*\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 101101, "key": ["Revolving"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["2d. Other Liabilities"], "start_identifier": [], "field_id": 101101, "field_name": "2c_table_omr_save", "sub_keys": ["uw_1003_2c_table_omr_save"], "document_id": 9000001}, {"id": 3004802, "key": ["Revolving"], "type": "table_new", "return_type": "table", "end_identifier": ["2d. Other Liabilities", "Does not apply", "Borrower Name"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004802, "field_name": "Monthly Payment - 3004802", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Monthly Payment", "field_string_col": "Monthly Payment", "probable_field_value_col": "Monthly Payment", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["use list above", "Company Name", "Account Number", "Unpaid Balance", "or before closing", "Monthly Payment"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 3004805, "key": ["Include all other liabilities"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "Section 3", "Financial Information — Real Estate"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004805, "field_name": "Other Liabilities & Expenses Type - 3004805", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Alimony", "field_string_col": "Alimony", "probable_field_value_col": "Alimony", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["• Alimony • Child Support • Separate Maintenance • Job Related Expenses • Other", "Monthly Payment"], "table_structure": "vertical"}, {"id": 3004806, "key": ["Include all other liabilities"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "Section 3", "Financial Information — Real Estate"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004806, "field_name": "Monthly Payment - 3004806", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Monthly Payment", "field_string_col": "Monthly Payment", "probable_field_value_col": "Monthly Payment", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Include all other", "Monthly Payment"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 100002, "key": ["Section 3"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["3a."], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 100002, "field_name": "dummy_section3_3", "document_id": 9000001}, {"id": 6000, "key": ["Section 3"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["3a. Property", "3a."], "start_identifier": [""], "field_id": 6000, "field_name": "Borrower_RealEstate_save", "sub_keys": ["UW_Borrower_RealEstate_OMR_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 101013, "key": ["Financial Information — Real Estate", "3a. Property"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name", "3b. IF APPLICABLE, Complete", "Section 4"], "start_identifier": [""], "field_id": 101013, "field_name": "Financial_Information_Real_Estate_save", "sub_keys": ["UW_Financial_Information_Real_Estate_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004815, "key": ["3a. Property You Own", "3a. Property", "if you are refinancing"], "type": "table_new", "return_type": "table", "end_identifier": ["Mortgage—s on this Property", "Mortgage Loans on this Property", "Borrower Name"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004815, "field_name": "Property Value - 3004815", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Property Value", "field_string_col": "Property Value", "probable_field_value_col": "Property Value", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Property Value", "Retained", "Residence, Second Home, Other", "Mortgage Payment", "Income", "Net Monthly Rental Income"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 3004816, "key": ["3a. Property You Own", "3a. Property"], "type": "table_new", "return_type": "table", "end_identifier": ["Mortgage—s on this Property", "Mortgage Loans on this Property", "Borrower Name"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004816, "field_name": "Intended Occupancy Type: -  3004816", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": ["Residence, Second Home, Other", "Residence, Second Home", "Home, Other"], "field_string_col": ["Residence, Second Home, Other", "Residence, Second Home", "Home, Other"], "probable_field_value_col": ["Residence, Second Home, Other", "Residence, Second Home", "Home, Other"], "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Property Value", "Retained", "Residence, Second Home, Other", "Mortgage Payment", "Income", "Net Monthly Rental Income"], "table_structure": "vertical"}, {"id": 3004817, "key": ["3a. Property You Own", "3a. Property"], "type": "table_new", "return_type": "table", "end_identifier": ["Mortgage—s on this Property", "Mortgage Loans on this Property", "Borrower Name"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004817, "field_name": "Monthly Rental Income: - 3004817", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": ["Income", "Monthly Rental Income"], "field_string_col": ["Income", "Monthly Rental Income"], "probable_field_value_col": ["Income", "Monthly Rental Income"], "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Property Value", "Retained", "Residence, Second Home, Other", "Mortgage Payment", "Income", "Net Monthly Rental Income"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 3004775, "key": ["3a. Property You Own", "3a. Property"], "type": "table_new", "return_type": "table", "end_identifier": ["Mortgage—s on this Property", "Mortgage Loans on this Property", "Borrower Name"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004775, "field_name": "3004775 - Net Rental:", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": ["Net Monthly Rental Income"], "field_string_col": ["Net Monthly Rental Income"], "probable_field_value_col": ["Net Monthly Rental Income"], "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Property Value", "Retained", "Residence, Second Home, Other", "Mortgage Payment", "Income", "Net Monthly Rental Income"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 7000, "key": ["Section 3"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name", "3b."], "start_identifier": [""], "field_id": 7000, "field_name": "mortgage_property_save", "sub_keys": ["UW_Mortgage_property_OMR_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004823, "key": ["Mortgage Loans on this Property", "Mortgage—s on this Property"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "3b. IF APPLICABLE", "Section 4"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004823, "field_name": "Creditor Name: - 3004823", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Creditor Name", "field_string_col": "Creditor Name", "probable_field_value_col": "Creditor Name", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Creditor Name", "Account Number", "Payment", "Unpaid Balance", "before closing", "Other", "(if applicable)"], "table_structure": "vertical"}, {"id": 3004824, "key": ["Mortgage Loans on this Property", "Mortgage—s on this Property"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "3b. IF APPLICABLE", "Section 4"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004824, "field_name": "Account Number: - 3004824", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": ["Account Number", "Number"], "field_string_col": ["Account Number", "Number"], "probable_field_value_col": ["Account Number", "Number"], "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Creditor Name", "Account Number", "Payment", "Unpaid Balance", "before closing", "Other", "(if applicable)"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["[^\\d,.]+$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "extract_only_numeric": true}}}, {"id": 3004825, "key": ["Mortgage Loans on this Property", "Mortgage—s on this Property"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "3b. IF APPLICABLE", "Section 4"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004825, "field_name": "Monthly Payment: - 3004825", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Payment", "field_string_col": "Payment", "probable_field_value_col": "Payment", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Creditor Name", "Account Number", "Payment", "Unpaid Balance", "before closing", "Other", "(if applicable)"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 3004826, "key": ["Mortgage Loans on this Property", "Mortgage—s on this Property"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "3b. IF APPLICABLE", "Section 4"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004826, "field_name": "Unpaid Balance: - 3004826", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": ["Unpaid Balance", "Balance"], "field_string_col": ["Unpaid Balance", "Balance"], "probable_field_value_col": ["Unpaid Balance", "Balance"], "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Creditor Name", "Account Number", "Payment", "Unpaid Balance", "before closing", "Other", "(if applicable)"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 101102, "key": ["Mortgage Loans on this Property", "Mortgage—s on this Property"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["3b. IF APPLICABLE"], "start_identifier": [], "field_id": 101102, "field_name": "3a_table_omr_save", "sub_keys": ["uw_1003_3a_table_omr_save"], "document_id": 9000001}, {"id": 9000, "key": ["3b. IF APPLICABLE", "3b."], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name", "3c."], "start_identifier": [""], "field_id": 9000, "field_name": "3b_own_any_additional_properties_save", "sub_keys": ["UW_3b_own_additional_properties_save1"], "document_id": 9000001}, {"id": 100001, "key": ["Section 4"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["4a."], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 100001, "field_name": "dummy_section4_4", "document_id": 9000001}, {"id": 1003, "key": ["Section 4"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["4b. Other", "Creditor Name"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 1003, "field_name": "Loan_Purpose_checkbox_save_section", "sub_keys": ["UW_Section4_loan_property_information"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 8000, "key": ["4b. Other", "4b."], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name", "4c. <PERSON><PERSON>", "4c."], "start_identifier": [""], "field_id": 8000, "field_name": "4B_mortgage_subject_property_save", "sub_keys": ["UW_4B_mortgage_subject_property_OMR_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004757, "key": ["Manufactured Home"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "4c. Rental Income"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004757, "field_name": "Creditor Name - 3004757", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Creditor Name", "field_string_col": "Creditor Name", "probable_field_value_col": "Creditor Name", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Creditor Name", "Lien Type", "Monthly Payment", "to be Drawn", "(if applicable)"], "table_structure": "vertical"}, {"id": 3004759, "key": ["Manufactured Home"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "4c. Rental Income"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004759, "field_name": "Monthly Payment - 3004759", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Monthly Payment", "field_string_col": "Monthly Payment", "probable_field_value_col": "Monthly Payment", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Creditor Name", "Lien Type", "Monthly Payment", "to be Drawn", "(if applicable)"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 3004758, "key": ["Manufactured Home"], "type": "table_new", "return_type": "table", "end_identifier": ["Borrower Name", "4c. Rental Income"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004758, "field_name": "Loan Amount - 3004758", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "to be Drawn", "field_string_col": "to be Drawn", "probable_field_value_col": "to be Drawn", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true}}, "table_header_list": ["Creditor Name", "Lien Type", "Monthly Payment", "to be Drawn", "(if applicable)"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 10091, "key": ["About Your Finances", "<PERSON>. Are you a co-signer", "outstanding judgments"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [6], "include_key": true, "field_id": 10091, "field_name": "Borrower_Declarations_save_section", "sub_keys": ["UW_Section5_Declaration_omr"], "document_id": 9000001}, {"id": 1005, "key": ["<PERSON>. Have you declared"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [6], "field_id": 1005, "field_name": "Bo<PERSON><PERSON>_Declarations_MField_save_section", "sub_keys": ["UW_Section5_Declaration_MField_omr"], "document_id": 9000001}, {"id": 3000, "key": ["section 6"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 3000, "field_name": "dummy_section6_6", "document_id": 9000001}, {"id": 3005873, "key": ["Borrower Signature"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Additional Borrower Signature", "Borrower Name"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3005873, "field_name": "3005873 - Document Date", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 1011, "key": ["Citizenship"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [], "field_id": 1011, "field_name": "dummy_section1_co-borrower_info", "document_id": 9000001}, {"id": 3005715, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "field_id": 3005715, "field_name": "Co-Borrower Name - 3005715", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3005716, "key": ["Borrower information", "Additional <PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Date of Birth"], "start_identifier": [""], "field_id": 3005716, "field_name": "Co-Borrower Social Security Number - 3005716", "document_id": 9000001, "output_format": {"number_parser_output_format": {"extract_multiple": false, "pattern_keys": ["ssn"]}}, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3005717, "key": ["Date of Birth"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Non-Permanent Resident Alien", "type of credit"], "start_identifier": [""], "field_id": 3005717, "field_name": "Co-Borrower Date of birth - 3005717", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 101014, "key": ["Name", "Social Security Number"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 101014, "field_name": "<PERSON><PERSON><PERSON><PERSON>er_Section1_omr_fields", "sub_keys": ["UW_Co_borrower_Section1_omr_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 12000, "key": ["1c. IF APPLICABLE", "1c. IF"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["GURLA", "1d. IF APPLICABLE", "1d. IF"], "start_identifier": [""], "field_id": 12000, "field_name": "Borrower_additional_info_save1", "sub_keys": ["UW_<PERSON>rrower_additional_info_OMR_save1"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 101015, "key": ["Gross Monthly Income"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 101015, "field_name": "Co_Borrower_Gross_monthly_total_save", "sub_keys": ["UW_Co_borrower_Gross_monthly_income_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 100002, "key": ["Section 3"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["3a."], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 100002, "field_name": "dummy_section3_3", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3005730, "key": ["3a. IF APPLICABLE", "3a. IF"], "type": "table_new", "end_identifier": ["Mortgage—s on this Property", "Mortgage Loans on this Property", "Borrower Name"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3005730, "field_name": "3005730 - Net Rental:", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "table_processor": {"extract_data_from_table_without_lines": {"field_string": ["Net Monthly Rental Income"], "field_string_col": ["Net Monthly Rental Income"], "probable_field_value_col": ["Net Monthly Rental Income"], "probable_place_in_table": "below_row", "probable_field_value_next_row": 1}}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "remove_characters": ["$"]}}}, {"id": 101016, "key": ["Original Cost"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Title to the Property Will"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 101016, "field_name": "UW_property_save_section", "sub_keys": ["UW_project_type_omr"], "document_id": 9000001}, {"id": 50055, "key": ["L3. Mortgage Loan Information", "L3. Mortgage"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Amortization"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 50055, "field_name": "UW_Loan_program_save_section", "sub_keys": ["UW_Mortgage_Loan_info_omr"], "document_id": 9000001}]