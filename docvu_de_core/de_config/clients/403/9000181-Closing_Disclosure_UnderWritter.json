[{"id": 3005877, "key": ["Date Issued"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON><PERSON>"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005877, "field_name": "3005877 - Document Date", "document_id": 9000181}, {"id": 3005724, "key": ["Sale Price $", "Sale Price", "Appraised Prop. Value", "Appraised Prop Value", "Estimated Prop. Value"], "direction": "right", "type": "text", "return_type": "amount", "multi_line_value": false, "end_identifier": ["<PERSON><PERSON>", "Loan <PERSON>", "mic", "Gate"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005724, "field_name": "3005724 - Pur<PERSON> Price", "document_id": 9000181, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3005729, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Interest Rate"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005729, "field_name": "3005729 -  <PERSON><PERSON>", "document_id": 9000181, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["[A-Za-z,\\s]+\\d{5}.*$", "[A-Za-z,\\s]+$", "\\s*$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3005727, "key": ["Cash to Close"], "direction": "down", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3005727, "field_name": "3005727 - Estimated Closing Cost", "document_id": 9000181, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3005728, "key": ["Closing Cost Details", "A. Origination Charges"], "type": "table_new", "end_identifier": ["B. Services Borrower Did Not Shop For"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3005728, "field_name": "3005728- Discount Points", "document_id": 9000181, "table_processor": {"extract_data_from_table_without_lines": {"field_string": ["A. Origination Charges", "A. . . , npitiOn Charges"], "field_string_col": ["A. Origination Charges", "A. . . , npitiOn Charges"], "probable_field_value_col": ["A. Origination Charges", "A. . . , npitiOn Charges"], "probable_place_in_table": "below_row", "probable_field_value_next_row": 2}}, "output_format": {"number_parser_output_format": {"extract_multiple": false, "pattern_keys": ["percent_or_rate"]}}}, {"id": 3005732, "key": ["<PERSON><PERSON>"], "direction": "just_down", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["B. Services Borrower Did Not Shop For", "B. Services"], "start_identifier": ["Others"], "field_id": 3005732, "field_name": "3005732 - Closing Cost Paid by Other", "document_id": 9000181, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3005726, "key": ["F. Prepaids", "<PERSON><PERSON>", "<PERSON><PERSON>"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Homeowner's Insurance Premium"], "start_identifier": [], "possible_page_numbers": [1], "field_id": 3005726, "field_name": "3005726 - Prepaid Items", "document_id": 9000181, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 1234565, "key": ["<PERSON><PERSON>", "<PERSON>id <PERSON>"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Other Credits"], "start_identifier": [], "field_id": 1234565, "field_name": "EMD_save", "document_id": 9000181, "sub_keys": ["UW_EMD_save"]}, {"id": 3005725, "key": ["04 Payoff of First Mortgage Loan", "First Mortgage"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Seller Credit", "03 Seller", "05 Seller"], "start_identifier": [], "possible_page_numbers": [1], "field_id": 3005725, "field_name": "3005725 - Mortgage Payoff", "document_id": 9000181, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 123456, "key": ["Total Paid Already by or on <PERSON><PERSON><PERSON> of Borrower at Closing (L)"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["PAGE"], "start_identifier": [], "field_id": 123456, "field_name": "UW_cash_from_borrower", "sub_keys": ["UW_cash_from_borrower_omr"], "document_id": 9000181}]