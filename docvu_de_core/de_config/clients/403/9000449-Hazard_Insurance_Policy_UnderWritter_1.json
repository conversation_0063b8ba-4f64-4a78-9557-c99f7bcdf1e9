[{"id": 3005861, "key": ["renewed automatically", "Effective Date:", "Producer Name", "Effective Dates:", "Claims Questions", "Premium Period Beginning"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Expiration of Policy Period", "Residence Premises", "standard time", "Interinsurance Network", "ends at Noon"], "start_identifier": [""], "field_id": 3005861, "field_name": "Document Date", "document_id": 9000449, "output_format": {"date_parser_output_format": {"date_position": 1}}}, {"id": 3004834, "key": ["Total", "Total Premium:"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["First Mortgagee"], "start_identifier": [""], "field_id": 3004834, "field_name": "Annual Hazard Insurance Premium", "document_id": 9000449, "alternate_locations": [{"id": 3004834, "key": ["Policy Premium"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["First Mortgagee", "Annual Premium", "Endorsements"], "start_identifier": [""], "field_id": 3004834, "field_name": "Annual Hazard Insurance Premium", "document_id": 9000449}], "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\.", "\\s*%"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}]