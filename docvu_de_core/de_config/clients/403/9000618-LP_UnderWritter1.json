[{"id": 3004752, "key": ["Risk Class"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Purchase Eligibility"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3004752, "field_name": "3004752  - LP Result/DU Result1", "document_id": 9000618}, {"id": 3002599, "key": ["Amortization type", "AMORTISATION TYPE"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Purpose of Refinance", "Balloon Term"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3002599, "field_name": "3002599 - Amortization Type", "document_id": 9000618}, {"id": 3004751, "key": ["Amortization months"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Offering Identifier", "Property Type"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3004751, "field_name": "3004751  - <PERSON><PERSON> (Months)", "document_id": 9000618}, {"id": 3002152, "key": ["Interest rate"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>", "ARM Qualifying Rate"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3002152, "field_name": "3002152  - Interest Rate", "document_id": 9000618, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3002146, "key": ["PURCHASE PRICE"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["TLTV"], "start_identifier": [""], "field_id": 3002146, "field_name": "3002146 - Sales Price", "document_id": 9000618, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3004750, "key": ["Purpose of Refinance"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Balloon Term"], "start_identifier": [""], "field_id": 3004750, "field_name": "3004750 - Refinance Purpose", "document_id": 9000618}]