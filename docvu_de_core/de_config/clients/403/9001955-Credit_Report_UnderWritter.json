[{"id": 3004793, "key": ["Order#"], "direction": "up", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [], "start_identifier": [], "field_id": 3004793, "field_name": "3004793 - Institution Name", "document_id": 9001955}, {"id": 3004791, "key": ["Order#"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["C<PERSON> <PERSON>an"], "start_identifier": [""], "field_id": 3004791, "field_name": "3004791 - Credit Report Number:", "document_id": 9001955, "output_format": {"string_operations_output_format": {"remove_from_end": ["\\s*\\d{2}/\\d{2}/\\d{4}$"]}}}, {"id": 3004792, "key": ["Order#"], "direction": "right", "type": "date", "return_type": "date", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["C<PERSON> <PERSON>an"], "start_identifier": [], "field_id": 3004792, "field_name": "3004792 - Credit Report Date:", "document_id": 9001955}, {"id": 3004740, "key": ["DATA SOURCES SCORE INFORMATION"], "type": "table_new", "end_identifier": ["SCORE RANGE"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004740, "field_name": "3004740 - Equifax", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "FACTA", "field_string_col": "FACTA", "probable_field_value_col": "FACTA", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": 1}}, "table_header_list": ["Item #", "Product Score", "Factor Information", "Data", "Applicant"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^\\["], "remove_from_end": ["\\]\\s*$"], "remove_spaces": true}}}, {"id": 3004739, "key": ["DATA SOURCES SCORE INFORMATION"], "type": "table_new", "end_identifier": ["TRANS UNION FICO"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004739, "field_name": "3004739 - <PERSON><PERSON><PERSON>", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "EXPERIAN FAIR ISAAC V2", "field_string_col": "EXPERIAN FAIR ISAAC V2", "probable_field_value_col": "EXPERIAN FAIR ISAAC V2", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": 1}}, "table_header_list": ["Item #", "Product Score", "Factor Information", "Data", "Applicant"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^\\["], "remove_from_end": ["\\]\\s*$"], "remove_spaces": true}}}, {"id": 3004741, "key": ["DATA SOURCES SCORE INFORMATION"], "type": "table_new", "end_identifier": [], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004741, "field_name": "3004741 - Transunion", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "SCORE CLASSIC", "field_string_col": "SCORE CLASSIC", "probable_field_value_col": "SCORE CLASSIC", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": 1}}, "table_header_list": ["Item #", "Product Score", "Factor Information", "Data", "Applicant"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^\\["], "remove_from_end": ["\\]\\s*$", "SCORERANGE:.*"], "remove_spaces": true, "regex_extract": {"pattern": "\\b\\d{3}\\b", "max_return": 3, "join_with": ","}}}}, {"id": 101, "key": ["CREDIT HISTORY"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 101, "field_name": "dummy_section1_1", "document_id": 9001955}, {"id": 3005721, "key": ["CoBorrower"], "type": "table_new", "end_identifier": ["EXPERIAN FAIR"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3005721, "field_name": "3005721 - Equifax", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "FACTA", "field_string_col": "FACTA", "probable_field_value_col": "FACTA", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": 1}}, "table_header_list": ["Item #", "Product Score", "Factor Information", "Data", "Applicant"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S", "^\\["], "remove_from_end": ["\\s*", "\\.", "\\]"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3005720, "key": ["CoBorrower"], "type": "table_new", "end_identifier": ["TRANS UNION FICO"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3005720, "field_name": "3005720 - <PERSON><PERSON><PERSON>", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "EXPERIAN", "field_string_col": "EXPERIAN", "probable_field_value_col": "EXPERIAN", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": 1}}, "table_header_list": ["Item #", "Product Score", "Factor Information", "Data Source", "Applicant Identifier"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^\\["], "remove_from_end": ["\\]\\s*$"], "remove_spaces": true}}, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3005722, "key": ["CoBorrower"], "type": "table_new", "end_identifier": ["CREDIT HISTORY"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3005722, "field_name": "3005722 - Transunion", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "SCORE CLASSIC", "field_string_col": "SCORE CLASSIC", "probable_field_value_col": "SCORE CLASSIC", "probable_place_in_table": "below_next_row", "probable_field_value_next_row": 1}}, "table_header_list": ["Item #", "Product Score", "Factor Information", "Data Source", "Applicant Identifier"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^\\["], "remove_from_end": ["\\]\\s*$"], "remove_spaces": true}}, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 3004794, "key": ["INQUIRIES MADE IN THE LAST 120 DAYS"], "type": "table_new", "return_type": "table", "end_identifier": ["ADDRESS INFORMATION"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "field_id": 3004794, "field_name": "3004794 - Inquiry Date", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Inquiry Date", "field_string_col": 4, "probable_field_value_col": 2, "probable_place_in_table": "below_next_row", "probable_field_value_next_row": false, "value_return_as_table": true, "table_group_rows_by_column": true, "group_key": "B"}}, "table_header_list": ["Item #", "Inquiry Made By", "Inquiry Date", "Data Source", "Applicant Identifier"], "table_structure": "vertical"}, {"id": 3004795, "key": ["ADDRESS INFORMATION"], "direction": "down_multi_page", "type": "text", "return_type": "table", "end_identifier": ["AKA INFORMATION"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "multi_page_value": true, "exclude_header": true, "exclude_footer": true, "field_id": 3004795, "field_name": "3004795  - Address Reported", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Address", "field_string_col": 6, "probable_field_value_col": 1, "probable_place_in_table": "same_row", "probable_field_value_next_row": 0, "value_return_as_table": true, "table_group_rows_by_column": true, "group_key": "B"}}, "table_header_list": ["Item #", "Address", "Address", "Since", "Date", "Data Source", "Applicant"], "table_structure": "vertical", "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3004795, "key": ["ADDRESS INFORMATION"], "direction": "down_multi_page", "type": "text", "return_type": "table", "end_identifier": ["AKA INFORMATION"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "multi_page_value": true, "exclude_header": true, "exclude_footer": true, "field_id": 3004795, "field_name": "3004796 - Month & Year Reported", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Date Reported", "field_string_col": 6, "probable_field_value_col": 1, "probable_place_in_table": "same_row", "probable_field_value_next_row": 0, "value_return_as_table": true, "table_group_rows_by_column": true, "group_key": "B"}}, "table_header_list": ["Item #", "Address", "Address", "Since", "Date", "Data Source", "Applicant"], "table_structure": "vertical", "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3004743, "key": ["AKA INFORMATION"], "direction": "down_multi_page", "type": "text", "return_type": "table", "end_identifier": ["EMPLOYMENT INFORMATION"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "multi_page_value": true, "exclude_header": true, "exclude_footer": true, "field_id": 3004743, "field_name": "3004743  - AKA Names", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Name", "field_string_col": 3, "probable_field_value_col": 1, "probable_place_in_table": "same_row", "probable_field_value_next_row": 0, "value_return_as_table": true, "table_group_rows_by_column": true, "group_key": "B"}}, "table_header_list": ["Item #", "Names", "Data Source", "Applicant Identifier"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^AKA:\\s*", "^NAME:\\s*", "^NICKNAME:\\s*"], "remove_special_chars_from_beginning": true}}, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}, {"id": 3005724, "key": ["AKA INFORMATION"], "direction": "down_multi_page", "type": "text", "return_type": "table", "end_identifier": ["EMPLOYMENT INFORMATION"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "multi_page_value": true, "exclude_header": true, "exclude_footer": true, "field_id": 3005724, "field_name": "3005724  - AKA Names", "document_id": 9001955, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "Name", "field_string_col": 3, "probable_field_value_col": 1, "probable_place_in_table": "same_row", "probable_field_value_next_row": 0, "value_return_as_table": true, "table_group_rows_by_column": true, "group_key": "C"}}, "table_header_list": ["Item #", "Names", "Data Source", "Applicant Identifier"], "table_structure": "vertical", "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^NICKNAME:\\s*", "^SIMILAR NAME:\\s*", "^AKA:\\s*", "^NAME:\\s*"], "remove_special_chars_from_beginning": true}}, "additional_info": {"continue_to_next_page_without_identifier": true, "stop_on_end_identifier": true}}]