{"document_type": "deed_of_trust", "extraction_rules": {"parties": {"fields": [{"name": "trustor", "search_patterns": ["Trustor", "<PERSON><PERSON><PERSON>"], "extraction_type": "text_after_pattern", "required": true}, {"name": "trustee", "search_patterns": ["Trustee"], "extraction_type": "text_after_pattern", "required": true}, {"name": "beneficiary", "search_patterns": ["Beneficiary", "<PERSON><PERSON>"], "extraction_type": "text_after_pattern", "required": true}]}, "property_info": {"fields": [{"name": "property_address", "search_patterns": ["Property Address", "Real Property"], "extraction_type": "address_pattern", "required": true}, {"name": "legal_description", "search_patterns": ["Legal Description", "Described as follows"], "extraction_type": "text_block", "required": false}]}, "loan_info": {"fields": [{"name": "principal_amount", "search_patterns": ["Principal Amount", "Sum of"], "extraction_type": "currency_pattern", "required": true}, {"name": "note_date", "search_patterns": ["Note Date", "Dated"], "extraction_type": "date_pattern", "required": true}]}}, "validation_rules": {"required_fields": ["trustor", "beneficiary", "property_address", "principal_amount"], "field_validations": {"principal_amount": {"min_value": 1000, "error_message": "Principal amount must be at least $1,000"}}}}