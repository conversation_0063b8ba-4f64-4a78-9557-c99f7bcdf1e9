{"max_upper_block": 3, "use_upper_split_percentage": 0.45, "max_lines_for_header": 5, "max_upper_lines_for_key_search": 8, "debug": true, "enable_multi_variant": true, "confidence_threshold": 0.6, "enable_confidence_scoring": true, "case_sensitive_matching": false, "partial_string_matching": true, "use_legacy_mode": false, "variant_priority_order": ["1003_application_form_v1", "1003_application_form_v2", "ach_authorization_form", "closing_disclosure", "appraisal_report", "loan_estimate"], "default_return": "docvu_de_core/de_config/default_de.json", "document_types": {"1003_application_form_v1": {"return": "docvu_de_core/de_config/1003_v1_de.json", "header": {"include_strings": ["1003", "UNIFORM RESIDENTIAL LOAN APPLICATION", "SECTION I", "BORROWER INFORMATION"], "exclude_strings": ["REVISED", "NEW VERSION", "SPANISH", "BILINGUAL"]}, "body": {"include_strings": ["<PERSON><PERSON><PERSON>", "Co-<PERSON><PERSON><PERSON>", "Property Address", "<PERSON><PERSON>", "Purpose of <PERSON>an"], "exclude_strings": []}, "big_font": {"include_strings": ["1003", "LOAN APPLICATION"], "exclude_strings": [], "height_threshold": 0.6, "num_clusters": 3}}, "1003_application_form_v2": {"return": "docvu_de_core/de_config/1003_v2_de.json", "header": {"include_strings": ["1003", "REVISED", "UNIFORM RESIDENTIAL LOAN APPLICATION", "NEW VERSION"], "exclude_strings": ["SPANISH", "BILINGUAL", "SHORT FORM"]}, "body": {"include_strings": ["Joint Credit", "Co-Borrower Information", "Employment Information", "Monthly Income"], "exclude_strings": []}}, "ach_authorization_form": {"return": "docvu_de_core/de_config/ach_auth_de.json", "header": {"include_strings": ["ACH Authorization", "Autopay", "Spring EQ", "Sign Up For Autopay", "Electronic Payment"], "exclude_strings": ["1003", "Loan Application", "Closing Disclosure"]}, "body": {"include_strings": ["Bank Account", "Routing Number", "Account Number", "Authorization", "Electronic Funds Transfer"], "exclude_strings": []}}, "closing_disclosure": {"return": "docvu_de_core/de_config/closing_disclosure_de.json", "header": {"include_strings": ["Closing Disclosure", "Final Terms", "Loan <PERSON>", "Projected Payments"], "exclude_strings": ["1003", "Application", "ACH Authorization"]}, "body": {"include_strings": ["<PERSON><PERSON>", "Interest Rate", "Monthly Principal", "Closing Costs", "Cash to Close"], "exclude_strings": []}}, "appraisal_report": {"return": "docvu_de_core/de_config/appraisal_de.json", "header": {"include_strings": ["Appraisal Report", "Property Valuation", "Market Value", "URAR"], "exclude_strings": ["1003", "Loan Application", "Closing Disclosure"]}, "body": {"include_strings": ["Subject Property", "Comparable Sales", "Market Analysis", "Property Description", "Appraised Value"], "exclude_strings": []}}, "loan_estimate": {"return": "docvu_de_core/de_config/loan_estimate_de.json", "header": {"include_strings": ["Loan Estimate", "Estimated Terms", "Projected Payments", "<PERSON>an <PERSON>"], "exclude_strings": ["Closing Disclosure", "Final Terms", "1003"]}, "body": {"include_strings": ["<PERSON><PERSON>", "Interest Rate", "Estimated Monthly Payment", "Estimated Closing Costs", "Estimated Cash to Close"], "exclude_strings": []}}}}