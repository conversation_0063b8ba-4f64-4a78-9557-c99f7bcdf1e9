[{"id": 3002135, "key": ["Borrower 1", "Borrower I"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Submission Number"], "start_identifier": [""], "field_id": 3002135, "field_name": "Borrower Name", "document_id": 9000089}, {"id": 3002136, "key": ["Borrower 2"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Submission Date"], "start_identifier": [""], "field_id": 3002136, "field_name": "Borrower 2", "document_id": 9000089}, {"id": 80017404, "key": ["Loan Number"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Submission Date"], "start_identifier": [""], "field_id": 80017404, "field_name": "Loan Number", "document_id": 9000089}, {"id": 3002141, "key": ["Property Information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Property Address", "Number of Units"], "start_identifier": [""], "field_id": 3002141, "field_name": "Subject Property", "document_id": 9000089, "output_format": {"address_parser_output_format": {"get_line1_and_line2": true, "from_field": "Subject Property"}}}, {"id": 3002143, "key": ["Property Information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Property Address", "Number of Units"], "start_identifier": [""], "field_id": 3002143, "field_name": "city", "document_id": 9000089, "output_format": {"address_parser_output_format": {"get_city": true, "from_field": "city"}}}, {"id": 3002144, "key": ["Property Information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Property Address", "Number of Units"], "start_identifier": [""], "field_id": 3002144, "field_name": "state", "document_id": 9000089, "output_format": {"address_parser_output_format": {"get_state": true, "from_field": "state"}}}, {"id": 3002145, "key": ["Property Information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Property Address", "Number of Units"], "start_identifier": [""], "field_id": 3002145, "field_name": "Zip_code", "document_id": 9000089, "output_format": {"address_parser_output_format": {"get_zip_code": true, "from_field": "Zip_code"}}}, {"id": 3002585, "key": ["Number of Units"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Occupancy"], "start_identifier": [""], "field_id": 3002585, "field_name": "Number of Units", "document_id": 9000089}, {"id": 3002588, "key": ["Property Type"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Occupancy"], "start_identifier": [""], "field_id": 3002588, "field_name": "Property type", "document_id": 9000089}, {"id": 3002587, "key": ["Occupancy Status"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 3002587, "field_name": "Occupancy", "document_id": 9000089}, {"id": 3002614, "key": ["Recommendation"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Submission Number"], "start_identifier": [""], "field_id": 3002614, "field_name": "Aus_Decision_Recommendation", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_numbers": true, "remove_special_chars": true, "remove_from_beginning": ["^\\d+"], "remove_from_end": ["\\d+$"], "retain_only_alpha": true}}}, {"id": 3002591, "key": ["Loan Type"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON>an <PERSON>"], "start_identifier": [], "field_id": 3002591, "field_name": "Loan_program", "document_id": 9000089}, {"id": 3002593, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>"], "start_identifier": [], "field_id": 3002593, "field_name": "Loan_Purpose", "document_id": 9000089}, {"id": 3002154, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Bought Down rate", "Property Information"], "start_identifier": [], "field_id": 3002154, "field_name": "Loan_Type", "document_id": 9000089}, {"id": 3002133, "key": ["Total Loan Amount"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Amortization Type"], "start_identifier": [""], "field_id": 3002133, "field_name": "Loan_amount", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_special_chars_from_beginning": true, "remove_from_end": ["\\s*\\$\\.", "^\\$", "^S"]}}}, {"id": 3002152, "key": ["Note Rate"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Loan Type"], "start_identifier": [""], "field_id": 3002152, "field_name": "rate", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\.", "\\s*%"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3002153, "key": ["<PERSON>an <PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Amortization Type"], "start_identifier": [""], "field_id": 3002153, "field_name": "Term", "document_id": 9000089}, {"id": 3002599, "key": ["Amortization Type"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON><PERSON>"], "start_identifier": [""], "field_id": 3002599, "field_name": "Amortization_Type", "document_id": 9000089}, {"id": 3002146, "key": ["(purchase"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["transactions)", "<PERSON><PERSON>"], "start_identifier": [""], "field_id": 3002146, "field_name": "Sale_Price", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"]}}}, {"id": 3002134, "key": ["<PERSON><PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Appraised Value"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3002134, "field_name": "Appraisal_Value", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_special_chars_from_beginning": true, "remove_from_beginning": ["^.*\\$"]}}}, {"id": 3002606, "key": ["Housing Expense Ratio"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Loan Type"], "start_identifier": [""], "field_id": 3002606, "field_name": "Front_End", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\.", "\\s*%"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3002608, "key": ["Debt-to-Income Ratio"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON>an <PERSON>"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3002608, "field_name": "Back_End", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\.", "\\s*%"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 80017389, "key": ["HCLTV", "HCLT/"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Note Rate"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80017389, "field_name": "LTV/CLTV/HCLTV", "document_id": 9000089}, {"id": 3002604, "key": ["Amt. Subordinate Fin."], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Note Rate"], "start_identifier": [""], "field_id": 3002604, "field_name": "Second_Mortgage", "document_id": 9000089, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}]