[{"id": 106, "key": ["section 6"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 106, "field_name": "dummy_section6_6", "document_id": 9000001}, {"id": 3001660, "key": ["Borrower Signature"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 3001660, "field_name": "Application Date", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3001666, "key": ["Loan Identifier"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "Agency Case"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001666, "field_name": "Loan Number", "document_id": 9000001}, {"id": 101, "key": ["borrower information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 101, "field_name": "dummy_section1_1", "document_id": 9000001}, {"id": 3001688, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001688, "field_name": "Borrower_name", "document_id": 9000001}, {"id": 107, "key": ["borrower information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 107, "field_name": "dummy_section1_11", "document_id": 9000001}, {"id": 3001689, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001689, "field_name": "Co-Borrower_name", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 1003, "key": ["Section 4"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Creditor Name"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 1003, "field_name": "Loan_Purpose_checkbox_save_section", "sub_keys": ["UW_Section4_loan_property_information"], "document_id": 9000001}, {"id": 115, "key": ["Original Cost"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Title to the Property Will"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 115, "field_name": "property_save_section", "sub_keys": ["UW_project_type_omr"], "document_id": 9000001}, {"id": 50055, "key": ["Tenancy in Common"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["<PERSON>an <PERSON>"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 50055, "field_name": "Loan_program_save_section", "sub_keys": ["UW_Mortgage_Loan_info_omr"], "document_id": 9000001}, {"id": 50000, "key": ["Mortgage Type"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Loan Features"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 50000, "field_name": "Amortization_Type_save_section", "sub_keys": ["UW_Amortization_info_omr"], "document_id": 9000001}, {"id": 817337, "key": ["community property state"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["energy improvement"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 817337, "field_name": "Loan_type_refinance", "sub_keys": ["UW_Loan_Refinance_Type"], "document_id": 9000001}, {"id": 3002597, "key": ["Note Rate"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON>an <PERSON>"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3002597, "field_name": "Rate", "document_id": 9000001, "output_format": {"string_operations_output_format": {"contains": ["(\\d+\\.?\\d*)\\s*%"], "remove_from_end": ["\\s*%"]}}}, {"id": 3001665, "key": ["<PERSON>an <PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["(months)"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 3001665, "field_name": "Term", "document_id": 9000001}, {"id": 500556, "key": ["L4. Qualifying the Borrower"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name(s)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 500556, "field_name": "L4_qualifying_borrower_minimum_required_funds_cash_back_save", "sub_keys": ["UW_L4_Qualifying_borrower_minimum_required_funds"], "document_id": 9000001}]