{"max_upper_block": 3, "use_upper_split_percentage": 0.45, "max_lines_for_header": 5, "max_upper_lines_for_key_search": 8, "debug": true, "enable_variant_detection": true, "variant_confidence_threshold": 0.5, "case_sensitive_matching": false, "partial_string_matching": true, "default_return": "docvu_de_core/de_config/default_1003_de.json", "document_types": {"1003_variant_1_original": {"return": "docvu_de_core/de_config/1003_variant_1_de.json", "header": {"include_strings": ["1003", "UNIFORM RESIDENTIAL LOAN APPLICATION", "SECTION I", "BORROWER INFORMATION"], "exclude_strings": ["REVISED", "NEW VERSION", "SPANISH", "BILINGUAL", "SHORT FORM"]}, "body": {"include_strings": ["Borrower Information", "Property Information", "Loan Information", "Employment Information"], "exclude_strings": ["Co-<PERSON><PERSON><PERSON>", "Joint Credit", "Abbreviated"]}, "big_font": {"include_strings": ["1003", "UNIFORM RESIDENTIAL LOAN APPLICATION"], "exclude_strings": []}}, "1003_variant_2_revised": {"return": "docvu_de_core/de_config/1003_variant_2_de.json", "header": {"include_strings": ["1003", "REVISED", "NEW VERSION", "UNIFORM RESIDENTIAL", "UPDATED FORM"], "exclude_strings": ["OLD VERSION", "SPANISH", "ORIGINAL"]}, "body": {"include_strings": ["Borrower Information", "Co-<PERSON><PERSON><PERSON>", "Joint Credit", "Updated Fields", "Revised Section"], "exclude_strings": ["OLD FORM", "DEPRECATED"]}, "big_font": {"include_strings": ["REVISED", "NEW VERSION"], "exclude_strings": []}}, "1003_variant_3_spanish": {"return": "docvu_de_core/de_config/1003_variant_3_de.json", "header": {"include_strings": ["1003", "SPANISH", "SOLICITUD", "PRESTAMO", "BILINGUAL", "ESPAÑOL"], "exclude_strings": ["ENGLISH ONLY", "MONOLINGUAL"]}, "body": {"include_strings": ["Información del Prestatario", "Información de la Propiedad", "Información del Préstamo", "Prestatario", "Co-Prestatario"], "exclude_strings": ["English", "Only English"]}, "big_font": {"include_strings": ["SOLICITUD", "ESPAÑOL"], "exclude_strings": []}}, "1003_variant_4_short_form": {"return": "docvu_de_core/de_config/1003_variant_4_de.json", "header": {"include_strings": ["1003", "SHORT FORM", "ABBREVIATED", "SIMPLIFIED", "QUICK APPLICATION"], "exclude_strings": ["FULL FORM", "COMPLETE", "DETAILED"]}, "body": {"include_strings": ["Basic Information", "Essential Details", "Quick Entry", "Simplified Section"], "exclude_strings": ["Detailed Information", "Complete Section", "Full Details"]}, "big_font": {"include_strings": ["SHORT FORM", "SIMPLIFIED"], "exclude_strings": []}}, "1003_variant_5_digital": {"return": "docvu_de_core/de_config/1003_variant_5_de.json", "header": {"include_strings": ["1003", "DIGITAL", "ELECTRONIC", "E-FORM", "ONLINE APPLICATION"], "exclude_strings": ["PAPER", "PRINTED", "MANUAL"]}, "body": {"include_strings": ["Digital Signature", "Electronic Consent", "Online Submission", "E-Signature", "Digital Processing"], "exclude_strings": ["Handwritten", "Manual", "Paper Form"]}, "big_font": {"include_strings": ["DIGITAL", "ELECTRONIC"], "exclude_strings": []}}, "1003_variant_6_state_specific": {"return": "docvu_de_core/de_config/1003_variant_6_de.json", "header": {"include_strings": ["1003", "STATE SPECIFIC", "CALIFORNIA", "CA ADDENDUM", "STATE REQUIREMENTS", "TEXAS", "FLORIDA", "NEW YORK"], "exclude_strings": ["FEDERAL ONLY", "STANDARD", "GENERIC"]}, "body": {"include_strings": ["State Requirements", "California Disclosure", "State-Specific", "Local Regulations", "State Addendum"], "exclude_strings": ["Federal Standard", "Generic Form"]}, "big_font": {"include_strings": ["STATE SPECIFIC", "CALIFORNIA", "TEXAS"], "exclude_strings": []}}}}