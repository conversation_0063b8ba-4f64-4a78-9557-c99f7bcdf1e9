[{"id": 3001660, "key": ["ACKNOWLEDGEMENT AND AGREEMENT"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["INFORMATION FOR GO<PERSON><PERSON><PERSON>MENT MONITORING PURPOSES", "To be Completed by the Loan"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001660, "field_name": "Application Date", "document_id": 9000001, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 3001666, "key": ["LOAN #:"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Uniform Residential Loan Application"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001666, "field_name": "Loan Number", "document_id": 9000001}, {"id": 3001688, "key": ["<PERSON><PERSON><PERSON>'s Name"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Social Security Number"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001688, "field_name": "Borrower_Name", "document_id": 9000001}, {"id": 3001689, "key": ["Co-<PERSON><PERSON><PERSON>'s Name"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Social Security"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001689, "field_name": "Co_Borrower_Name", "document_id": 9000001}, {"id": 3001675, "key": ["No. of Units"], "direction": "right", "type": "address", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["County"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001675, "field_name": "Property_Street", "document_id": 9000001, "output_format": {"address_parser_output_format": {"get_line1": true, "from_field": "Property_Street"}}}, {"id": 3001677, "key": ["No. of Units"], "direction": "right", "type": "address", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["County"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001677, "field_name": "Property_City", "document_id": 9000001, "output_format": {"address_parser_output_format": {"get_city": true, "from_field": "Property_City"}}}, {"id": 3001678, "key": ["No. of Units"], "direction": "right", "type": "address", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["County"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001678, "field_name": "Property_State", "document_id": 9000001, "output_format": {"address_parser_output_format": {"get_state": true, "from_field": "Property_State"}}}, {"id": 3001679, "key": ["No. of Units"], "direction": "right", "type": "number", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["County"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001679, "field_name": "Property_ZIP", "document_id": 9000001, "output_format": {"address_parser_output_format": {"get_zip_code": true, "from_field": "Property_ZIP"}}}, {"id": 3002584, "key": ["No. of Units"], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Legal Description"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3002584, "field_name": "Units", "document_id": 9000001}, {"id": 10551, "key": ["Legal"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["original cost"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 10551, "field_name": "occupancy_checkbox_save_section", "sub_keys": ["UW_OLD_occupancy_old_omr"], "document_id": 9000001}, {"id": 80022869, "key": ["Purpose of <PERSON>an"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Complete this line"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022869, "field_name": "LoanPurpose", "sub_keys": ["UW_OLD_loan_purpose_omr"], "document_id": 9000001}, {"id": 12500, "key": ["TYPE OF MORTGAGE"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Amortization"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 12500, "field_name": "Loan_program_save_section", "sub_keys": ["UW_OLD_Mortgage_applied_for_save"], "document_id": 9000001}, {"id": 111111, "key": ["Amount"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["II. PROPERTY"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 111111, "field_name": "Amortization_Type_save", "sub_keys": ["UW_OLD_amortization_type_omr"], "document_id": 9000001}, {"id": 3001680, "key": ["Amount"], "direction": "just_down", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Subject Property Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001680, "field_name": "LoanAmount", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3002595, "key": ["a. Purchase Price"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["If you answer", "N you answer"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3002595, "field_name": "Seller_Contribution_($)_purchase_price", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\.+", "[^0-9]+$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3002601, "key": ["a. Purchase Price"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["If you answer", "N you answer"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3002601, "field_name": "Sale Price", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\.+", "[^0-9]+$"], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3001669, "key": ["d. Refinance (loci. debts to be paid off)"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["b. Have you been"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001669, "field_name": "Loan_Type_refinance", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$\\.", "^\\$", "^S"], "remove_from_end": ["\\s*", "\\."], "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true}}}, {"id": 3002603, "key": ["j. Subordinate financing"], "direction": "right", "type": "amount", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["or loan guarantee.", "or loan", "<PERSON><PERSON>'s"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3002603, "field_name": "Second Mortgage", "document_id": 9000001}, {"id": 3002597, "key": ["Interest Rate"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["(street,", "Subject Property Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3002597, "field_name": "Rate", "document_id": 9000001}, {"id": 3001665, "key": ["No. of Months"], "direction": "down_inline", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["II. PROPERTY", "Subject Property Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 3001665, "field_name": "Term", "document_id": 9000001}]