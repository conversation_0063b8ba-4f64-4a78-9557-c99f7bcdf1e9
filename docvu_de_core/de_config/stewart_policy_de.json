[{"id": 0, "key": ["issued by", "SCHEDULE A", "loan policy schedule a", "name and address", "Loan Policy of Title Insurance - Schedule A"], "direction": "up_till_start", "type": "text", "multi_line_value": true, "return_type": "text", "probable_type": "Header", "max_cblock_iter": 4, "end_identifier": ["order no", "policy no"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 60000001, "field_name": "Policy_Type_Text", "document_id": 12345678, "alternate_locations": [{"key": ["Schedule A", "File No."], "direction": "down", "type": "text", "multi_line_value": true, "probable_place": "in_footer", "end_identifier": ["Page", "Policy Serial No."], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 60000002, "field_name": "Policy_Type_Text", "document_id": 12345678}], "post_process": {"policy_type_text_post_processor": {"custom": true}}}, {"id": 2, "key": ["Owner's Policy of Title Insurance No:", "Policy no", "Policy na", "policy number", "Lenders Policy", "policy serial no"], "direction": "right", "type": "text", "multi_line_value": false, "probable_place": "Individual", "return_type": "text", "use_match": "fuzzy", "end_identifier": ["Loan", "Amount", "Effective", "reference", "all", "office", "endorsements", "property", "file", "issued", "associated", "address", "issued", "patch", "lender", "title", "date", "customer", "case", "po box", "this policy", "Schedule", "Loan", "No", "$", "Amount", "Effective", "Address", "page", "policy", "premium", "Agent ID", "Order Number"], "start_identifier": [], "possible_page_numbers": [7], "field_id": 60000003, "field_name": "Full_Policy_Number", "document_id": 12345678, "additional_info": {"alternate_locations": "in_footer"}, "output_format": {"string_operations_output_format": {"remove_spaces": true, "remove_special_chars_from_beginning": true, "remove_special_chars_from_end": true, "replace_from_beginning": [["0-", "O-"], ["D-", "O-"], ["Q-", "O-"], ["0", "O"]]}}}]