{"document_type": "appraisal_report", "extraction_rules": {"property_info": {"fields": [{"name": "property_address", "search_patterns": ["Property Address", "Subject Property"], "extraction_type": "address_pattern", "required": true}, {"name": "appraised_value", "search_patterns": ["Market Value", "Appraised Value", "Opinion of Value"], "extraction_type": "currency_pattern", "required": true}, {"name": "effective_date", "search_patterns": ["Effective Date", "Date of Inspection"], "extraction_type": "date_pattern", "required": true}]}, "appraiser_info": {"fields": [{"name": "appraiser_name", "search_patterns": ["Appraiser", "Appraiser Name"], "extraction_type": "text_after_pattern", "required": true}, {"name": "appraiser_license", "search_patterns": ["License Number", "State License"], "extraction_type": "pattern_match", "pattern": "[A-Z0-9-]+", "required": true}]}, "property_details": {"fields": [{"name": "property_type", "search_patterns": ["Property Type", "Single Family", "Condominium"], "extraction_type": "text_after_pattern", "required": false}, {"name": "square_footage", "search_patterns": ["Square Feet", "Sq Ft", "GLA"], "extraction_type": "number_pattern", "required": false}, {"name": "year_built", "search_patterns": ["Year Built", "Built"], "extraction_type": "year_pattern", "required": false}]}}, "validation_rules": {"required_fields": ["property_address", "appraised_value", "appraiser_name"], "field_validations": {"appraised_value": {"min_value": 1000, "error_message": "Appraised value must be at least $1,000"}}}}