[{"id": 80012124, "key": ["SCHEDULE A", "loan policy schedule a", "Loan Policy of Title Insurance Schedule A"], "direction": "up", "type": "text", "multi_line_value": true, "probable_type": "Header", "return_type": "text", "max_cblock_iter": 4, "end_identifier": [""], "start_identifier": [""], "field_id": 80012124, "field_name": "Policy_Type_Text", "document_id": 9002231, "alternate_locations": [{"key": ["Schedule A"], "direction": "down", "type": "text", "multi_line_value": true, "probable_place": "in_footer", "end_identifier": ["Page"], "start_identifier": [""], "field_id": 80012124, "field_name": "Policy_Type_Text", "document_id": 9002231}], "post_process": {"policy_type_text_post_processor": {"custom": true}}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["[a-z\\s\\W]+"], "remove_from_beginning_ignore_case": false, "remove_special_chars_from_beginning": true, "output_format": {"remove_from_end": ["(?i)\\b- Schedule A\\b", "Schedule A", "-- Schedule A", "- Schedule A", "- Schedule B", "Sch A", "Sch B", "- Sch A", "-- Sch A", "(?i)\\*patch\\*"]}}}}, {"id": 80012126, "key": ["name and address of title insurance company", "Name and Address of"], "direction": "right", "return_type": "text", "type": "text", "multi_line_value": false, "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["Policy", "file", "issuing", "p.o."], "start_identifier": [""], "field_id": 80012126, "field_name": "Underwriter", "document_id": 9002231, "additional_info": {"nth_line": 1}, "alternate_locations": [{"key": ["schedule a"], "direction": "up", "type": "text", "multi_line_value": false, "probable_place": "just_above", "use_match": "fuzzy", "end_identifier": ["schedule a"], "start_identifier": [""], "field_id": 80012126, "field_name": "Underwriter", "document_id": 9002231}, {"key": ["ISSUED BY"], "direction": "down", "type": "text", "multi_line_value": false, "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["Policy", "file", "issuing"], "start_identifier": [""], "field_id": 80012126, "field_name": "Underwriter", "document_id": 9002231}, {"key": ["page"], "direction": "down", "type": "text", "multi_line_value": true, "probable_place": "in_footer", "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "field_id": 80012126, "field_name": "Underwriter", "document_id": 9002231, "additional_info": {"is_in_center": true, "has_keyword": ["title"]}}, {"key": ["company name"], "direction": "up", "type": "text", "multi_line_value": false, "probable_place": "just_above", "use_match": "fuzzy", "end_identifier": ["company name"], "start_identifier": ["countersignature"], "field_id": 80012126, "field_name": "Underwriter", "document_id": 9002231}]}, {"id": 80012125, "key": ["Loan Policy number", "Loan Policy no", "Policy no", "Policy na", "policy number", "Lenders Policy", "Policy #", "policy serial no"], "direction": "right", "search_in_tables": true, "probable_place_in_table": ["same_row_value", "below_row_value"], "return_type": "text", "type": "text", "multi_line_value": false, "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["Loan", "Amount", "Effective", "reference", "all", "office", "endorsements", "property", "file", "issued", "associated", "address", "issued", "patch", "lender", "title", "date", "customer", "case", "po box", "this policy", "Schedule", "Loan", "No", "$", "Amount", "Effective", "Address", "page", "policy", "premium"], "start_identifier": ["Schedule", ""], "field_id": 80012125, "field_name": "Full_Policy_Number", "document_id": 9002231, "additional_info": {"starts_with": ["M", "U", "u", "m"], "found_val": []}, "post_process": {"remove_far_elements_right_one_line": {"custom": false}}, "output_format": {"string_operations_output_format": {"remove_special_chars_from_end": true}}}, {"id": 80012127, "key": ["Agent file number", "agent file no", "Office File Number", "Office File No", "File Number", "File No", "Contract Number", "contract no", "case number", "Case no", "case"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "probable_place": "Individual", "search_in_tables": true, "probable_place_in_table": ["same_row_value", "below_row_value"], "choose_row_in_table_based_on": "Full_Policy_Number", "use_match": "fuzzy", "end_identifier": ["Policy", "Amount", "Premium", "loan", "Contract", "street", "schedule", "alta", "of", "DATE"], "start_identifier": [""], "field_id": 80012127, "field_name": "File_Number", "document_id": 9002231, "additional_info": {"alternate_locations": "in_footer", "find_in_zone": true}, "post_process": {"remove_far_elements_right_one_line": {"custom": false}}, "output_format": {"string_operations_output_format": {"remove_spaces": true}}}, {"id": 80012219, "key": ["Loan Number", "Loan No"], "direction": "right", "return_type": "text", "type": "text", "multi_line_value": false, "probable_place": "Individual", "use_match": "fuzzy", "end_identifier": ["Policy", "Amount", "Premium", "Contract", "street", "schedule", "alta", "of", "Date", "Address", "File", "Property"], "start_identifier": [""], "field_id": 80012219, "field_name": "Loan_Number", "document_id": 9002231, "max_page_to_search": 2, "post_process": {"remove_far_elements_right_one_line": {"custom": false}}}, {"id": 80012128, "key": ["Order Number", "Order No"], "direction": "right", "return_type": "text", "type": "text", "multi_line_value": false, "probable_place": "Individual", "probable_type": "Header", "max_cblock_iter": 2, "use_match": "fuzzy", "end_identifier": ["Policy", "Amount", "Premium", "loan", "Contract", "street", "schedule", "alta", "of"], "start_identifier": [""], "field_id": 80012128, "field_name": "Order_Number", "document_id": 9002231, "post_process": {"remove_far_elements_right_one_line": {"custom": false}}}, {"id": 80012131, "key": ["Address Reference", "Address for reference only", "Address of Property", "Street address of the land", "street address", "Property Address", "Address:", "Premises known as"], "direction": "right", "return_type": "text", "type": "address", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Amount", " Amount of Insurance", "premium", "This policy incorporates", "policy", "(For Company Reference Purposes Only)", "county", "Unit/Lot"], "start_identifier": [""], "field_id": 80012131, "field_name": "Property_Full_Address", "max_page_to_search": 1, "document_id": 9002231, "additional_info": {"search_after": ["street address", "county and state", "(For identification purposes only)"]}, "sub_keys": ["address_information"], "post_process": {"one_of": false, "remove_next_line_if_starts_with_point": {"custom": false}, "string_operations_post_processor": {"remove_from_beginning": ["of the Land:", "(For identification purposes only):"], "remove_from_end": ["00000"]}, "address_splitter_post_processor": {"return_first": true}}}, {"id": 80012136, "key": ["Amount of Insurance", "policy amount", "Amount of Ins"], "direction": "right", "return_type": "text", "search_in_tables": true, "choose_row_in_table_based_on": "Full_Policy_Number", "probable_place_in_table": ["same_row_value", "below_row_value"], "type": "amount", "multi_line_value": false, "probable_place": "Individual", "end_identifier": ["premium", "loan", "date", "total", "address", "name", "whichever"], "start_identifier": [""], "field_id": 80012136, "field_name": "Policy_Liability", "document_id": 9002231, "post_process": {"one_of": false, "remove_far_elements_right_one_line": {}}, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$"], "remove_substrings": true}}}, {"id": 80012129, "key": ["Date of policy", "Effective Date", "Date/Time of Policy", "Policy Date"], "direction": "right", "return_type": "text", "search_in_tables": true, "probable_place_in_table": ["same_row_value", "below_row_value"], "choose_row_in_table_based_on": "Full_Policy_Number", "type": "date", "multi_line_value": false, "probable_place": "Individual", "end_identifier": ["AM", "PM", "Name", "premium", "amount", "loan", "1. name", "at"], "start_identifier": [""], "field_id": 80012129, "field_name": "Effective_Date", "document_id": 9002231, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 80012130, "key": ["Total Charge", "Policy Premium", "Premium amount", "Premium(Risk Rate)", "Premium"], "direction": "right", "type": "amount", "probable_place": "Individual", "multi_line_value": false, "end_identifier": ["Date", "amount", "endorsements", "1. Name"], "start_identifier": [""], "field_id": 80012130, "field_name": "Policy_Premium", "document_id": 9002231, "max_page_to_search": 2, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["\\s*\\$", "^.*?\\$"], "remove_special_chars_from_end": true, "remove_special_chars_from_beginning": true, "remove_substrings": true, "remove_from_end": ["[a-zA-Z][a-zA-Z0-9]*$"]}}}, {"id": 80012137, "key": ["Name of Insured"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "include_key": true, "use_match": "fuzzy", "end_identifier": ["2. The estate or interest", "The estate or interest", "title is vested in", "<PERSON><PERSON>", "2. T"], "start_identifier": [""], "field_id": 80012137, "field_name": "Insured_Full_Name", "document_id": 9002231, "additional_info": {"nth_line": 1}, "post_process": {"remove_next_line_if_starts_with_point": {"custom": false}}}, {"id": 80012138, "key": ["The estate or interest", "interest in the Land identified "], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Title is vested in:", "title to the estate", "4. The insured mortgage and", "The insured mortgage and"], "start_identifier": ["insured mortgage is"], "field_id": 80012138, "field_name": "Interest_Estate", "document_id": 9002231, "additional_info": {"search_in_key": true}, "post_process": {"string_operations_post_processor": {"contains": ["fee simple", "leasehold ownership", "Contract Purchaser", "fee/easement", "feesimple", "fee", "easement", "leashold", "equitable", "leasehold"]}}}, {"id": 80012139, "key": ["title to the estate or interest in the land is vested in", "Title is vested in", "Title to is vested in", "title to the estate", "land is vested in", "Title is insured as vested in"], "direction": "right", "return_type": "text", "type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["4. The Insured Mortgage and", "The Insured Mortgage and", "The land referred to", "NOTE", "4. The", "4. <PERSON><PERSON>", "4. Th", "3. Th"], "start_identifier": ["Title is vested in", "title to the estate", "land is vested in"], "field_id": 80012139, "field_name": "Vesting", "document_id": 9002231, "additional_info": {"nth_line": 1}, "post_process": {"remove_next_line_if_starts_with_point": {"custom": false}}, "output_format": {"schedule_a_output_format": {"custom": true}}, "sub_keys": ["vesting_information"]}, {"id": 80012140, "key": ["The Insured Mortgage and its assignments", "The insured mortgage and assignments", "The Insured Mortgage and the assignments", "The insured Deed of Trust and its assignments", "The Security Instrument", "The mortgage and assignments", "The Insured Mortgage or Deed of Trust and assignments", "The mortgage, herein referred to as the insured mortgage,", "The mortgage or deed of trust and assignments", "The insured Security Deed and its assignments", "Deed of Trust to secure an indebtedness", "The Insured premises is"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "end_identifier": ["The Land referred to in this policy", "The Land referred to in this policy is described as follows:", "The land is described as follows", "5. The ", ""], "start_identifier": [""], "field_id": 80012140, "field_name": "Loan_Recording_Information", "document_id": 9002231, "additional_info": {"nth_line": 1}, "output_format": {"schedule_a_output_format": {"custom": true}, "string_operations_output_format": {"remove_from_beginning": ["mortgages or trust deeds shown on Schedule B herein"]}}, "post_process": {"remove_next_line_if_starts_with_point": {"custom": false}}, "sub_keys": ["loan_information", "book_information"]}, {"id": 80012148, "key": ["The land referred to in this policy is described as follows:", "The Land referred to herein", "the land is described as follows:", "The land referred to in this policy"], "direction": "down_block", "return_type": "text", "type": "text", "multi_line_value": true, "multi_page_value": true, "end_identifier": ["This policy incorporates", "Copyright", "Countersigned", "exceptions"], "start_identifier": [""], "field_id": 80012148, "field_name": "Legal_Description_Text", "exclude_footer": true, "document_id": 9002231, "additional_info": {"nth_line": 1}, "output_format": {"one_of": false, "copy_extraction_items_output_format": {"swap_with": "Legal_Description_Text_Exhibit", "found_str": ["exhibit \"a\"", "exhibit a", "schedule c", "schedule a", "SEE DESCRIPTION SHEET", "Attached Legal Description"]}, "string_operations_output_format": {"remove_from_beginning": ["LEGAL DESCRIPTION", "(Legal Description)"], "remove_special_chars_from_beginning": true, "remove_from_end": ["(End of Legal Description)", "End of Legal Description"]}}, "sub_keys": ["legal_subdivision_information", "tax_apn_information"]}, {"id": 80014060, "key": ["endorsements are incorporated"], "direction": "down_block", "return_type": "table", "type": "text", "multi_line_value": true, "end_identifier": ["This policy incorporates", "The endorsements checked below", "Countersigned", "exceptions"], "start_identifier": [""], "field_id": 80014060, "field_name": "Endorsement_2", "document_id": 9002231, "post_process": {"split_by_regex_post_processor": {"custom": false}}}, {"id": 80014060, "key": ["endorsements are incorporated", "endorsements indicated below", "endorsements checked below", "endorsements selected below", "endorsements marked below"], "direction": "down_multi_page", "return_type": "table", "type": "checkbox", "multi_line_value": true, "multi_page_value": true, "max_page_limit": 1, "use_match": "fuzzy", "end_identifier": ["copyright", "schedule", "Countersigned by"], "start_identifier": [""], "field_id": 80014060, "field_name": "Endorsement_Type", "document_id": 9002231, "additional_info": {"search_key": ["ENDORSEMENT"], "search_after": [""], "consider_complete_line": true}, "output_format": {"concat_extraction_items_output_format": {"from_field": "Endorsement_2", "get_field1_and_field2": true}}}, {"id": 80012147, "key": ["Exhibit a legal description", "ADDENDUM", "ADDENDUM/Exhibit A", "EXHIBIT \"A\"", "Exhibit A", "SCHEDULE C", "SCHEDULE \"C\"", "Appendix A", "LEGAL DESCRIPTION", "SCHEDULE A - DESCRIPTION", "DESCRIPTION", "SCHEDULE A", "SCHEDULE \"A\""], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "multi_page_value": true, "exclude_footer": true, "probable_type": "Header", "page_range_before": -1, "max_cblock_iter": 4, "end_identifier": ["copyright", "For Company Reference Purposes", ""], "use_mst": true, "use_mst_for_start_identifier": true, "use_sentence_transformer": true, "start_identifier": ["LEGAL DESCRIPTION", "Policy Number", "policy no", "file number", "file no"], "field_id": 80012147, "field_name": "Legal_Description_Text_Exhibit", "start_identifier_down": true, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["(Legal Description)", "LEGAL DESCRIPTION", "Exhibit A - Legal Description"], "remove_special_chars_from_beginning": true, "remove_from_end": ["(End of Legal Description)", "End of Legal Description"]}}, "document_id": 9002231, "additional_info": {"nth_line": 1}, "sub_keys": ["legal_subdivision_information", "tax_apn_information"]}, {"id": 80012147, "key": ["loan policy schedule b", "schedule b"], "direction": "down_multi_page", "type": "text", "return_type": "text", "multi_line_value": true, "multi_page_value": true, "include_key": true, "exclude_footer": true, "probable_place": "Individual", "probable_type": "Header", "merge_with_sub_item": 1, "use_bullet_point_match": true, "save_page_no_for_next_key": true, "page_range_before": "to_start", "reprocess_if_not_found": true, "search_key_in_multi_page": true, "max_cblock_iter": 4, "end_identifier": ["copyright", "end of exceptions", "end of schedule", "countersignature", "issuing agent", "California Land Title Association. All rights reserved."], "start_identifier": ["SCHEDULE B"], "field_id": 80012147, "field_name": "Exception_Header", "document_id": 9002231, "additional_info": {"search_key": ["SCHEDULE B"]}, "output_format": {"exception_output_format": {"custom": true}}, "sub_items": [{"id": 80012840, "key": ["reason of", "insured mortgage", "resulting from", "resultingfrom"], "type": "text", "use_match": "fuzzy", "return_type": "table", "start_identifier": ["insured mortgage", "resulting from", "policy number", "policy no", "file number", "exceptions", "1.", "stewart title guaranty company"], "field_id": 80012840, "field_name": "Exception_Text", "document_id": 9002231, "additional_info": {"split_by_bullets": true}, "sub_keys": ["tax_apn_information"], "sub_items": [{"id": 80012841, "type": "text", "return_type": "table", "key": [], "start_identifier": [], "field_id": 80012841, "field_name": "Exception_Type", "document_id": 9002231, "sub_item_processor": {"exception_type_processor": {"custom": true}}}]}]}, {"key": ["Countersigned by", "Countersigned", "Authorized Signature", "Authorized Countersignature", "Authorized Signatory"], "direction": "down_block", "type": "text", "use_match": "fuzzy", "multi_line_value": true, "start_identifier": [""], "end_identifier": ["Copyright", "Policy number", "Policy serial", "page", "part 1 of", "Serial No", "If you want information", "Exclusions", "Exclusions from coverage", "File No", "File Number"], "page_range_before": "to_start", "id": 80012124, "field_id": 80012124, "field_name": "Title_Company", "document_id": 9002231, "output_format": {"ner_service_req_output_format": {"text_field_name": "Title_Company", "label": ["ORGANIZATION"]}}}, {"id": 28, "key": ["For Company Reference Purposes Only"], "direction": "down_block", "type": "text", "return_type": "text", "multi_line_value": true, "multi_page_value": false, "max_cblock_iter": 4, "page_range_before": "to_start", "end_identifier": ["copyright", ""], "start_identifier": [""], "field_id": 60003212, "field_name": "Legal_Description_Text_Footer", "document_id": 12345678, "sub_keys": ["tax_apn_information"]}]