[{"id": 80016765, "key": ["Loan Identifier"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["/", "Agency Case"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80016765, "field_name": "Loan Number", "document_id": 9000001}, {"id": 8001676, "key": ["Loan Identifier"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Agency Case"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 8001676, "field_name": "Universal Loan Identifier", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_beginning": ["^\\d+\\s?/\\s?"], "retain_only_numbers": false, "remove_special_chars_from_beginning": true}}}, {"id": 80022755, "key": ["Agency Case No."], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Uniform Residential Loan Application"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022755, "field_name": "Agency Case Number", "document_id": 9000001}, {"id": 101, "key": ["borrower information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 101, "field_name": "dummy_section1_1", "document_id": 9000001}, {"id": 5000001, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 5000001, "field_name": "Borrower_name", "document_id": 9000001}, {"id": 80022782, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022782, "field_name": "B1 First Name", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"name_parser_output_format": {"get_first_name": true, "from_field": "Borrower_name"}}}, {"id": 80022796, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022796, "field_name": "B1 Middle Name", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"name_parser_output_format": {"get_middle_name": true, "from_field": "Borrower_name"}}}, {"id": 80022789, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022789, "field_name": "B1 Last Name", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"name_parser_output_format": {"get_last_name": true, "from_field": "Borrower_name"}}}, {"id": 80022806, "key": ["Social Security Number"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Date of Birth"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022806, "field_name": "B1 SSN", "document_id": 9000001}, {"id": 80022775, "key": ["Date of Birth"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Non-Permanent Resident Alien", "type of credit"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022775, "field_name": "Borrower Date of Birth", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 80000909, "key": ["Total Number of Borrowers"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Each Borrower intends"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000909, "field_name": "Total_Number_of_Borrowers_DFCU_325988", "document_id": 9000001, "output_format": {"string_operations_output_format": {"remove_from_end": ["\\D*"], "remove_special_chars_from_end": true, "retain_only_numbers": true}}}, {"id": 80022787, "key": ["Home Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Separated"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 70001466, "field_name": "B1 Home Phone", "document_id": 9000001}, {"id": 80022769, "key": ["Cell Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unmarried"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022769, "field_name": "B1 Cell Phone", "document_id": 9000001}, {"id": 80000912, "key": ["Work Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Ext."], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000912, "field_name": "Borrower1_Work_Phone_DFCU_326019", "document_id": 9000001}, {"id": 800009, "key": ["Ext."], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Email"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 800009, "field_name": "Borrower1_Ext", "document_id": 9000001}, {"id": 80000821, "key": ["Email"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000821, "field_name": "Borrower1EMailAddressDFCU326038", "document_id": 9000001}, {"id": 80000821, "key": ["Section 1"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000821, "field_name": "Borrower_Section1_omr_fields", "sub_keys": ["Section1_omr_save"], "document_id": 9000001}, {"id": 80022773, "key": ["Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit #"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022773, "field_name": "B1 Current Address Street", "document_id": 9000001}, {"id": 206, "key": ["Unit #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["City"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 206, "field_name": "Borrower_CurrentAddress_Unit", "document_id": 9000001}, {"id": 80022771, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022771, "field_name": "B1 Current Address City", "document_id": 9000001}, {"id": 80022772, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022772, "field_name": "B1 Current Address State", "document_id": 9000001}, {"id": 80022774, "key": ["ZIP"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022774, "field_name": "B1 Current Address Zip", "document_id": 9000001}, {"id": 205, "key": ["Country"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["How Long"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 205, "field_name": "<PERSON><PERSON><PERSON>_CurrentAddress_Country", "document_id": 9000001}, {"id": 1331, "key": ["LESS than"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Mailing Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 1331, "field_name": "<PERSON><PERSON><PERSON>_Former_address_save", "sub_keys": ["former_address_save"], "document_id": 9000001}, {"id": 14001, "key": ["Mailing Address"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Employer or Business Name"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 14001, "field_name": "Borrower_mailling_address_save", "sub_keys": ["mailling_address_save"], "document_id": 9000001}, {"id": 80022777, "key": ["Employer or Business Name"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Phone"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022777, "field_name": "B1_Employer_or_Business_Name001", "document_id": 9000001}, {"id": 14021, "key": ["Employer or Business"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 14021, "field_name": "Employer_or_Business_save", "sub_keys": ["Employer_or_Business"], "document_id": 9000001}, {"id": 14022, "key": ["Gross Monthly Income"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 140, "field_name": "Gross_monthly_total", "sub_keys": ["Gross_monthly_income_save"], "document_id": 9000001}, {"id": 10002, "key": ["Loan and Property Information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 10002, "field_name": "dummy_section4_4", "document_id": 9000001}, {"id": 1003, "key": ["Section 4"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Creditor Name"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 1003, "field_name": "Loan_Purpose_checkbox_save_section", "sub_keys": ["Section4_loan_property_information"], "document_id": 9000001}, {"id": 107, "key": ["Declarations"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 107, "field_name": "dummy_section5_5", "document_id": 9000001}, {"id": 1001, "key": ["Declarations"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name"], "start_identifier": [""], "possible_page_numbers": [4], "field_id": 1001, "field_name": "Borrower_Declarations_save_section", "sub_keys": ["Section5_Declaration_omr"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 106, "key": ["section 6"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 106, "field_name": "dummy_section6_6", "document_id": 9000001}, {"id": 80022764, "key": ["Borrower Signature"], "direction": "down", "type": "date", "return_type": "date", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 80022764, "field_name": "Application Date", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 104, "key": ["section 8", "Demographic Information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 104, "field_name": "dummy_section8_3", "document_id": 9000001}, {"id": 105, "key": ["please check below", "please chock", "the law also provides"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name"], "start_identifier": [""], "possible_page_numbers": [], "field_id": 105, "field_name": "demographic_checkbox_save_section", "sub_keys": ["demographic_information_omr"], "document_id": 9000001}, {"id": 105, "key": ["Financial Institution"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 105, "field_name": "demographic_checkbox_save_section01", "additional_info": {"search_dummy_found_page_only": true}, "sub_keys": ["Borrower_Demographic_information2"], "document_id": 9000001}, {"id": 920, "key": ["section 9"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name"], "start_identifier": [""], "possible_page_numbers": [7], "field_id": 920, "field_name": "Loan_Originator_Information_save", "sub_keys": ["Loan_originator_information"], "document_id": 9000001}, {"id": 115, "key": ["Original Cost"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Title to the Property Will"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 115, "field_name": "property_save_section", "sub_keys": ["project_type_omr"], "document_id": 9000001}, {"id": 80017337, "key": ["Mortgage Type"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Loan Features"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 80017337, "field_name": "Amortization_Type_save_section", "sub_keys": ["Amortization_info_omr"], "document_id": 9000001}, {"id": 123456, "key": ["Project Type"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Mortgage Type"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 123456, "field_name": "L2_Title_information_save", "sub_keys": ["L2_title_information"], "document_id": 9000001}, {"id": 80011703, "key": ["Tenancy in Common"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Amortization Type"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 80011703, "field_name": "Loan_Type_DFCU_save_section", "sub_keys": ["Mortgage_Loan_info_omr"], "document_id": 9000001}, {"id": 817337, "key": ["community property state"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["energy improvement"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 817337, "field_name": "Loan_type_refinance", "sub_keys": ["Loan_Refinance_Type"], "document_id": 9000001}, {"id": 80017334, "key": ["Note Rate"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["<PERSON>an <PERSON>"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 80017334, "field_name": "Rate", "document_id": 9000001, "output_format": {"string_operations_output_format": {"contains": ["(\\d+\\.?\\d*)\\s*%"], "remove_from_end": ["\\s*%"]}}}, {"id": 80017334, "key": ["<PERSON>an <PERSON>"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["(months)"], "start_identifier": [""], "possible_page_numbers": [9], "field_id": 80017334, "field_name": "Term", "document_id": 9000001}, {"id": 1111111, "key": ["Proposed Monthly"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name(s)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 1111111, "field_name": "L3proposed_monthly_payment_for_property_save", "sub_keys": ["L3_proposed_monthly_payment_for_property"], "document_id": 9000001}, {"id": 1234567, "key": ["L4. Qualifying the Borrower"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name(s)"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 1234567, "field_name": "L4_qualifying_borrower_minimum_required_funds_cash_back_save", "sub_keys": ["L4_Qualifying_borrower_minimum_required_funds"], "document_id": 9000001}, {"id": 107, "key": ["borrower information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 107, "field_name": "dummy_section1_11", "document_id": 9000001}, {"id": 5000011, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 5000011, "field_name": "Co-Borrower_name", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80022833, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022833, "field_name": "Co-Borrower_First", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"name_parser_output_format": {"get_first_name": true, "from_field": "Co-Borrower_name"}}}, {"id": 70000946, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 70000946, "field_name": "Co-<PERSON><PERSON><PERSON>_<PERSON>", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"name_parser_output_format": {"get_middle_name": true, "from_field": "Co-Borrower_name"}}}, {"id": 80022842, "key": ["Name ("], "direction": "just_down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Alternate Names"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022842, "field_name": "Co-Bo<PERSON><PERSON>_Last", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"name_parser_output_format": {"get_last_name": true, "from_field": "Co-Borrower_name"}}}, {"id": 80003214, "key": ["Date of Birth"], "direction": "right", "type": "date", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Non-Permanent Resident Alien", "type of credit"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80003214, "field_name": "Co-Borrower Date of Birth", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"date_finder_output_format": {"return_format": "%m/%d/%Y"}}}, {"id": 80022855, "key": ["Social Security Number"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Date of Birth"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022855, "field_name": "<PERSON><PERSON><PERSON><PERSON>er_SSN_DFCU_326176", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 70000422, "key": ["Home Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Separated"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 70000422, "field_name": "Co-<PERSON><PERSON><PERSON>_Home_Phone", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80022859, "key": ["Cell Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unmarried"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022859, "field_name": "Co-Borrower_Cell_Phone_DFCU_326040", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000912, "key": ["Work Phone"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Ext."], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000912, "field_name": "Co-<PERSON><PERSON><PERSON>_Work_Phone_DFCU_326019", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000009, "key": ["Ext."], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Email"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000009, "field_name": "co-Borrower_Ext", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80022827, "key": ["Email"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022827, "field_name": "Co-Borrower_EMailAddressDFCU326038", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80000909, "key": ["Total Number of Borrowers"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Each Borrower intends"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80000909, "field_name": "Total_Number_of_co-Borrowers_DFCU_325988", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}, "output_format": {"string_operations_output_format": {"remove_from_end": ["\\D*"], "remove_special_chars_from_end": true, "retain_only_numbers": true}}}, {"id": 800008211, "key": ["Section 1"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Current Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 800008211, "field_name": "co-Bo<PERSON><PERSON>_Section1_omr_fields", "sub_keys": ["co_borrower_page1_omr_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80022825, "key": ["Street"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Unit #"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022825, "field_name": "co-<PERSON><PERSON><PERSON>_CurrentAddress_street", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 2066, "key": ["Unit #"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["City"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 2066, "field_name": "co-<PERSON><PERSON><PERSON>_CurrentAddress_Unit", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80022823, "key": ["City"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["State"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022823, "field_name": "co-<PERSON><PERSON><PERSON>_CurrentAddress_city", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80022824, "key": ["State"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["ZIP"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022824, "field_name": "co-<PERSON><PERSON><PERSON>_CurrentAddress_State", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 80022826, "key": ["ZIP"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Country"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 80022826, "field_name": "co-<PERSON><PERSON><PERSON>_CurrentAddress_ZIP", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 2055, "key": ["Country"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["How Long"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 2055, "field_name": "co-<PERSON><PERSON><PERSON>_CurrentAddress_Country", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 13311, "key": ["LESS than"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Mailing Address"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 13311, "field_name": "co_<PERSON><PERSON><PERSON>_Former_address", "sub_keys": ["co_borrower_former_address_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 140011, "key": ["Mailing Address"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Employer or Business Name"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 140011, "field_name": "co_<PERSON>rrower_mailling_address", "sub_keys": ["co_borrower_mailling_address_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 6011, "key": ["Employer or Business Name"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": ["Phone"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 6011, "field_name": "Co_borrower_Employer_or_Business_Name001", "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 140211, "key": ["Employer or Business"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name"], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 140211, "field_name": "Co_borrower_Employer_or_Business_save", "sub_keys": ["Co_borrower_Employer_or_Business"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 140222, "key": ["Gross Monthly Income"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [1], "field_id": 140222, "field_name": "Co-borrower_Gross_monthly_total", "sub_keys": ["Co_borrower_Gross_monthly_income_save"], "document_id": 9000001, "additional_info": {"search_dummy_found_page_only": true}}, {"id": 108, "key": ["section 8", "Demographic Information"], "direction": "right", "type": "text", "return_type": "text", "multi_line_value": false, "use_match": "fuzzy", "end_identifier": [""], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 108, "field_name": "dummy_section8_6", "document_id": 9000001}, {"id": 109, "key": ["please check below", "please chock", "select the"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 109, "field_name": "demographic_checkbox_save_section", "additional_info": {"search_dummy_found_page_only": true}, "sub_keys": ["co_borrower_demographic_information_omr"], "document_id": 9000001}, {"id": 10559, "key": ["Financial Institution"], "direction": "down", "type": "text", "return_type": "text", "multi_line_value": true, "use_match": "fuzzy", "end_identifier": ["Borrower Name"], "start_identifier": [""], "possible_page_numbers": [8], "field_id": 10559, "field_name": "demographic_checkbox_save_section02", "additional_info": {"search_dummy_found_page_only": true}, "sub_keys": ["Co_Borrower_Demographic_information2"], "document_id": 9000001}]