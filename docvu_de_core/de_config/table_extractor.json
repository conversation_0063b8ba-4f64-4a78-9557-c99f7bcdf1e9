[{"id": 101, "key": ["Recommendation"], "type": "table_new", "end_identifier": ["Mortgage Information"], "keep_start_key": true, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "possible_page_numbers": [1], "field_id": 103, "field_name": "loan_amount", "document_id": 9000001, "table_processor": {"extract_data_from_table_with_horizontal_lines": {"field_string": "Primary Borrower", "field_string_col": 0, "probable_field_value_col": 0, "probable_place_in_table": "below_row", "probable_field_value_next_row": 0}}}, {"id": 101, "key": ["Mortgage Information"], "type": "table_new", "end_identifier": ["Property Information"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "possible_page_numbers": [1], "field_id": 103, "field_name": "loan_amount", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "<PERSON>an <PERSON>", "field_string_col": 2, "probable_field_value_col": 2, "probable_place_in_table": "below_row", "probable_field_value_next_row": 0}}}, {"id": 101, "key": "following assets", "type": "table_new", "end_identifier": ["Information about"], "field_id": 101, "field_name": "b_name", "document_id": 9000181, "table_processor": {"extract_data_from_table": {"table_sequence": 2, "field_string": "Institution Name", "field_string_col": 2, "probable_field_value_col": 3, "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}}, {"id": 101, "key": ["increase"], "type": "table_new", "end_identifier": ["Payment Calculation"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "possible_page_numbers": [1], "field_id": 103, "field_name": "loan_amount", "document_id": 9000001, "table_processor": {"extract_data_from_table_with_horizontal_lines": {"field_string": "<PERSON><PERSON>", "field_string_col": 0, "probable_field_value_col": 1, "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}}, {"id": 101, "key": ["Results"], "type": "table_new", "end_identifier": ["Credit Report Information"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "possible_page_numbers": [2], "field_id": 103, "field_name": "appraisal_type", "document_id": 9000001, "table_processor": {"extract_data_from_table_without_lines": {"field_string": "APPRAISAL TYPE", "field_string_col": 1, "probable_field_value_col": 1, "probable_place_in_table": "below_row", "probable_field_value_next_row": 0}}}, {"id": 101, "key": ["Agency Case Number"], "type": "table_new", "end_identifier": ["Subject Property Address"], "keep_start_key": false, "hpos_end_identifier": false, "hpos_end_key": [""], "consider_on_after_hpos": false, "possible_page_numbers": [1], "field_id": 103, "field_name": "interest_rate", "document_id": 9000001, "table_processor": {"extract_data_from_table_with_horizontal_lines": {"field_string": "Interest Rate", "field_string_col": 1, "probable_field_value_col": 1, "probable_place_in_table": "below_row", "probable_field_value_next_row": 0}}}, {"id": 101, "key": "credit and liabilities", "type": "table_new", "possible_page_numbers": [3], "field_id": 103, "field_name": "<PERSON><PERSON><PERSON>", "document_id": 9000089, "table_processor": {"extract_data_from_table": {"page_no": 4, "table_no": 1, "field_string": "Employment Income", "field_string_col": 1, "probable_field_value_col": 3, "probable_place_in_table": "same_row", "probable_field_value_next_row": 0}}}]