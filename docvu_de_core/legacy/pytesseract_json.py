from __future__ import print_function
# import __builtin__
import xml.etree.ElementTree as ET
import json

# debug  = True
# def print(*args, **kwargs):
#     if(debug):
#         return print(*args, **kwargs)

class PyTesseractXML2JSON:
    def __init__(self):
        self.debug = False
        pass

    @staticmethod
    def is_near_vertically(block1, block2, threshold=20):
        vpos_block1 = int(block1.get('VPOS'))
        vpos_block2 = int(block2.get('VPOS'))
        hpos_block1 = int(block1.get('HPOS'))
        hpos_block2 = int(block2.get('HPOS'))
        height_block1 = int(block1.get('HEIGHT'))
        width_block1 = int(block1.get('WIDTH'))
        
        # Check vertical distance using a threshold
        ret_value = (abs(vpos_block1 - vpos_block2) <= threshold or abs(vpos_block1 + height_block1 - vpos_block2) <= threshold) and abs(hpos_block1 + width_block1 - hpos_block2) > threshold

        return ret_value
    
    @staticmethod
    def is_blocks_nearby(block1, block2, threshold=40):
        vpos_block1 = int(block1.get('VPOS'))
        vpos_block2 = int(block2.get('VPOS'))
        hpos_block1 = int(block1.get('HPOS'))
        hpos_block2 = int(block2.get('HPOS'))
        height_block1 = int(block1.get('HEIGHT'))
        height_block2 = int(block2.get('HEIGHT'))
        width_block1 = int(block1.get('WIDTH'))
        width_block2 = int(block2.get('WIDTH'))

        # two blocks are side by side
        if abs(hpos_block1 + width_block1 - hpos_block2) <= threshold and abs(vpos_block1 - vpos_block2) <= threshold:
            return True

        # Two blocks are one below other
        # if abs(vpos_block1 + height_block1 - vpos_block2) <= threshold and abs(hpos_block1 - hpos_block2) <= threshold:
        #     return True
        if abs(vpos_block1 + height_block1 - vpos_block2) <= threshold:
            return True
        
        # Two blocks are one over other horizontally
        if (hpos_block1 <= hpos_block2 <= (hpos_block1 + width_block1)) and abs(vpos_block1 - vpos_block2) <= threshold:
            return True
        
        if (hpos_block2 <= hpos_block1 <= (hpos_block2 + width_block2)) and abs(vpos_block1 - vpos_block2) <= threshold:
            return True
        
        # Two blocks are one over other vertically
        if (vpos_block1 <= vpos_block2 <= (vpos_block1 + height_block1)) and abs(hpos_block1 - hpos_block2) <= threshold:
            return True
        
        if (vpos_block2 <= vpos_block1 <= (vpos_block2 + height_block2)) and abs(hpos_block1 - hpos_block2) <= threshold:
            return True
        
        # When one box is inside another box
        if (vpos_block1 <= vpos_block2 <= (vpos_block1 + height_block1)) and (hpos_block1 <= hpos_block2 <= (hpos_block1 + width_block1)):
            return True
        
        if (vpos_block2 <= vpos_block1 <= (vpos_block2 + height_block2)) and (hpos_block2 <= hpos_block1 <= (hpos_block2 + width_block2)):
            return True
        
        return False


    def get_neighbouring_composed_blocks(self, composed_blocks):
        near_by_blocks = []
        empty_blocks = set()
        # Find composed block near to each other vertically where none of block has empty contents :
        for i, block1 in enumerate(composed_blocks):

            content_block1 = ' '.join(string.get('CONTENT', '') for string in block1.findall(".//{http://www.loc.gov/standards/alto/ns-v3#}String"))
            block1_num = int(block1.get('ID').split("_")[-1])
            if content_block1.strip() == "":
                empty_blocks.add(block1_num)
                continue

            for j, block2 in enumerate(composed_blocks[i+1:]) :
                content_block2 = ' '.join(string.get('CONTENT', '') for string in block2.findall(".//{http://www.loc.gov/standards/alto/ns-v3#}String"))
                block2_num = int(block2.get('ID').split("_")[-1])
                if content_block2.strip() == "":
                    empty_blocks.add(block2_num)
                    continue

                if self.is_blocks_nearby(block1, block2):
                    if self.debug:
                        print(f"ComposedBlock {block1.get('ID')} is near ComposedBlock {block2.get('ID')} vertically.")
                    near_by_blocks.append((int(block1.get('ID').split("_")[-1]), int(block2.get('ID').split("_")[-1])))
        if self.debug:
            print(f"{len(composed_blocks)} composed blocks are present in xml")
        empty_blocks = list(sorted(empty_blocks))
        return near_by_blocks, empty_blocks


    def combine_near_by_blocks(self, near_by_blocks, empty_blocks, composed_blocks):
        grouped_near_by_blocks = []

        for item in near_by_blocks:
            added = False
            for i, g_item in enumerate(grouped_near_by_blocks):
                if item[0] in g_item and item[1] not in g_item:
                    grouped_near_by_blocks[i].append(item[1])
                    added =True
                    break
                if item[1] in g_item and item[0] not in g_item:
                    grouped_near_by_blocks[i].append(item[0])
                    added = True
                    break
                if item[0] in g_item and item[1] in g_item:
                    added = True
                    break

            if not added:
                grouped_near_by_blocks.append(list(item))

        if self.debug:
            print(f"{len(empty_blocks)} empty composed blocks")
            print(f"empty_blocks: {empty_blocks}")
        used_composed_blocks = []
        [used_composed_blocks.extend(itm) for itm in grouped_near_by_blocks]
        if self.debug:
            print("used_composed_blocks:",used_composed_blocks)
        used_composed_blocks.extend(empty_blocks)
        all_composed_blocks = list(range(len(composed_blocks)))

        remaining_composed_blocks = sorted(list(set(all_composed_blocks) - set(used_composed_blocks)))
        if self.debug:
            print("remaining_composed_blocks:",remaining_composed_blocks)
        [grouped_near_by_blocks.append([blk_num]) for blk_num in remaining_composed_blocks]
        # Sort the groups based on the first value in the list
        grouped_near_by_blocks = sorted(grouped_near_by_blocks, key=lambda x:x[0])
        if self.debug:
            print("grouped_near_by_blocks:",grouped_near_by_blocks)

        return grouped_near_by_blocks

    @staticmethod
    def combine_neighbouring_composed_blocks(grouped_near_by_blocks, composed_blocks):
        list_composed_blocks = []

        for i, group in enumerate(grouped_near_by_blocks):
            composit_block_group = [] #ID="cblock_44" HPOS="191" VPOS="1755" WIDTH="931" HEIGHT="56"
            group_hpos = 10000000
            group_vpos = 10000000
            group_height_end = 0
            group_width = 0
            c_id = f"cblock_{i}"
            for num in sorted(group):
                # print(group, num, len(composed_blocks))
                c_block = composed_blocks[int(num)]
                composit_block_group.extend(c_block.findall(".//{http://www.loc.gov/standards/alto/ns-v3#}TextBlock"))
                if group_hpos > int(c_block.get('HPOS')):
                    group_hpos = int(c_block.get('HPOS'))

                if group_vpos > int(c_block.get('VPOS')):
                    group_vpos = int(c_block.get('VPOS'))

                if group_width < int(c_block.get('WIDTH')):
                    group_width = int(c_block.get('WIDTH'))

                if group_height_end < int(c_block.get('HPOS')) + int(c_block.get('HEIGHT')):
                    group_height_end = int(c_block.get('HPOS')) + int(c_block.get('HEIGHT'))
                
            group_height = abs(group_hpos - group_height_end)
            list_composed_blocks.append({"ID":c_id, "HPOS":group_hpos,"VPOS":group_vpos,
                                        "WIDTH":group_width,"HEIGHT":group_height,"Data":composit_block_group})

        return list_composed_blocks

    @staticmethod
    def convert_to_json(list_composed_blocks):
        space_threshold = 30
        new_list_composed_blocks = []

        for cblock_item in list_composed_blocks.copy():
            json_text_block = []
            for text_block in cblock_item["Data"]:
                json_text_line_block = []
                for text_line_block in text_block:
                    json_string_block = []
                    str_item = {"HPOS":0,"VPOS":0, "WIDTH":0,"HEIGHT":0, "text": "", "SCORE": 0.0}
                    count = 0
                    for string_block in text_line_block:
                        # print(string_block.get('CONTENT'))
                        # print(string_block.tag)
                        # If the space between two strings(words) are more than threshold then consider it to be two different strings
                        if "SP" in string_block.tag and int(string_block.get('WIDTH')) > space_threshold:
                            str_item["SCORE"] = str_item["SCORE"]/count
                            json_string_block.append(str_item)
                            str_item = {"HPOS":0,"VPOS":0, "WIDTH":0,"HEIGHT":0, "text": "", "SCORE": 0.0}
                            count = 0
                        elif "String" in string_block.tag:
                            # For inital we will consider our hpos and vpos same as the first word
                            if str_item["text"] == "":
                                str_item["HPOS"] = int(string_block.get('HPOS'))
                                str_item["VPOS"] = int(string_block.get('VPOS'))
                            str_item["text"] += " " + string_block.get('CONTENT')
                            if str_item["HEIGHT"] < int(string_block.get('HEIGHT')):
                                str_item["HEIGHT"] = int(string_block.get('HEIGHT'))
                            # To calculate the width of string we substract the string hops from (word hops + width)
                            str_item["WIDTH"] = (int(string_block.get('HPOS')) + int(string_block.get('WIDTH'))) - str_item["HPOS"]
                            # Add all the scores and divide by count to get average accuracy
                            str_item["SCORE"] += float(string_block.get("WC"))
                            count += 1
                    str_item["SCORE"] = str_item["SCORE"]/count
                    json_string_block.append(str_item)
                    # print(json_string_block)
                    text_line_item = {"HPOS":text_line_block.get('HPOS'),"VPOS":text_line_block.get('VPOS'),
                                    "WIDTH":text_line_block.get('WIDTH'),"HEIGHT":text_line_block.get('HEIGHT'),
                                        "STRING": json_string_block}
                    json_text_line_block.append(text_line_item)
                    # -----------------------------------------
                text_item = {"HPOS":text_block.get('HPOS'),"VPOS":text_block.get('VPOS'),
                            "WIDTH":text_block.get('WIDTH'),"HEIGHT":text_block.get('HEIGHT'),
                            "TEXT_LINE": json_text_line_block}
                json_text_block.append(text_item)
                # ------------------------------------
            cblock_item["TEXT_BLOCK"] = json_text_block
            del cblock_item["Data"]
            new_list_composed_blocks.append(cblock_item)
        
        return new_list_composed_blocks

    @staticmethod
    def convert_xml_to_word_level_json(xml_path, json_path):
        tree = ET.parse(xml_path)
        root = tree.getroot()
        xml_string_list = root.findall(".//{http://www.loc.gov/standards/alto/ns-v3#}String")

        modified_list = []
        for item in xml_string_list:
            if item.get('CONTENT').strip() == "":
                continue
            else:
                new_item = {
                    "HPOS": int(item.get('HPOS')), "VPOS": int(item.get('VPOS')), "WIDTH": int(item.get('WIDTH')),
                    "HEIGHT": int(item.get('HEIGHT')),"text": item.get('CONTENT'), "SCORE": float(item.get('WC'))
                    }
                modified_list.append(new_item)

        with open(json_path, "w+") as temp_f:
            temp_f.write(json.dumps(modified_list))


    def convert_xml_to_json(self, xml_path, json_path):
        # Load the XML data
        tree = ET.parse(xml_path)
        root = tree.getroot()

        composed_blocks = root.findall(".//{http://www.loc.gov/standards/alto/ns-v3#}ComposedBlock")

        near_by_blocks, empty_blocks = self.get_neighbouring_composed_blocks(composed_blocks.copy())
        grouped_blocks = self.combine_near_by_blocks(near_by_blocks, empty_blocks, composed_blocks.copy())
        list_composed_blocks = self.combine_neighbouring_composed_blocks(grouped_blocks, composed_blocks.copy())
        json_data = self.convert_to_json(list_composed_blocks)
        
        with open(json_path, "w+") as temp_f:
            temp_f.write(json.dumps(json_data))


if __name__ == "__main__":
    xml2json_obj = PyTesseractXML2JSON()
    xml_path = './../ocr_xml/STEWARTECM_PROD000068403899/1.xml'
    json_path = '../../temp.json'
    xml2json_obj.convert_xml_to_json(xml_path, json_path)
    print('Done')