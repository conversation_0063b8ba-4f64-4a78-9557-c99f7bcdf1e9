import os
import json
import pytesseract
import pdf2image
import cv2
import numpy as np

#from src.xml_to_json import XML2JSON
from docvu_de_core.config import pdf2ocr_config


class PDF2OCR:
    def __init__(self):
        self.xml_to_json = XML2JSON()
        pass

    @staticmethod
    def enhance_image(img_path):
        img = cv2.imread(img_path)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        img = cv2.GaussianBlur(img, ksize=(1, 1), sigmaX=0)
        kernel = np.ones((1, 1), np.uint8)
        img = cv2.erode(img, kernel)
        img = cv2.dilate(img, kernel)
        cv2.imwrite(img_path, img)
        return

    def get_ocr_from_pdf(self, pdf_path):
        working_dir = os.path.dirname(pdf_path) +"/"+ os.path.basename(pdf_path).replace(".pdf","")
        os.makedirs(working_dir, exist_ok=True)

        pages = pdf2image.convert_from_path(pdf_path, dpi=pdf2ocr_config['dpi'])

        combined_json = {}
        combined_word_json = {}
        for count, page in enumerate(pages):
            image_path = f'{working_dir}/{count}.jpg'
            page.save(image_path, 'JPEG')
            self.enhance_image(image_path)
            xml_path = f'{working_dir}/{count}.xml'
            try:
                xml_data = pytesseract.image_to_alto_xml(image_path)
            except:
                continue

            f = open(xml_path, "wb")
            f.write(xml_data)
            f.close()

            json_path = f'{working_dir}/{count}.json'
            self.xml_to_json.convert_xml_to_json(xml_path, json_path)

            with open(json_path) as fo:
                combined_json[count+1] = json.loads(fo.read())

            json_path = f'{working_dir}/{count}_words.json'
            self.xml_to_json.convert_xml_to_word_level_json(xml_path, json_path)

            with open(json_path) as fo:
                combined_word_json[count+1] = json.loads(fo.read())

        combined_json_path = f'{working_dir}/combined.json'
        with open(combined_json_path, "w+") as fo:
            fo.write(json.dumps(combined_json))

        combined_word_json_path = f'{working_dir}/combined_words.json'
        with open(combined_word_json_path, "w+") as fo:
            fo.write(json.dumps(combined_word_json))


if __name__ == "__main__":
    pdf_path = '/home/<USER>/secondry_drive/clone_namrata/pavan/SOFTWARE-SERVICE-%20document_extraction/trial/1003 (2).pdf'
    PDF2OCR().get_ocr_from_pdf(pdf_path)