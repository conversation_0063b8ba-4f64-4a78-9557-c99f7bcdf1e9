from docvu_de_core.extraction_item import BaseFieldData

class TableRow(dict):
    default_required_fields = ['row_number', 'page_number', 'line_number', 'columns']
    def __init__(self, row_number=None, page_number=None, line_number=None, columns=None):
        """
        Initializes a new TableRow instance that behaves like a dictionary.

        :param row_number: The row number within its respective table.
        :param page_number: The page number on which the row is located.
        :param line_number: The line number of the row on the page.
        :param columns: Optional; a list of BaseFieldData instances representing columns in the row.
        """
        super().__init__(
            row_number=row_number,
            page_number=page_number,
            line_number=line_number,
            columns=columns if columns is not None else []
        )

    def has_attribute(self, attr_name):
        """
        Checks if a given attribute exists in the TableData dictionary.

        :param attr_name: The name of the attribute to check for.
        :return: True if the attribute exists, False otherwise.
        """
        return attr_name in self

    def add_column(self, column_data):
        """
        Adds a column to the row.

        :param column_data: An instance of BaseFieldData representing the new column.
        """
        if not isinstance(column_data, BaseFieldData):
            raise ValueError("column_data must be an instance of BaseFieldData")
        self["columns"].append(column_data)

    def to_dict(self):
        """
        Returns the dictionary representation of the TableRow, which is already in dictionary format.

        :return: A dictionary representation of the TableRow, including row metadata and columns.
        """
        return dict(self)

    def normalize_coordinates(self, page_height, page_width, is_camel_case = False):
        """
        Normalizes the 'name_coordinates' and 'value_coordinates' of each column in the row based on given page dimensions.

        :param page_width: Width of the page to normalize coordinates against.
        :param page_height: Height of the page to normalize coordinates against.
        """
        if is_camel_case:
            col_key = "Columns"
        else:
            col_key = "columns"
        for idx, column in enumerate(self[col_key]):
            self[col_key][idx].normalize_coordinates(page_height, page_width, is_camel_case=is_camel_case)

    def fill_defaults(self):
        """Calls fill_defaults on each BaseFieldData in the columns."""
        self['row_number'] = 0 if not self['row_number'] else self['row_number']
        self['page_number'] = 0 if not self['page_number'] else self['page_number']
        self['line_number'] = 0 if not self['line_number'] else self['line_number']

        if self.get("columns") is None:
            self["columns"] = []
        for idx, c in enumerate(self.get("columns", [])):
            if hasattr(self["columns"][idx], 'fill_defaults'):
                self["columns"][idx].fill_defaults()
            else:
                self["columns"][idx] = BaseFieldData(**self["columns"][idx]).fill_defaults()

    def update_name(self, new_name, col_idx=0):
        if self.get("columns") is None:
            self["columns"] = []
        if len(self["columns"]) > col_idx:
            self["columns"][col_idx]['name'] = new_name

    def to_camel_case(self):
        """Converts all dictionary keys to camelCase in place."""
        new_dict = {}
        for key, value in self.items():
            new_key = self._to_camel_case(key)
            if new_key.lower() == 'columns':
                # Process list of BaseFieldData objects
                col_value = []
                for col in value:
                    col.to_camel_case()
                    col_value.append(col)
                new_dict[new_key] = col_value
            else:
                new_dict[new_key] = value
        self.clear()
        self.update(new_dict)

        return self

    @staticmethod
    def _to_camel_case(s):
        """Helper method to convert snake_case to camelCase"""
        parts = s.split('_')

        if len(parts) == 1:
            if parts[0][0].isupper():
                return  s
        return ''.join(x.capitalize() for x in parts)

    def drop_non_default_fields(self):
        """
        Removes all fields not listed in the default_required_fields from this instance.
        """
        keys_to_remove = [key for key in self if key not in self.default_required_fields]
        for key in keys_to_remove:
            del self[key]

        for idx, row in enumerate(self.get("columns", [])):
            self["columns"][idx].drop_non_default_fields()

    def update_post_processing_value_if_not_found(self):
        if self['columns']:
            for i in range(len(self['columns'])):
                self['columns'][i].update_post_processing_value_if_not_found()

    def __str__(self):
        """
        Returns a pretty-printed string representation of the TableRow instance.

        :return: A string representation of the TableRow.
        """
        return ""

    def update_field_ids(self, field_id_mapping, is_camel_case = False):
        """
        Updates the field_id of each column based on a mapping of field_name to field_id.

        :param field_id_mapping: A dictionary mapping field names to their corresponding IDs.
        """
        if is_camel_case:
            col_key = 'Columns'
            id_key = 'Id'
            name_key = 'Name'
        else:
            col_key = 'columns'
            id_key = 'id'
            name_key = 'name'
        for idx, column in enumerate(self.get(col_key, [])):
            if column[name_key] in field_id_mapping:
                self[col_key][idx][id_key] = str(field_id_mapping[column[name_key]])

# Example usage
if __name__ == '__main__':
    columns = [
        BaseFieldData(id="1", name="Column1", key="Column 1 Key", value="Value 1", post_processing_value="Processed Value 1",
                      page_number=1, line_number=10, confidence_indicator=95, color_indicator=1,
                      name_coordinates={"x": 0, "y": 0, "width": 0, "height": 0},
                      value_coordinates={"x": 0, "y": 0, "width": 0, "height": 0})
    ]
    row = TableRow(row_number=1, page_number=1, line_number=10, columns=columns)
    row.add_column(BaseFieldData(id="2", name="Column2", key="Column 2 Key", value="Value 2",
                                 page_number=1, line_number=10, confidence_indicator=90, color_indicator=2,
                                 name_coordinates={"x": 1, "y": 1, "width": 1, "height": 1},
                                 value_coordinates={"x": 1, "y": 1, "width": 1, "height": 1}))
    print(row)  # Display the TableRow with its columns
