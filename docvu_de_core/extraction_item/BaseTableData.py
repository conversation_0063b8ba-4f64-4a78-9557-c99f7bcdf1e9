from docvu_de_core.extraction_item import TableRow
from docvu_de_core.extraction_item import BaseFieldData


class BaseTableData(dict):
    # Define required fields for the table
    default_required_fields = [
        "confidence_indicator", "color_indicator", "start_page", "end_page", "rows", "name",
        "is_table", "value"
    ]

    defaults = {
        "confidence_indicator": 0.99,
        "color_indicator": 1,
        "start_page": 0,
        "end_page": 0,
        "rows": [],
        "name": "",
        "value": ""
    }

    default_types = {
        "confidence_indicator": float,
        "color_indicator": int,
        "start_page": int,
        "end_page": int,
        "rows": list,
        "name": str,
        "value": str
    }

    def __init__(self, **kwargs):
        """
        Initializes a new instance of the BaseTableData class, allowing for dynamic fields and initially
        incomplete data structure while ensuring certain fields are initialized.

        :param kwargs: Keyword arguments containing initial metadata and rows for the table.
        """
        super().__init__()

        if 'name' in kwargs and "field_name" not in kwargs:
            kwargs["field_name"] = kwargs["name"]
        elif 'field_name' in kwargs and "name" not in kwargs:
            kwargs["name"] = kwargs["field_name"]

        # Initialize required fields, setting defaults if not provided
        for field in self.default_required_fields:
            self[field] = kwargs.get(field)

        # Validate rows to ensure they are either instances of TableRow or None
        if self['rows'] is not None and not all(isinstance(row, TableRow) for row in self['rows']):
            raise ValueError("All rows must be instances of TableRow or None initially")

        # Include any additional dynamic fields provided
        self.update(kwargs)

        self.is_table = True
        self['is_table'] = True

        self.sub_keys = []
        if 'sub_keys' in self.keys():
            from docvu_de_core.utils import GeneralUtils
            for sub_item in self["sub_keys"]:
                self.sub_keys.append(GeneralUtils.get_base_format(sub_item))
            self["sub_keys"] = self.sub_keys

    def add_row(self, row):
        """
        Adds a TableRow instance to the table's rows list.

        :param row: A TableRow instance to be added to the table's rows list.
        """
        if not isinstance(row, TableRow):
            raise ValueError("The row must be an instance of TableRow")
        if self['rows'] is None:
            self['rows'] = []
        self['rows'].append(row)

    def insert_row(self, index, row):
        """
        Inserts a TableRow instance at a specific index in the table's rows list and updates row numbers accordingly.

        :param index: The index at which the row should be inserted.
        :param row: A TableRow instance to be inserted into the table's rows list.
        """
        if not isinstance(row, TableRow):
            raise ValueError("The row must be an instance of TableRow")
        if self['rows'] is None:
            self['rows'] = []

        self['rows'].insert(index, row)

        # Update the row_number for all rows to ensure correctness
        for i in range(index, len(self['rows'])):
            self['rows'][i]['row_number'] = i

    def delete_row(self, index):
        if index < len(self['rows']):
            # delete the row if present
            self['rows'].pop(index)

            # Update the row_number for all rows to ensure correctness
            for i in range(index, len(self['rows'])):
                self['rows'][i]['row_number'] -= 1

    def is_complete(self):
        """
        Checks if all required fields are populated with non-None values.

        :return: True if all required fields are filled, False otherwise.
        """
        return all(self.get(field) is not None for field in self.default_required_fields)

    def has_attribute(self, attr_name):
        """
        Checks if a given attribute exists in the TableData dictionary.

        :param attr_name: The name of the attribute to check for.
        :return: True if the attribute exists, False otherwise.
        """
        return attr_name in self

    def to_dict(self):
        """
        Converts the TableData instance into a dictionary format suitable for serialization.
        This function is optional since the object is already a dictionary.

        :return: A dictionary representation of the TableData.
        """
        if 'rows' in self and self['rows'] is not None:
            return {**self, 'Rows': [row.to_dict() for row in self['rows']]}
        else:
            return dict(self)

    def drop_non_default_fields(self):
        """
        Removes all fields not listed in the default_required_fields from this instance.
        """
        keys_to_remove = [key for key in self if key not in self.default_required_fields]
        for key in keys_to_remove:
            del self[key]

        for idx, row in enumerate(self.get("rows", [])):
            self["rows"][idx].drop_non_default_fields()

    def n_rows(self):
        return len(self['rows']) if self['rows'] else 0

    def update_field_ids(self, field_id_mapping, is_camel_case = False):
        """Calls update_field_ids on each row with the provided mapping."""

        if is_camel_case:
            row_key = 'Rows'
        else:
            row_key = 'rows'
        for idx, row in enumerate(self.get(row_key, [])):
            self[row_key][idx].update_field_ids(field_id_mapping, is_camel_case = is_camel_case)

    def merge_field_data_by_page(self, field_data_list):
        """
        Converts dictionary field data to BaseFieldData and adds it as a new column to all rows matching
        the page number of the BaseFieldData, only if there is no existing column with the same field_name.

        :param field_data_list: A list of dictionary objects representing BaseFieldData to be merged based on page numbers.
        """
        for idx, field_dict in enumerate(field_data_list):
            # Convert dictionary to BaseFieldData object
            field_data = BaseFieldData(**field_dict)
            field_data.drop_non_default_fields()  # Ensure only required fields are present
            page_number = field_data['page_number']
            field_name = field_data['name']
            line_number = field_data['line_number']
            matched = False  # To track if any row was updated

            if not field_data['value'] and idx > 0:
                prev_field_dict = field_data_list[idx-1]
                field_dict['value'] = prev_field_dict['value']

            if self['rows'] is None:
                columns = [
                        BaseFieldData(id=self['field_id'],
                                      field_name=self['field_name'], key='',
                                      value='',
                                      post_processing_value='',
                                      page_number=page_number,
                                      line_number=0, confidence_indicator=95,
                                      color_indicator=1,
                                      field_name_coordinates={"x": 0, "y": 0, "width": 0, "height": 0},
                                      field_value_coordinates={"x": 0, "y": 0, "width": 0, "height": 0})
                    ]
                row = TableRow(row_number=0, page_number=page_number, line_number=0,
                               columns=columns)
                self.add_row(row)
            for row in self['rows']:
                if row['page_number'] == page_number:
                    # Check if this row already has a column with the same field_name
                    if any(column['name'] == field_name for column in row['columns']):
                        continue  # Skip adding this column if it already exists

                    row.add_column(field_data)
                    matched = True

            # If no row exists for this page or no column matched, create a new one and add the field data
            if not matched:
                new_row = TableRow(row_number=max(0, len(self['rows'])),
                                   page_number=page_number,
                                   line_number = line_number,
                                   columns=[field_data])
                self.add_row(new_row)

    def fill_defaults(self):
        """Calls fill_defaults on each TableRow in the rows list."""
        for field in self.default_required_fields:
            if self.get(field) is None:
                if field in self.defaults.keys():
                    self[field] = self.defaults[field]
            else:
                if field in self.default_types.keys():
                    if not isinstance(self[field], self.default_types[field]):
                        if isinstance(self[field], list) and self.default_types[field] == str:
                            self[field] = " ".join(self[field])
                        else:
                            self[field] = self.default_types[field](self[field])

        for row in self.get('rows', []):
            row.fill_defaults()

        # Update the row_number for all rows to ensure correctness
        for i in range(len(self['rows'])):
            self['rows'][i]['row_number'] = i

        self["confidence_indicator"] = self["confidence_indicator"] * 100 if self["confidence_indicator"] < 1. \
            else self["confidence_indicator"]

    def update_name(self, new_name, col_idx=0):
        if self.get("rows") is None:
            self["rows"] = []
        for idx, r in enumerate(self.get("rows", [])):
            if isinstance(r, TableRow):
                r.update_name(new_name, col_idx=col_idx)

    def to_camel_case(self):
        """Converts all dictionary keys to camelCase in place."""
        new_dict = {}
        for key, value in self.items():
            new_key = self._to_camel_case(key)
            if new_key == 'Rows':
                # Process list of BaseFieldData objects
                row_value = []
                for row in value:
                    row.to_camel_case()
                    row_value.append(row)
                new_dict[new_key] = row_value
            else:
                new_dict[new_key] = value
        self.clear()
        self.update(new_dict)

        return self

    @staticmethod
    def _to_camel_case(s):
        """Helper method to convert snake_case to camelCase"""
        parts = s.split('_')

        if len(parts) == 1:
            if parts[0][0].isupper():
                return  s
        return ''.join(x.capitalize() for x in parts)

    def normalize_coordinates(self, page_dimensions, is_camel_case=False):
        """
        Normalizes the 'name_coordinates' and 'value_coordinates' for each row and each column
        based on the provided page dimensions.

        :param page_dimensions: A dictionary with page numbers as keys and tuples (image_width, image_height) as values.
        """
        if is_camel_case:
            rows = 'Rows'
            page_number = 'PageNumber'
        else:
            rows = 'rows'
            page_number = 'page_number'
        for row in self[rows]:
            # Get the dimensions for the specific page of the current row
            if row[page_number] in page_dimensions:
                try:
                    page_height, page_width, _ = page_dimensions.get(row[page_number], page_dimensions[1])
                except:
                    # page_width, page_height, _ = page_dimensions.get(row[page_number], page_dimensions[1])
                    page_width = page_height = 3000
                row.normalize_coordinates(page_height, page_width)
            else:
                page_width = page_height = 3000
                row.normalize_coordinates(page_height, page_width)
                # raise ValueError(f"Page dimensions for page {row[page_number]} not provided.")

    def get_page_numbers(self):
        """
        Returns a list of unique page numbers from all the TableRow instances in the table.

        :return: A list of unique page numbers.
        """
        if self.get('rows', []):
            page_numbers = {row['page_number'] for row in self.get('rows', []) if row is not None and 'page_number' in row}
            return sorted(list(page_numbers))

    def get_required_fields_dict(self):
        """
        Returns a dictionary containing only the default required fields.

        :return: A dictionary of default required fields.
        """
        return {field: self[field] for field in self.default_required_fields}

    def remove_empty_rows(self):
        """
        Remove rows where the 'columns' list is empty.
        """
        # Filter rows, keeping only those that have non-empty 'columns' lists
        self['rows'] = [row for row in self['rows'] if row['columns']]

        # Update the row_number for all rows to ensure correctness after removal
        for i, row in enumerate(self['rows']):
            row['row_number'] = i

    def update_post_processing_value_if_not_found(self):
        if self['rows']:
            for i in range(len(self['rows'])):
                self['rows'][i].update_post_processing_value_if_not_found()

    def __str__(self):
        """
        Returns a pretty-printed string representation of the TableData instance.

        :return: A string representation of the TableData.
        """
        return ""

# Example usage
if __name__ == '__main__':
    rows = [TableRow(columns=[BaseFieldData(id="1", name="Column1", key="Column 1 Key", value="Value 1",
                                            post_processing_value="Processed Value 1", page_number=1, line_number=1,
                                            confidence_indicator=95, color_indicator=1,
                                            name_coordinates={"x": 0, "y": 0, "width": 0, "height": 0},
                                            value_coordinates={"x": 0, "y": 0, "width": 0, "height": 0})])]
    table_data = BaseTableData(confidence_indicator=85.92362, color_indicator=2, start_page=1, end_page=1, rows=rows)
    print(table_data)  # To view the pretty-printed output
