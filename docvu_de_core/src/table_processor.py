import importlib
import logging
from docvu_de_core.io import TableProcessorOutputFormat

class TableProcessor:
    """
    This class applies post-processing on the extracted content from the parser.
    It dynamically loads and applies table processors based on the configuration provided.
    """

    def __init__(self, logger):
        """
        Initializes the TableProcessor instance.

        Args:
            logger (logging.Logger): Logger instance for logging information and errors.
        """
        self.logger = logger
        self.default_module = 'docvu_de_core.table_processor'  # Default module for table processors
        self.custom_module = 'docvu_de_core.custom'  # Module for custom table processors

    @staticmethod
    def _to_camel_case(s):
        """
        Helper method to convert snake_case to CamelCase.

        Args:
            s (str): String in snake_case format.

        Returns:
            str: String converted to CamelCase format.
        """
        parts = s.split('_')  # Split the string by underscores
        return ''.join(x.capitalize() for x in parts)  # Capitalize each part and join them

    def __call__(self, extraction_item, img_path, json_data):
        """
        Executes the table processing logic.

        Args:
            extraction_item (dict): The extracted data to be processed, containing processor configurations.
            img_path (str): Path to the image being processed.
            json_path (str): Path to the JSON file for additional metadata.

        Returns:
            TableProcessorOutputFormat: The result of the table processing operation.
        """
        # Initialize the output with default values (unsuccessful)
        output = TableProcessorOutputFormat(value=None, success=False, line_number=None, bbox=None)
        
        # Check if 'table_processor' is specified in the extraction item
        if 'table_processor' in extraction_item:
            table_processor_list = extraction_item['table_processor']  # Get the table processor configuration
            
            # Iterate over each processor specified in the configuration
            for table_processor_key, table_processor_val in table_processor_list.items():
                # Convert the processor key from snake_case to CamelCase to match class naming conventions
                class_name = self._to_camel_case(table_processor_key)
                
                try:
                    # Determine which module to import based on the 'custom' flag in the configuration
                    if not table_processor_val.get('custom', False):
                        module = importlib.import_module(self.default_module)  # Import the default module
                    else:
                        module = importlib.import_module(self.custom_module)  # Import the custom module
                    
                    # Dynamically fetch the class from the module
                    table_extractor_class = getattr(module, class_name)
                    
                    # Instantiate the class with logger and additional configuration parameters
                    tb_instance = table_extractor_class(logger=self.logger, **table_processor_val)
                    
                    # Call the table processor instance and update the output
                    output = tb_instance(
                        extraction_item, img_path, json_data
                    )
                    
                except (ImportError, AttributeError) as e:
                    # Log and raise an error if the class cannot be found in the module
                    print(f"Table Processor class {class_name} not found in module. Error: {e}")
                    raise ValueError(f"Table Processor class {class_name} not found in module. Error: {e}")
        
        # Return the final output after processing
        return output
