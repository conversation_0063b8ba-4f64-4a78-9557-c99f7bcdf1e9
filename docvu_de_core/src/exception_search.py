from docvu_de_core.search.direction_based_search.BaseSearchOption import BaseSearchOption
from docvu_de_core.modules import SplitByRegEx, SplitByBullets, CCA
from docvu_de_core.io import OutputFormat
from docvu_de_core.search import <PERSON><PERSON>ey<PERSON><PERSON><PERSON>elper
from docvu_de_core.utils import logger
import re


class ExceptionSearch:
    def __init__(self, debug=False):
        super().__init__()
        self.helper = Search<PERSON>eyFieldHelper(logger)  # Assuming 'logger' is defined elsewhere
        self.debug = debug
        self.is_key_found = False
        self.curr_page_bullet_point = None
        self.prev_page_bullet_point = None

    def _extract_bullet_point_from_data(self, data):
        possible_answer_list = []

        # Flatten the data and extract valid text strings
        for item in data:
            for blocks in item.get("TEXT_BLOCK", []):
                for text_line in blocks.get("TEXT_LINE", []):
                    for text_string in text_line.get("STRING", []):
                        if text_string:
                            possible_answer_list.append(text_string)

        # Re-arrange based on vertical and horizontal positions
        sorted_list = self.helper.process_utils.rearrange_based_on_vpos_hpos(possible_answer_list)

        # Split the sorted list by bullet points
        sbb_output = SplitByBullets.split_by_bullets(sorted_list)
        value = sbb_output.value

        return value

    def get_curr_page_bullet_point(self, whole_page_data):
        # Extract the bullet points from the current page
        value = self._extract_bullet_point_from_data(whole_page_data)

        if value and len(value) > 1:
            # Extract bullet point from the second element of the list (value[1])
            curr_bullet_point = value[1].strip()
            match = re.match(r"(\d+)\.", curr_bullet_point)
            if match:
                self.curr_page_bullet_point = int(match.group(1))
            else:
                self.curr_page_bullet_point = None
        else:
            self.curr_page_bullet_point = None

        return self.curr_page_bullet_point

    def get_prev_page_bullet_point(self, prev_page_json_data):
        # Extract the bullet points from the previous page
        value = self._extract_bullet_point_from_data(prev_page_json_data)

        if value and len(value) > 0:
            # Extract bullet point from the last element of the list (value[-1])
            last_bp_val = value[-1].strip()
            match = re.match(r"(\d+)\.", last_bp_val)
            if match:
                self.prev_page_bullet_point = int(match.group(1))
            else:
                self.prev_page_bullet_point = None
        else:
            self.prev_page_bullet_point = None

        return self.prev_page_bullet_point





