from docvu_de_core.src.search_key_fields import *
from docvu_de_core.src.table_parser import TableParser
from docvu_de_core.src.crop_image import *
from docvu_de_core.extraction_item import BaseFieldData, TableRow
from docvu_de_core.utils import GeneralUtils
from docvu_de_core.src.post_process import PostProcess
from docvu_de_core.src.output_format_processor import OutputFormatProcessor
from docvu_de_core.src.sub_item_processor import SubItemProcessor
from docvu_de_core.custom import ExceptionTextProcessor, ExceptionTableProcessor
from docvu_de_core.models.ModelManager import ModelManager
from docvu_de_core.search import SearchKeyFieldHelper
from docvu_de_core.extraction_item import BaseTableData
from docvu_de_core.page_info_parser.FooterDetection import FooterDetection
from docvu_de_core.custom.ExceptionSearch import ExceptionSearch
from docvu_de_core.utils.table_formatter import TableFormatter
from docvu_de_core.src.table_processor import TableProcessor


class ParserClass:
    def __init__(self, logger, **kwargs):
        self.search_key_lender = SearchKeyFields(logger, debug=False)
        self.general_utils = GeneralUtils()
        self.get_padding = GetPaddingInfo()
        self.search_page = -1
        self.page_json = None
        self.table_parser = TableParser()
        self.post_process = PostProcess(logger)
        self.ofp = OutputFormatProcessor()
        self.sip = SubItemProcessor()
        self.skfh = SearchKeyFieldHelper(logger)
        self.footer_detection = FooterDetection()
        self.exceptionsearch = ExceptionSearch()
        self.page_no_mandatory_client = ["401"]
        ModelManager.load_config(model_config)
        ModelManager.load(
            logger,
            load_slm = kwargs.get('load_slm', False),
            load_yolo_v8_for_checkbox = kwargs.get('load_yolo_v8_for_checkbox', False),
            load_subdivision_model = kwargs.get('load_subdivision_model', False),
            overwrite = kwargs.get('overwrite_model', True),
            load_yolo_v8_for_lbsd_model=kwargs.get('load_yolo_v8_for_lbsd_model', False),
            load_ner = kwargs.get('load_ner', False)
        )
        self.debug = False
        self.table_formatter = TableFormatter()
        self.table_processor = TableProcessor(logger)

    @staticmethod
    def process_multi_page_header(item, field_key, value, elements, prev_page_value, prev_page_elements,
                                  prev_page_numbers, page_no, page_numbers):
        if "multi_page_value" in item.keys() and item["multi_page_value"]:
            prev_page_value.append(value)
            prev_page_numbers.append(page_no)
            prev_page_elements.append(elements)
            if field_key:
                if "to_continue_next_page" in field_key.keys():
                    continue_search = field_key["to_continue_next_page"]  # True
                else:
                    continue_search = True
            else:
                continue_search = False
            if "search_key_in_all_pages" in item.keys() and item["search_key_in_all_pages"]:
                continue_search = True
            found_on_page = page_no + 1 if value else page_no
            if found_on_page <= len(page_numbers):
                return prev_page_value, prev_page_elements, prev_page_numbers, found_on_page, continue_search
            else:
                return prev_page_value, prev_page_elements, prev_page_numbers, found_on_page, False
        return value, elements, prev_page_numbers, page_no, False

    def separate_sub_keys(self, item, all_results, return_parent=True):
        results = [item] if return_parent else []
        for sub_key in item['sub_keys']:
            sub_key_data = self.general_utils.get_base_format(sub_key, default='text')

            # Check if the field exists and if the value is empty or invalid
            if self.field_exists(all_results, sub_key_data['field_name']) and (
                    not sub_key_data.get('value') or not str(sub_key_data['value']).strip()
            ):
                continue

            sub_key_data['page_number'] = item['page_number']
            results.append(sub_key_data)

        return results

    def field_exists(self, results, field_name):
        for r in results:
            if r['field_name'] == field_name:
                return True
        return False

    @staticmethod
    def process_sub_keys(item, value, page_no, table_formatter):
        item["value"] = value[item["field_name"]] if isinstance(value, dict) else value
        page_no = page_no if item["page_number"] == 0 else item["page_number"]
        
        for sub_index in range(len(item["sub_keys"])):
            sub_field_name = item["sub_keys"][sub_index]["field_name"]
        
            if sub_field_name in value.keys():
                item["sub_keys"][sub_index]["value"] = (
                    value)[item["sub_keys"][sub_index]["field_name"]]
        
                if "field_value_coordinates" in value:
                    item["sub_keys"][sub_index]["field_value_coordinates"] = (
                        value)["field_value_coordinates"]
        
                item["sub_keys"][sub_index]["page_number"] = page_no
                item["sub_keys"][sub_index]["key_info"] = None

                if "additional_info" in item["sub_keys"][sub_index]:
                    if item["sub_keys"][sub_index]["additional_info"].get("1003_table_cb"):
                        item["sub_keys"][sub_index]["bbox"] = value["bbox"]

            if item["sub_keys"][sub_index].get("return_type") == "table" and item["sub_keys"][sub_index]["value"]:
                subkeys_item = table_formatter.table_formatter(item["sub_keys"][sub_index])
        return item

    def process_exception_sub_item(self, extraction_item, page_elements, page_no, sub_items, page_footer):
        etextp = ExceptionTextProcessor()
        etablep = ExceptionTableProcessor()
        regex_list = [
            r"\bAny\s+subject\s+property\s+specific\s+exceptions:",
            r"(?i)\bStandard\s+Exceptions[:?]?\b",
            r"(?i)\bSpecial\s+Exceptions:?\b",
            r"\bTaxes:",
            r"\bExceptions:",
        ]
        page_value = page_elements

        result = []
        exception_table = None
        is_first_epage = True
        is_partii_processed = False
        for value, page in zip(page_value, page_no):
            if page in page_footer:
                footer_starts = page_footer[page]
            else:
                footer_starts = None
            item = extraction_item.copy()
            start_vpos = []
            if ("additional_info" in item.keys() and
                    "search_key" in item["additional_info"].keys()):
                if value and len(value) > 2:
                    if type(value[1]) == dict:
                        if "part" in value[1]["text"].lower():
                            item["value"] = " ".join(v["text"] for v in value[:2])
                            start_vpos = [v["VPOS"] for v in value[:2]]
                        else:
                            item["value"] = " ".join(v["text"] for v in value[:1])
                            start_vpos = [v["VPOS"] for v in value[:1]]
                        item["page_number"] = page
                    result.append(item)
            for sub_index in range(len(item["sub_items"])):
                if start_vpos:
                    start_searching = max(start_vpos)
                else:
                    continue
                f1_excp, f2_item, f2_excp = self.search_key_lender.process_same_page_exceptions(
                        item, value, start_searching, page, footer_starts)
                if f2_excp:
                    result.append(f2_item)
                    final_val = [f1_excp, f2_excp]
                    for f in final_val:
                        sub_field = sub_items[sub_index]
                        ans_value = self.exceptionsearch.get_exception_data_with_start_end_identifiers(
                                f, sub_field, start_searching, use_key=is_first_epage)
                        sub_field["page_number"] = page
                        sub_field["key_info"] = item["key_info"]
                        _, _, image_path = self.page_image_json_data[str(page)]
                        output = etextp(elements=ans_value, regex=regex_list, image_path=image_path)
                        ## SplitByBullets.split_by_bullets(ans_value)
                        exception_table = etablep(sub_field['sub_items'][0],
                                                              sub_field,
                                                              item,
                                                              output=output,
                                                              exception_table=exception_table,
                                                              is_first_epage=is_first_epage)
                        if is_first_epage:
                            is_first_epage = False

                else:
                    sub_field = sub_items[sub_index]
                    part_variations = ["part II", "part I1", "part 1I", "part 11"]
                    ## checking key[part] in value upto 3 lines instead of 1st line for exception
                    found_part = False
                    for i in range(3):  # Iterate over value[0] to value[2]
                        if any(variant.lower() in value[i]["text"].lower() for variant in part_variations):
                            found_part = True
                            break  # Stop checking once found

                    if found_part and not is_partii_processed:
                        # Set both flags to True to prevent further changes
                        is_first_epage = True
                        is_partii_processed = True
                    ans_value = self.exceptionsearch.get_exception_data_with_start_end_identifiers(
                            value, sub_field, start_searching, use_key=is_first_epage)

                    sub_field["value"] = ans_value
                    sub_field["page_number"] = page
                    _, _, image_path = self.page_image_json_data[str(page)]
                    sub_field["key_info"] = item["key_info"]
                    output = etextp(elements=ans_value, regex=regex_list, image_path=image_path)
                    exception_table = etablep(sub_field['sub_items'][0],
                                                          sub_field,
                                                          item,
                                                          output=output,
                                                          exception_table=exception_table,
                                                          is_first_epage=is_first_epage)
                    if is_first_epage:
                        is_first_epage = False
                    sub_items[sub_index] = sub_field



        if exception_table is None:
            exception_table = BaseTableData(**extraction_item)
        return [exception_table]

    def process_sub_items(self, extraction_item, page_elements, page_no, page_footer=None):
        page_value = page_elements

        sub_items = []
        for sub_index in range(len(extraction_item["sub_items"])):
            _sub_field = extraction_item["sub_items"][sub_index].copy()
            sub_field = self.general_utils.get_base_format(_sub_field, default="text")
            output = self.sip(sub_field, extraction_item, page_elements)
            if output.success:
                sub_items.append(output.sub_item)
            else:
                sub_items.append(sub_field)

        if extraction_item['field_name'] == "Exception_Header":
            print(9)
            sub_items = self.process_exception_sub_item(extraction_item, page_elements, page_no, sub_items, page_footer)
        else:
            sub_items.append(extraction_item)

        return sub_items

    def search_alternate_locations(self, ext_item, page_json_data, page_word_json_data, img_path,
                                   page_number, continue_search):
        if "alternate_locations" in ext_item.keys():
            for alternate_keys in ext_item["alternate_locations"]:
                output = self.search_key_lender.check_alternate_locations(
                    alternate_keys, page_json_data, page_word_json_data, img_path, page_number, continue_search)
                return output
        return None

    @staticmethod
    def append_if_not_exists(results, new_item):
        # Check if there's an item with the same field_id and field_name
        for existing_item in results:
            if (existing_item['field_id'] == new_item['field_id'] and
                    existing_item['field_name'] == new_item['field_name']):
                # If found, don't append the new item
                return results, False
        # If not found, append the new item
        results.append(new_item)
        return results, True

    def process_and_extract_data_from_page(self, item, page_no, prev_page_count, page_numbers,
                                           search_prev_page, continue_search,
                                           consider_table_extraction, extraction_fields,
                                           prev_page_value, prev_page_elements, prev_page_numbers,
                                           found_on_page, continue_search_page_count, found_key, total_json_data, data_path):
        line_numbers = []
        value_page_numbers = []
        prev_page_json_data = None
        print(f"Searching Field = {item['name']} , on Page = {page_no}", page_no)
        item["field_key"] = None
        prev_page_count += 1
        if page_no not in page_numbers:
            return ["break", item, None, None, prev_page_count, page_numbers, search_prev_page, continue_search,
                    prev_page_value, prev_page_elements, prev_page_numbers, found_on_page, continue_search_page_count,
                    found_key, line_numbers, value_page_numbers]
        if item.has_attribute("use_prev_key_page_no"):
            if search_prev_page != -1:
                page_no = search_prev_page
        if self.debug:
            print('self.page_image_json_data.keys() =', self.page_image_json_data.keys())
        page_json_data, page_word_json_data, img_path = self.page_image_json_data[str(page_no)]

        if (page_no - 1) not in self.page_image_json_data.keys():
            if not prev_page_json_data and page_no > 1:
                prev_page_json_data, _, _, _ = (
                    self.process_image_and_page_json_data(total_json_data, page_no - 1, data_path))
        else:
            if not prev_page_json_data and page_no > 1:
                prev_page_json_data = self.page_image_json_data[str(page_no - 1)]

        # Save the JSON to the file
        # with open("data.json", "w") as file:
        #     json.dump(page_json_data, file)


        ## taking prev_page_json_data to search key fields
        output = self.search_key_lender.search_key_field(
            item, page_json_data, prev_page_json_data, page_word_json_data, img_path, page_no, continue_search)
        if output.field_key is not None:
            found_key = True
        value = output.value
        elements = output.elements
        line_number = output.line_number
        bbox = output.bbox

        if bbox and isinstance(bbox, list):
            if isinstance(bbox[0], tuple):
                bbox = bbox[0]
            else:
                bbox = tuple(bbox)
            print("bbox!!!!!!", bbox)
        if bbox is not None and not item.is_table and not item.get('multi_page_value', False):
            item.update_coordinates(bbox, type='x1y1x2y2', target='value', is_camel_case=False)
        elif item.is_table and item.get('multi_page_value', False):
            if 'bbox' in item and isinstance(item['bbox'], list):
                if bbox is not None:  # Check if bbox is not None
                    item['bbox'] += bbox
            else:
                if bbox is not None:  # Check if bbox is not None
                    item['bbox'] = bbox
        else:
            if bbox is not None:  # Check if bbox is not None
                item['bbox'] = bbox
        item["field_key"] = output.field_key
        field_key = output.field_key
        if isinstance(line_number, int):
            line_numbers.append(line_number)
        elif isinstance(line_number, list):
            line_numbers.extend(line_number)
        elif line_number is not None:
            line_numbers.append(line_number)
        else:
            line_numbers.append(0)
        value_page_numbers.append(page_no)
        if value is None and consider_table_extraction and item.get("search_in_tables", False):
            tb_data = self.table_parser.extract_from_table_single(
                item, page_json_data, page_no, extraction_fields)

            if tb_data is not None and len(tb_data) > 0:
                field_key = tb_data[0]["key_info"] if "key_info" in tb_data[0] else field_key
                value = tb_data[0]['value'] if "value" in tb_data[0] and tb_data[0][
                    "value"] is not None and len(tb_data[0]["value"].strip()) != 0 else value
                if "additional_info" in item.keys():
                    if "found_val" in item["additional_info"]:
                        item["additional_info"]["found_val"] = tb_data[0]["additional_info"]["found_val"]
                if item["type"].lower() == "date":
                    temp_field_value = self.general_utils.get_dates_in_text(value)
                    value = temp_field_value
                if "additional_info" in item.keys():
                    if "starts_with" in item["additional_info"]:
                        has_keywords = item["additional_info"]["starts_with"]
                        if type(value) is str:
                            value = value.strip()
                            if len(value) > 0:
                                if not (value[0] in has_keywords):
                                    value = None

        if (field_key is None or value is None) and ("alternate_locations" in item.keys()):
            output = self.search_alternate_locations(
                item, page_json_data, page_word_json_data, img_path, page_no, continue_search)
            if output is not None:
                value = output.value
                elements = output.elements
                line_number = output.line_number
                bbox = output.bbox
                item["bbox"] = bbox
                field_key = output.field_key
                if isinstance(line_number, int):
                    line_numbers.append(line_number)
                elif isinstance(line_number, list):
                    line_numbers.extend(line_number)
                elif line_number is not None:
                    line_numbers.append(line_number)
                else:
                    line_numbers.append(0)

        if field_key is None:
            item["key_info"] = field_key

        if self.debug:
            print('Key: {}, Value : {}'.format(field_key, value))
        if not continue_search:
            item["key_info"] = field_key
        item["page_number"] = min(page_no, max(page_numbers))

        if value is not None:
            if "additional_info" in item.keys():
                if "starts_with" in item["additional_info"]:
                    has_keywords = item["additional_info"]["starts_with"]
                    if type(value) is str:
                        value = value.strip()
                        if len(value) > 0:
                            if not (value[0] in has_keywords):
                                if not item["additional_info"].get('consider_only_starts_with', False):
                                    if "found_val" in item["additional_info"]:
                                        item["additional_info"]["found_val"].append(value.strip())
                                    else:
                                        item["additional_info"]["found_val"] = []
                                        item["additional_info"]["found_val"].append(value.strip())
                                    return ["continue", item,  value, elements, prev_page_count, page_numbers, search_prev_page,
                                            continue_search, prev_page_value, prev_page_elements, prev_page_numbers,
                                            found_on_page, continue_search_page_count, found_key, line_numbers,
                                            value_page_numbers]
                                else:
                                    value = None

            if "multi_page_value" in item.keys() and item["multi_page_value"]:
                prev_page_value, prev_page_elements, prev_page_numbers, found_on_page, continue_search = (
                    self.process_multi_page_header(item, field_key, value, elements, prev_page_value,
                                                   prev_page_elements, prev_page_numbers, page_no,
                                                   page_numbers))

                if (not value) and prev_page_value:
                    item["value"] = prev_page_value

            if continue_search:
                continue_search_page_count+=1
                ## stop the extraction for un structure format based on page_limit
                if item.has_attribute("max_page_limit"):
                    max_page_limit = item.get('max_page_limit')
                    if continue_search_page_count > max_page_limit:
                        return ["break", item, value, elements, prev_page_count, page_numbers, search_prev_page,
                                continue_search, prev_page_value, prev_page_elements, prev_page_numbers, found_on_page,
                                continue_search_page_count, found_key, line_numbers, value_page_numbers]
                return ["continue",  item,  value, elements, prev_page_count, page_numbers, search_prev_page, continue_search,
                        prev_page_value, prev_page_elements, prev_page_numbers, found_on_page,
                        continue_search_page_count, found_key, line_numbers, value_page_numbers]
            else:
                item["value"] = prev_page_value

            if item["field_name"] == "Interest_Estate":
                if type(value) is list:
                    value = value[0]

            item["value"] = value
            if found_key:
                found_on_page = page_no
                ## storing the current field page number
                if "save_page_no_for_next_key" in item.keys():
                    search_prev_page = found_on_page - continue_search_page_count
            if item["field_name"] == "Exception_Text":
                found_on_page = page_no
            return ["break", item,  value, elements, prev_page_count, page_numbers, search_prev_page, continue_search,
                    prev_page_value, prev_page_elements, prev_page_numbers, found_on_page, continue_search_page_count,
                    found_key, line_numbers, value_page_numbers]
        else:
            if prev_page_value:
                if ("search_key_in_multi_page" in item) and item.get('search_key_in_multi_page'):
                    return ["continue", item, value, elements, prev_page_count, page_numbers, search_prev_page,
                            continue_search,
                            prev_page_value, prev_page_elements, prev_page_numbers, found_on_page,
                            continue_search_page_count, found_key, line_numbers, value_page_numbers]
                else:
                    return ["break", item, value, elements, prev_page_count, page_numbers, search_prev_page, continue_search,
                            prev_page_value, prev_page_elements, prev_page_numbers, found_on_page,
                            continue_search_page_count, found_key, line_numbers, value_page_numbers]
            elif ("search_key_in_multi_page" in item) and item.get('search_key_in_multi_page'):
                return ["continue", item, value, elements, prev_page_count, page_numbers, search_prev_page,
                        continue_search,
                        prev_page_value, prev_page_elements, prev_page_numbers, found_on_page,
                        continue_search_page_count, found_key, line_numbers, value_page_numbers]
        return [None, item,  value, elements, prev_page_count, page_numbers, search_prev_page, continue_search,
                prev_page_value, prev_page_elements, prev_page_numbers, found_on_page, continue_search_page_count,
                found_key, line_numbers, value_page_numbers]

    def process_image_and_page_json_data(self, total_json_data, page_no, data_path):
        page_json_data = total_json_data[str(page_no)]['COMPOSED_BLOCKS'] if isinstance(
            total_json_data[str(page_no)], dict) else total_json_data[str(page_no)]

        page_word_json_data = self.general_utils.load_word_json_by_page(page_no, data_path)

        img_path = os.path.join(data_path, f"{page_no}.jpg")
        json_path = os.path.join(data_path, f"{page_no}.json")

        if not os.path.exists(img_path):
            raise Exception("Image not found")
        self.search_key_lender.img_shape = [total_json_data[str(page_no)]['PAGE_HEIGHT'],
                                            total_json_data[str(page_no)]['PAGE_WIDTH'], 3]
        self.skfh.update_image_dim([total_json_data[str(page_no)]['PAGE_HEIGHT'],
                                        total_json_data[str(page_no)]['PAGE_WIDTH'], 3])

        self.search_key_lender.extra_padded_vpos_hpos = self.get_padding.get_padding_in_vpos_hpos(img_path)
        return page_json_data, page_word_json_data, img_path, json_path

    def output_format(self, results, de_start_page=0, client_id=None):
        results_temp = []
        for item in results:
            output = self.ofp(results, item, de_start_page=de_start_page, client_id=client_id)
            if output.success:
                if output.multi_item:
                    results_temp += output.item
                else:
                    results_temp.append(output.item)
            else:
                results_temp.append(item)

        results = results_temp
        return results

    def perform_de(self, extraction_fields, total_json_data, data_path, probable_start_page, client_id=None, end_page=None,
                   consider_table_extraction=True):
        print("probable_start_page =", probable_start_page)
        results = []
        page_dimensions = {int(page_no): (total_json_data[str(page_no)]['PAGE_HEIGHT'],
                                          total_json_data[str(page_no)]['PAGE_WIDTH'], 3)
                           for page_no in total_json_data.keys()}
        prev_dummy_found_page = 1
        dummy_section_present_dict = {}
        found_on_page = -1
        search_prev_page = -1
        found_key = False
        dummy_not_found = False
        page_numbers = sorted(page_dimensions.keys())

        page_json_data = None
        prev_page_json_data = None

        # Detect footers in the provided data path
        footer_stats = self.footer_detection.detect_footer(data_path)

        self.page_image_json_data = {}
        # Iterate through all the fields which need to be extracted
        for index, ext_item in enumerate(extraction_fields):
            continue_search_page_count = 0
            continue_search = False
            prev_page_value = []
            prev_page_elements = []
            prev_page_numbers = []
            line_numbers = []
            value_page_numbers = []
            prev_page_count = 0
            elements = []

#           Default: If page_no_mandatory_client list is empty or client_id is not in it, use probable_start_page
            # Modified client_specific possible page no logic by Harsh, Shoaib on 2025-04-28
            possible_page = probable_start_page

            if len(self.page_no_mandatory_client) > 0:
                if str(client_id) in self.page_no_mandatory_client:
                    # page wise logic should work
                    if "possible_page_numbers" in ext_item:
                        possible_page = ext_item["possible_page_numbers"][0]

            #possible_page = probable_start_page # ext_item["possible_page_numbers"][0]
            # If the searching page number is not in the page_numbers then skip the key searching
            if possible_page > len(page_numbers):
                break
            start_page = page_numbers[possible_page - 1]
            if self.debug:
                print('possible_page, page_numbers, start_page =', possible_page, page_numbers, start_page)
            item = self.general_utils.get_base_format(ext_item, default="text")
            item["page_number"] = min(start_page, max(page_numbers))
            item["key_info"] = None
            item["bbox"] = None

            _prev, _next = 0, len(page_numbers) - start_page + 1
            if "max_page_to_search" in item.keys():
                _next = item["max_page_to_search"]
            ## if page_range_before  == "to_start" it will start extraction from starting
            if item.get('page_range_before') == "to_start":
                parsing_start_page = 1
            ## based on page_range_before adjust the pages for extraction
            else:
                parsing_start_page = max(1, start_page - item.get('page_range_before', _prev))
            ## Updating parsing end page for 405(sls_new) client
            if client_id and int(client_id) == 405:
                parsing_end_page = end_page
            else:
                parsing_end_page = min(start_page + _next, len(total_json_data))
            ## use the previous field page number for sequential extraction
            if item.has_attribute("use_prev_field_page_no"):
                if search_prev_page != -1:
                    parsing_start_page = search_prev_page

            if 'dummy' not in item['field_name'].lower() and 'additional_info' in item.keys() and "search_dummy_found_page_only" in item['additional_info'].keys():
                if dummy_not_found:
                    results, is_appended = self.append_if_not_exists(results, item)
                    continue
                else:
                    parsing_start_page = prev_dummy_found_page
                    parsing_end_page = prev_dummy_found_page
            elif 'dummy' in item['field_name'].lower():
                dummy_section = item['field_name'].split('_')[1]
                if dummy_section in dummy_section_present_dict:
                    parsing_start_page = dummy_section_present_dict[dummy_section]+1

            if self.debug:
                print('parsing_start_page, parsing_end_page, len(total_json_data), start_page, _next =',
                  parsing_start_page, parsing_end_page,
                  len(total_json_data), start_page, _next)

            if parsing_start_page > parsing_end_page:
                item["field_key"] = None

            if "exclusive_page_numbers" in item and len(item["exclusive_page_numbers"]) > 0:
                for page_no in item["exclusive_page_numbers"]:
                    if page_no == -1:
                        page_no = max(page_numbers)
                    if page_no not in page_numbers:
                        break
                    if page_no not in self.page_image_json_data.keys():
                        page_json_data, page_word_json_data, img_path, json_path = (
                            self.process_image_and_page_json_data(total_json_data, page_no, data_path))
                        self.page_image_json_data[str(page_no)] = [page_json_data, page_word_json_data, img_path]

                    if (page_no - 1) not in self.page_image_json_data.keys():
                        if not prev_page_json_data and page_no > 1:
                            prev_page_json_data, _, _, _ = (
                                self.process_image_and_page_json_data(total_json_data, page_no - 1, data_path))
                    else:
                        if not prev_page_json_data and page_no > 1:
                            prev_page_json_data = self.page_image_json_data[str(page_no - 1)]

                    [action, item, value, elements, prev_page_count, page_numbers, search_prev_page, continue_search,
                     prev_page_value, prev_page_elements, prev_page_numbers, found_on_page, continue_search_page_count,
                     found_key, line_numbers1, value_page_numbers1] = self.process_and_extract_data_from_page(
                            item, page_no, prev_page_count, page_numbers, search_prev_page, continue_search,
                            consider_table_extraction, extraction_fields, prev_page_value, prev_page_elements,
                            prev_page_numbers, found_on_page, continue_search_page_count, found_key, total_json_data, data_path)

                    line_numbers += line_numbers1
                    value_page_numbers += value_page_numbers1
                    if action == "break":
                        break
                    elif action == "continue" or page_no in item["exclusive_page_numbers"]:
                        continue
            else:
                for page_no in range(parsing_start_page, parsing_end_page+1):
                    if page_no not in self.page_image_json_data.keys():
                        if page_no == 0:
                            pass
                        page_json_data, page_word_json_data, img_path, json_path = (
                            self.process_image_and_page_json_data(total_json_data, page_no, data_path))
                        self.page_image_json_data[str(page_no)] = [page_json_data, page_word_json_data, img_path]

                    if (page_no - 1) not in self.page_image_json_data.keys():
                        if not prev_page_json_data and page_no > 1:
                            prev_page_json_data, _, _, _ = (
                                self.process_image_and_page_json_data(total_json_data, page_no - 1, data_path))
                    else:
                        if not prev_page_json_data and page_no > 1:
                            prev_page_json_data = self.page_image_json_data[str(page_no - 1)]

                    [action, item,  value, elements, prev_page_count, page_numbers, search_prev_page, continue_search,
                    prev_page_value, prev_page_elements, prev_page_numbers, found_on_page, continue_search_page_count,
                    found_key, line_numbers1, value_page_numbers1] = self.process_and_extract_data_from_page(
                    item, page_no, prev_page_count, page_numbers, search_prev_page, continue_search,
                    consider_table_extraction, extraction_fields, prev_page_value, prev_page_elements,
                    prev_page_numbers, found_on_page, continue_search_page_count, found_key, total_json_data, data_path)

                    line_numbers += line_numbers1
                    value_page_numbers += value_page_numbers1
                    if action == "break":
                        break
                    elif action == "continue":
                        continue

            if ("table_processor" and "multi_page_value") in item:
                output = self.table_processor(ext_item, img_path, prev_page_elements)
                
                item["value"] = output.value
                item["line_number"] = output.line_number
                item["bbox"] = output.bbox

                if hasattr(output, 'elements') and output.elements is not None:
                    elements = output.elements

            if not item.is_table:
                if item["value"] is None or (isinstance(item["value"], str) and len(item["value"].strip()) == 0):
                    if "additional_info" in item.keys():
                        if "found_val" in item["additional_info"] and item["additional_info"]["found_val"]:
                            found_val = item["additional_info"]["found_val"]
                            # Get the maximum count
                            max_count = max(found_val.count(x) for x in set(found_val))
                            # If max count is greater than 1, get the most frequent value
                            if max_count > 1:
                                item["value"] = max(set(found_val), key=found_val.count)
                            else:
                                item["value"] = found_val[0]

                ## processing based on rule-specific
                if not item.get('multi_page_value') or item.get('reprocess_if_not_found'):
                    if item["value"] is None or (isinstance(item["value"], str) and len(item["value"].strip()) == 0):
                        if ("probable_type" in item and item["probable_type"].lower() == 'header') or \
                                ("probable_place" in item and item["probable_place"].lower() == 'individual') or \
                                ("use_match" in item and item["use_match"].lower() == 'exact_match'):
                            item["use_match"]=''
                            item["probable_type"] = ''
                            item["probable_place"] = ''
                            for page_no in range(start_page - _prev, start_page + _next):
                                if page_no not in page_numbers:
                                    break
                                if (item.get("exclusive_page_numbers") and \
                                        page_no not in item.get("exclusive_page_numbers", [])):
                                    break
                                if page_no not in self.page_image_json_data.keys():
                                    page_json_data, page_word_json_data, img_path, json_path = (
                                        self.process_image_and_page_json_data(total_json_data, page_no, data_path))
                                    self.page_image_json_data[str(page_no)] = [page_json_data, page_word_json_data, img_path]

                                if (page_no - 1) not in self.page_image_json_data.keys():
                                    if not prev_page_json_data and page_no > 1:
                                        prev_page_json_data, _, _, _ = (
                                            self.process_image_and_page_json_data(total_json_data, page_no - 1, data_path))
                                else:
                                    if not prev_page_json_data and page_no > 1:
                                        prev_page_json_data = self.page_image_json_data[str(page_no - 1)]

                                [action, item, value, elements, prev_page_count, page_numbers, search_prev_page, continue_search,
                                 prev_page_value, prev_page_elements, prev_page_numbers, found_on_page,
                                 continue_search_page_count, found_key, line_numbers1, value_page_numbers1] = (
                                    self.process_and_extract_data_from_page( item, page_no, prev_page_count,
                                                                             page_numbers, search_prev_page,
                                                                             continue_search, consider_table_extraction,
                                                                             extraction_fields, prev_page_value,
                                                                             prev_page_elements, prev_page_numbers,
                                                                             found_on_page, continue_search_page_count,
                                                                             found_key, total_json_data, data_path))

                                line_numbers += line_numbers1
                                value_page_numbers += value_page_numbers1
                                if action == "break":
                                    break
                                elif action == "continue":
                                    continue

            if item.get("multi_page_value"):
                if "table_processor" not in item:
                    item["value"] = prev_page_value
                    value = prev_page_value

            if not item.get('multi_page_value'):
                output = self.post_process(item, elements, page_json_data, prev_page_json_data, img_path)

                if output.value is not None:
                    item["value"] = output.value
                    if hasattr(output, 'elements') and output.elements is not None:
                        elements = output.elements
                    if not item.is_table:
                        item["post_processing_value"] = output.value
                elif 'value' not in item.keys():
                    item["value"] = value
                    if not item.is_table:
                        item["post_processing_value"] = value

                if output.bbox is not None or 'bbox' not in item:
                    item["bbox"] = output.bbox

                    if "field_value_coordinates" in item:
                        item["field_value_coordinates"] = output.bbox

                if output.line_number is not None or not hasattr(item, 'line_number'):
                    item["line_number"] = output.line_number
            else:
                output = self.post_process(item, [ppe for ppe in prev_page_elements], page_json_data, prev_page_json_data, img_path)

                if output.value is not None:
                    item["value"] = output.value
                    if not item.is_table:
                        item["post_processing_value"] = output.value
                elif 'value' not in item.keys():
                    item["value"] = value
                    if not item.is_table:
                        item["post_processing_value"] = value

                if output.bbox is not None or 'bbox' not in item:
                    item["bbox"] = output.bbox

                    if "field_value_coordinates" in item:
                        item["field_value_coordinates"] = output.bbox

                if output.line_number is not None or not hasattr(item, 'line_number'):
                    item["line_number"] = output.line_number

            if item.is_table:
                value = item["value"]
                bbox = item["bbox"]
                field_key = item["field_key"] if hasattr(item, 'field_key') else None
                if value and isinstance(value[0], list):
                    value = [item for sublist in value for item in sublist]
                if value is None:
                    value = []
                item["confidence_indicator"] = 0.98
                item["color_indicator"] = 1
                item["start_page"] = item["page_number"]
                item["end_page"] = item["page_number"]
                if isinstance(value, str):
                    value = [value]
                for idx, v in enumerate(value):
                    if bbox and isinstance(bbox, list) and \
                            (isinstance(bbox[0], list) or isinstance(bbox[0], tuple)) and\
                            len(bbox) > idx and bbox[idx]:
                        coord_dict = {"x": bbox[idx][0], "y": bbox[idx][1],
                                      "width": abs(bbox[idx][0] - bbox[idx][2]),
                                      "height": abs(bbox[idx][1] - bbox[idx][3])}
                    else:
                        coord_dict = {"x": 0, "y": 0, "width": 0, "height": 0}

                    if (len(line_numbers) > 0) and (len(line_numbers) > idx):
                        ln = line_numbers[idx] if line_numbers[idx] else 0
                    else:
                        ln = 0
                    id = item['field_id']
                    name = item['field_name']
                    key = field_key['text'] if field_key and field_key['text'] else ''
                    pn = item["page_number"]
                    columns = [
                        BaseFieldData(id=id,
                                      field_name=name,
                                      key=key,
                                      value=v,
                                      post_processing_value=v,
                                      page_number=pn-1 if item.get('multi_page_value', False) else pn,
                                      line_number=ln,
                                      confidence_indicator=.95,
                                      color_indicator=1,
                                      field_name_coordinates={"x": 0, "y": 0, "width": 0, "height": 0},
                                      field_value_coordinates=coord_dict)
                    ]
                    row = TableRow(row_number=item.n_rows(), page_number=found_on_page, line_number=ln, columns=columns)
                    item.add_row(row)

            elif (not item.is_table) and "multi_page_value" in item and item["value"] is not None:
                value = item["value"]
                if len(prev_page_elements) > 1:
                    elements = []
                    for element in prev_page_elements:
                        elements += element
                if isinstance(value, list):
                    item["value"] = [' '.join([str(v) for v in value if v])]
                else:
                    item["value"] = value

            if "sub_items" in item.keys():
                res = self.process_sub_items(item, prev_page_elements, prev_page_numbers, footer_stats)
                for i in range(len(res)):
                    res[i].update_post_processing_value_if_not_found()
                results += res

                if "sub_keys" in item.keys():
                    res = self.separate_sub_keys(item, return_parent=False)
                    for i in range(len(res)):
                        res[i].update_post_processing_value_if_not_found()
                    results += res
            elif "sub_keys" in item.keys():
                value = item["value"]
                if value is not None:
                    sub_item_dict = self.search_key_lender.get_sub_keys_values(
                        elements, item, img_path=img_path)
                    value = sub_item_dict

                    if isinstance(value, str):
                        value_str = value
                        value = {item["field_name"]: value_str}
                    item = self.process_sub_keys(item, value, page_no, self.table_formatter)

                res = self.separate_sub_keys(item, results)

                for i in range(len(res)):
                    res[i].update_post_processing_value_if_not_found()
                results += res
            else:
                item.update_post_processing_value_if_not_found()
                results, is_appended = self.append_if_not_exists(results, item)
                if item is None:
                    print(4)
                # results.append(item)
            if 'dummy' in item['field_name']:
                if not item['value']:
                    dummy_not_found = True
                    prev_dummy_found_page = 1
                    if item['field_name'].split('_')[1] not in dummy_section_present_dict:
                        dummy_section_present_dict[item['field_name'].split('_')[1]] = 0
                else:
                    dummy_not_found = False
                    prev_dummy_found_page = found_on_page
                    dummy_section_present_dict[item['field_name'].split('_')[1]] = prev_dummy_found_page
            print("*=" * 40)
            field_name = item["field_name"]
            field_value = item["value"] if not item['is_table'] else item['rows']
            field_page_no = item["page_number"]
            if not item.is_table:
                print(f"On Page : {field_page_no}\nField Name: = {field_name}\nField Value: = {field_value}\n")
            print("*=" * 40)

            prev_page_json_data = page_json_data

        ## post_process_results
        results = self.output_format(results, de_start_page=probable_start_page, client_id=client_id)

        return results, page_dimensions


if __name__ == "__main__":
    print("Done")