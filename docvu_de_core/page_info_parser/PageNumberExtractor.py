import json
import re
from typing import Dict, Optional, List

from docvu_de_core.modules.KofaxXML2JSONConverter import XMLToJSONConverter


class PageNumberExtractor:
    def __init__(self, top_section_percentage=0.15, bottom_section_percentage=0.15, max_page_number=100):
        """
        Initialize the PageNumberExtractor with parameters for detecting the top and bottom sections of the page.

        :param top_section_percentage: Percentage of the page height to consider as the top section.
        :param bottom_section_percentage: Percentage of the page height to consider as the bottom section.
        :param max_page_number: Maximum valid page number to consider.
        """
        self.top_section_percentage = top_section_percentage
        self.bottom_section_percentage = bottom_section_percentage
        self.max_page_number = max_page_number

    def load_ocr_data(self, file_path: str) -> Dict:
        """
        Load OCR data from a JSON file.

        :param file_path: Path to the OCR JSON file.
        :return: Parsed OCR data.
        """
        with open(file_path, 'r') as file:
            ocr_data = json.load(file)
        return ocr_data

    def extract_page_number(self, ocr_data: Dict) -> Optional[int]:
        """
        Extract the page number from OCR data.

        :param ocr_data: OCR data dictionary.
        :return: Detected page number or None if not found.
        """
        page_height = ocr_data.get("PAGE_HEIGHT", 2200)
        max_upper_vpos_permissible = int(page_height * self.top_section_percentage)
        min_lower_vpos_permissible = int(page_height * (1 - self.bottom_section_percentage))

        # Regular expressions to identify page number formats
        regular_expression_list = [
            (r"page\s+(\d{1,4})", "page", -1),  # e.g., "Page 1"
            (r"page(\d{1,4})", "page", -1),  # e.g., "Page1"
            (r"(\d{1,4})\s+of\s+(\d{1,4})", "of", 0),  # e.g., "1 of 10"
            (r"(\d{1,4})\s+0f\s+(\d{1,4})", "0f", 0),  # e.g., "1 0f 10" (common OCR error for 'of')
            (r"^(\d{1,4})$", "", 0)  # e.g., "1" (simple page number)
        ]

        # Consider only the top and bottom sections of the page
        text_to_search = []

        for block in ocr_data["COMPOSED_BLOCKS"]:
            for text_block in block["TEXT_BLOCK"]:
                for text_line in text_block["TEXT_LINE"]:
                    vpos = text_line["VPOS"]

                    # Check if the line is in the top or bottom section of the page
                    if vpos <= max_upper_vpos_permissible or vpos >= min_lower_vpos_permissible:
                        for string in text_line["STRING"]:
                            text_to_search.append(string["text"])

        # Combine the relevant text lines into a single string
        search_string = ' '.join(text_to_search)

        # Search for page numbers using regular expressions
        for pattern, split_keyword, group_index in regular_expression_list:
            regex = re.compile(pattern, re.IGNORECASE)
            match = regex.search(search_string)
            if match:
                # Extract the matched text and determine the page number
                result_text = match.group()
                # Split and extract the page number
                if split_keyword:
                    page_number = result_text.lower().split(split_keyword)[group_index]
                else:
                    page_number = result_text

                # Attempt to convert the extracted text to an integer
                try:
                    page_number = int(page_number)
                    # Filter out numbers that are too large to be valid page numbers
                    if 1 <= page_number <= self.max_page_number:
                        return page_number
                except ValueError:
                    continue  # If conversion fails, continue searching

        # Return None if no page number is found
        return None

    def process_document(self, file_path: str) -> Optional[int]:
        """
        Process the document to extract the page number.

        :param file_path: Path to the OCR JSON file.
        :return: Detected page number or None if not found.
        """
        ocr_data = self.load_ocr_data(file_path)
        page_number = self.extract_page_number(ocr_data)
        return page_number

    def process_document_from_xml(self, page_xml: str) -> Optional[int]:
        """
        Process the document to extract the page number from XML data.

        :param page_xml: XML string of the page.
        :return: Detected page number or None if not found.
        """
        converter = XMLToJSONConverter()
        ocr_data = converter.convert_to_json_new(xml_text=page_xml,
                                                 enable_vertical_separation=False,
                                                 iW=None, iH=None)
        page_number = self.extract_page_number(ocr_data)
        return page_number

    def detect_page_number_anomalies(self, page_numbers: List[Optional[int]]) -> Dict[str, List[int]]:
        """
        Detects anomalies in a sequence of page numbers, accounting for missing numbers (None).

        Args:
            page_numbers (list): List of page numbers where None represents unnumbered pages.

        Returns:
            dict: A dictionary with lists of missing, duplicate, out-of-order, and unnumbered pages.
        """
        anomalies = {
            'missing_pages': [],
            'duplicate_pages': [],
            'out_of_order_pages': [],
            'unnumbered_pages': [],
        }

        if not page_numbers:
            return anomalies

        # Track seen pages to find duplicates
        seen_pages = set()

        # Initialize the previous page number for order checking
        previous_page_number = None  # Start as None

        # Iterate over the list of page numbers
        for i, page_number in enumerate(page_numbers):

            # Track unnumbered pages
            if page_number is None:
                anomalies['unnumbered_pages'].append(i)
                continue

            # Check for duplicates
            if page_number in seen_pages:
                anomalies['duplicate_pages'].append(i)
            else:
                seen_pages.add(page_number)

            # Check for out-of-order pages
            if previous_page_number is not None and page_number <= previous_page_number:
                anomalies['out_of_order_pages'].append(i)

            # Check for missing pages (considering non-negative and continuous)
            if previous_page_number is not None and page_number > previous_page_number + 1:
                missing_range = list(range(previous_page_number + 1, page_number))
                anomalies['missing_pages'].extend(missing_range)

            # Update the previous page number
            previous_page_number = page_number

        return anomalies


# Usage example
if __name__ == '__main__':
    page_number_extractor = PageNumberExtractor(top_section_percentage=0.25, bottom_section_percentage=0.1,
                                                max_page_number=100)
    file_path = '/Users/<USER>/Repos/SOFTWARE-SERVICE-%20document_extraction/ocr_output/64b8ec43-9d76-4e6d-a7a6-e4aa75060d2d/5.json'  # Path to your OCR JSON file

    # Assuming the document has multiple pages, we simulate by creating a list
    # You would normally extract each page's number in a loop
    page_numbers = []
    for i in range(10):  # Simulating a 10-page document
        page_number = page_number_extractor.process_document(file_path)
        page_numbers.append(page_number)

    # Detect anomalies in the sequence of page numbers
    anomalies = page_number_extractor.detect_page_number_anomalies(page_numbers)

    print("Detected Page Numbers:", page_numbers)
    print("Missing Pages:", anomalies['missing_pages'])
    print("Duplicate Pages:", anomalies['duplicate_pages'])
    print("Out-of-Order Pages:", anomalies['out_of_order_pages'])
    print("Unnumbered Pages (indices):", anomalies['unnumbered_pages'])