import copy

from docvu_de_core.policy_classifier import *
from docvu_de_core.src import *
from docvu_de_core.utils import ResponseFormatter
from docvu_de_core import de_config_path 
from docvu_de_core import client_path 
from docvu_de_core import configure_logging
from docvu_de_core.modules import XMLToJSONConverter
from docvu_de_core.extraction_item import BaseTableData, BaseFieldData
from docvu_de_core.config import * 
from docvu_de_core.FormTypeConfig import FormTypeConfig


import os
import json


class API:
    def __init__(self, curr_path, ocr_dir="./ocr_output", mapper_config_path=None, **kwargs):
        logger = configure_logging(curr_path)
        self.classify_policy = ClassifyOld()
        self.parser = ParserClass(logger, **kwargs)
        self.formatter = ResponseFormatter()
        self.form_config = FormTypeConfig()       
        self.ocr_dir = ocr_dir
        
        # Load the JSON configuration for the document ID mapper
        print("Mapper Config Path:", mapper_config_path)
        if mapper_config_path is not None:
            with open(mapper_config_path, 'r') as config_file:
                self.mapper_config = json.load(config_file)
        else:
            self.mapper_config = None

    def get_correct_de_config(self, combined_json):
        form_type, search_start_page, num_pages = (
            self.classify_policy.process_form(combined_json, is_owner=False))
        return form_type, search_start_page, num_pages

    def _classify(self, pdf_data_path=None,
                  ocr_xml_json_path=None,
                  pdf_file_path=None,
                  classification_required=True,
                  return_class_id_and_name=False,
                  for_testing=False):
        if pdf_data_path is None and ocr_xml_json_path is not None and pdf_file_path is not None:
            combined_json_path, pdf_data_path = self._prepare_ocr_json(ocr_xml_json_path, pdf_file_path,
                                                                       for_testing=for_testing)

        combined_json_path = os.path.join(pdf_data_path, 'combined.json')
        form_type, search_start_page, num_pages = self.get_correct_de_config(combined_json_path)


        if form_type == "misc":
            return "misc", search_start_page

        if not classification_required:
            return form_type, search_start_page

        if os.path.isfile(combined_json_path):
            with open(combined_json_path) as temp_f:
                json_data = json.loads(temp_f.read())
        else:
            print("Combined JSON doesn't exist")
            return None, search_start_page
        page_numbers = sorted([int(it) for it in json_data.keys()])

        to_select_config_file = self.form_config.form_type_dict.get("identifier")
        
        with open(to_select_config_file) as temp_f:
            identifier_config = json.loads(temp_f.read())

        raw_results, page_dimensions = self.parser.perform_de(identifier_config, json_data, pdf_data_path, search_start_page)
        policy_number = None
        policy_type_text = None
        modified_for_policy_type_text = False
        for item in raw_results:
            res_val = self.formatter.post_process_text(item)
            if item["field_name"] == "Policy_Type_Text":
                value = res_val
                if value is not None:
                    if type(value) is list:
                        value = value[0]
                    policy_type_text = value
            if item["field_name"] == "Full_Policy_Number":
                policy_number = res_val

        if policy_type_text is not None:
            if "short" in policy_type_text.lower():
                form_type = "short_form"
                modified_for_policy_type_text = True
            elif "owner" in policy_type_text.lower():
                form_type = "owner_form"
                modified_for_policy_type_text = True
            elif "junior" in policy_type_text.lower():
                form_type = "long_form"
                search_start_page -= 1
                modified_for_policy_type_text = True
            else:
                prev_form_type = form_type
                form_type = "long_form"

                if prev_form_type != form_type:
                    modified_for_policy_type_text = True

        ## give priority to owner form if policy_number is starting with digit
        if policy_number is not None \
                and ("O-" in policy_number or (len(policy_number) > 1
                                    and policy_number[0].isdigit())) \
                and form_type != "owner_form" and not modified_for_policy_type_text:
            form_type = "owner_form"
        if self.mapper_config is not None:
            form_id, form_name = self.mapper_config['DocumentName2IdMapper'][form_type]

        if return_class_id_and_name and self.mapper_config is not None:
            return form_id, form_name, form_type, search_start_page
        else:
            return form_type, search_start_page
    def _extract(self,
                 form_type,
                 search_start_page,
                 client_id = None,
                 pdf_data_path=None,
                 ocr_xml_json_path=None,
                 pdf_file_path=None,
                 return_indexing_info=True,
                 to_extract_field=None,
                 document_id=None,
                 document_type=None,
                 reference_no=None,
                 fetch_prev_to_extract_field=False,
                 separate_table_data = True,
                 for_testing=False):

        ## Apply client-specific page_boundaries
        if client_id:
            # Check if the client ID exists in the config, and update the default values if found
            client_boundaries = page_bound_configs.get(int(client_id))
            if client_boundaries:
                page_bound_configs.update(client_boundaries)

        if pdf_data_path is None and ocr_xml_json_path is not None and pdf_file_path is not None:
            combined_json_path, pdf_data_path = self._prepare_ocr_json(ocr_xml_json_path, pdf_file_path,
                                                                       for_testing=for_testing)

        combined_json_path = os.path.join(pdf_data_path, 'combined.json')

        if os.path.isfile(combined_json_path):
            with open(combined_json_path) as temp_f:
                json_data = json.loads(temp_f.read())
        else:
            print("Combined JSON doesn't exist")
            return None
        print("Selected Form {} -> is: {}, To Start Extract from Page No. {}".
              format(pdf_data_path, form_type, search_start_page))
        config_file = None
        use_client_specific_file_path = False
        
        # Load config file path based on form type
        
        if str(client_id) in ["452","401", "449", "443", "403", "439","440","433"]:
            print("INSIDE CLIENT ID IF LOOP")
            config_file = self.form_config.get_form_path(client_id, document_id)
            
            if config_file:
                config_file_path = os.path.join(client_path, str(client_id), config_file)
            else:
                config_file_path = None
            use_client_specific_file_path = True
        else:
            config_file_path = self.form_config.get_form_path(client_id, document_id)
            
        print("document_id!!!!!!!", document_id, config_file_path)

        if not config_file_path:
            return None
        with open(config_file_path, 'r') as f:
            config_file  = json.load(f)

        if isinstance(config_file, dict):
            classify_obj = Classify(config_file)
            classify_result = classify_obj.process_form(combined_json_path=combined_json_path)
            config_file_name = classify_result["document_type"]
            if use_client_specific_file_path:
                config_file_path = os.path.join(client_path, str(client_id), config_file_name)
            else:
                config_file_path = os.path.join(de_config_path, config_file_name)
            print("Updated config_file_path", config_file_path)
        extraction_fields = self.load_config_json(config_file_path)

        if to_extract_field:
            to_search = [i for i, val in enumerate(extraction_fields) if val["field_name"] == to_extract_field]
            if to_search:
                to_search = to_search[0]
            else:
                return None
        else:
            to_search = -1
        if to_search != -1:
            if fetch_prev_to_extract_field:
                extraction_fields = extraction_fields[0:to_search + 1]
            else:
                extraction_fields = extraction_fields[to_search:to_search + 1]

        page_numbers = sorted([int(it) for it in json_data.keys()])
        # json_data = self.parser.rearrange_page_json(json_data, page_numbers)
        raw_results, page_dimensions = self.parser.perform_de(extraction_fields, json_data, pdf_data_path, search_start_page, client_id)
        print("--------> Formatting Started for {}".format(pdf_data_path))

        results = self.formatter.get_formatted_json_output_new(raw_results, page_dimensions, separate_table_data = separate_table_data)
        print('results....', results)

        # print(file_path)
        file_name = pdf_data_path.split('/')[-1]
        de_results = results
        if for_testing:
            de_results = {'Form_type': form_type, 'Original_Image_Name': file_name,
                        'Original_Image_Length': len(page_numbers), 'Value': results}
            return de_results
        if not client_id or str(client_id) not in ["402", "405"]:
            return de_results
        else:
            if self.mapper_config:
                de_results = self.field_id_mapper_sls_process(document_id, reference_no, results)
            return de_results
            
        
    def field_id_mapper_sls_process(self, document_id, reference_no, results):
        
        exception_doc_id = 9002234
        endorsement_doc_id = 9002249
        indexed_results = {document_id: {'FieldData': [], 'TableData': []},
                        exception_doc_id: {'FieldData': [], 'TableData': []},
                        endorsement_doc_id: {'FieldData': [], 'TableData': []}}
        ## Process FieldData
        id_mapper_dit = self.mapper_config['TextFieldConfigurationIdMapper'][str(document_id)][
            'TextFieldConfigurationIdMapper']
        not_found_names = list(id_mapper_dit.keys())
        for idx, val in enumerate(results['FieldData']):
            if results['FieldData'][idx]['Name'] in id_mapper_dit:
                if results['FieldData'][idx]['Name'] in id_mapper_dit:
                    results['FieldData'][idx]['Id'] = str(id_mapper_dit[results['FieldData'][idx]['Name']])
                    if results['FieldData'][idx]['Name'] in not_found_names:
                        not_found_names.remove(results['FieldData'][idx]['Name'])

                # results['FieldData'][idx]['Id'] = id_mapper_dit[results['FieldData'][idx]['Name']]
                indexed_results[document_id]['FieldData'].append(results['FieldData'][idx])
                if results['FieldData'][idx]['Name'] in not_found_names:
                    not_found_names.remove(results['FieldData'][idx]['Name'])
            else:
                pass
        for idx, val in enumerate(results['TableData']):
            if results['TableData'][idx]['Name'] in id_mapper_dit:
                not_found_names.remove(results['TableData'][idx]['Name'])

                id = str(id_mapper_dit[results['TableData'][idx]['Name']])
                id_mapper = {results['TableData'][idx]['Name'] : id}
                results['TableData'][idx].update_field_ids(id_mapper, is_camel_case=True)
                indexed_results[document_id]['TableData'].append(results['TableData'][idx])
        for nfn in not_found_names:
            result = BaseFieldData(from_camel_case=True, force_fill=True, **{
                                                            "Id": id_mapper_dit[nfn],
                                                            "Name": nfn,
                                                            "Key": '',
                                                            "Value": '',
                                                            "PostProcessingValue": '',
                                                            "PageNumber": 0,
                                                            "LineNumber": 0,
                                                            "ConfidenceIndicator": 0.0,
                                                            "ColorIndicator": 0,
                                                            "FieldNameCoordinates": None,
                                                            "FieldValueCoordinates": None
                                                        })
            result.fill_defaults()
            result.drop_non_default_fields()
            result.to_camel_case()
            indexed_results[document_id]['FieldData'].append(result)

        ## Process TableData Exeption
        id_mapper_dit = self.mapper_config['TextFieldConfigurationIdMapper'][str(exception_doc_id)][
            'TextFieldConfigurationIdMapper']
        found_exception = False
        for idx, val in enumerate(results['TableData']):
            if results['TableData'][idx]['Name'] in id_mapper_dit:
                found_exception = True
                results['TableData'][idx].update_field_ids(id_mapper_dit, is_camel_case = True)
                indexed_results[exception_doc_id]['TableData'].append(results['TableData'][idx])
                break
        if not found_exception:
            result = BaseTableData(confidence_indicator=None,
                            color_indicator=None,
                            start_page=None,
                            end_page=None, rows=[])
            result.fill_defaults()
            result.to_camel_case()
            indexed_results[exception_doc_id]['TableData'].append(result)

        ## Process TableData Endorsement
        id_mapper_dit = self.mapper_config['TextFieldConfigurationIdMapper'][str(endorsement_doc_id)][
            'TextFieldConfigurationIdMapper']
        found_endorsement = False
        for idx, val in enumerate(results['TableData']):
            if results['TableData'][idx]['Name'] in id_mapper_dit:
                found_endorsement = True
                results['TableData'][idx].update_field_ids(id_mapper_dit, is_camel_case = True)
                indexed_results[endorsement_doc_id]['TableData'].append(results['TableData'][idx])
                break
        if not found_endorsement:
            result = BaseTableData(confidence_indicator=None,
                                color_indicator=None,
                                start_page=None,
                                end_page=None, rows=[])
            result.fill_defaults()
            result.to_camel_case()
            indexed_results[endorsement_doc_id]['TableData'].append(result)

        extracted_document_data = []
        for doc_id in [document_id, endorsement_doc_id, exception_doc_id]:
            extracted_document_data.append({
                'DocumentId': str(doc_id),
                'DocumentType': self.mapper_config['TextFieldConfigurationIdMapper'][str(doc_id)][
                    'DocumentName'],
                'FieldData': indexed_results[doc_id]['FieldData'],
                'TableData': indexed_results[doc_id]['TableData']
            })

        de_results = {
                    'ReferenceNo': reference_no,
                    'ExtractedDocumentData': extracted_document_data
        }

        return de_results


    def _prepare_ocr_json(self, ocr_xml_json_path, pdf_file_path, for_testing=False):
        converter = XMLToJSONConverter(input_kofax_json_path=ocr_xml_json_path, pdf_file_path=pdf_file_path,
                                       output_dir=self.ocr_dir, horizontal_threshold_line=80)
        combined_json_filename = os.path.join(converter.output_dir, f"combined.json")
        
        isExist = os.path.exists(combined_json_filename)
        if isExist and for_testing:
            return combined_json_filename, converter.output_dir
        combined_json_path, pdf_data_path = (
            converter.process_pages_and_generate_jsons(enable_vertical_separation=False, for_testing=for_testing))
        return combined_json_path, pdf_data_path

    def load_config_json(self, config_file):
        sub_keys_config_file = self.form_config.form_type_dict.get("sub_keys")

        with open(config_file) as temp_f:
            extraction_fields = json.loads(temp_f.read())
        with open(sub_keys_config_file) as temp_f:
            sub_keys_fields = json.loads(temp_f.read())

        updated_extraction_fields = extraction_fields.copy()
        for index, ext_item in enumerate(updated_extraction_fields):
            if "sub_keys" in ext_item.keys():
                sub_keys_fields_temp = copy.deepcopy(sub_keys_fields)
                required_sub_key_fields = ext_item["sub_keys"]
                sub_keys = []
                for re_sub_key in required_sub_key_fields:
                    if re_sub_key in sub_keys_fields_temp.keys():
                        if sub_keys:
                            sub_keys += sub_keys_fields_temp[re_sub_key]
                        else:
                            sub_keys = sub_keys_fields_temp[re_sub_key]
                ext_item["sub_keys"] = sub_keys
            elif "sub_items" in ext_item.keys():
                sub_keys_fields_temp = copy.deepcopy(sub_keys_fields)
                for index, sub_item in enumerate(ext_item["sub_items"]):
                    if "sub_keys" in sub_item.keys():
                        required_sub_key_fields = ext_item["sub_items"][index]["sub_keys"]
                        sub_keys = []
                        for re_sub_key in required_sub_key_fields:
                            if re_sub_key in sub_keys_fields_temp.keys():
                                if sub_keys:
                                    sub_keys += sub_keys_fields_temp[re_sub_key]
                                else:
                                    sub_keys = sub_keys_fields_temp[re_sub_key]
                        ext_item["sub_items"][index]["sub_keys"] = sub_keys
        return updated_extraction_fields

    def clean(self, file_name=None):
        """
        Deletes contents of the OCR directory. If file_name is provided, deletes that specific file;
        otherwise, deletes all contents of the directory.

        :param file_name: Optional; name of a specific file to delete.
        """
        if file_name:
            # Build the full path to the file
            file_path = os.path.join(self.ocr_dir, file_name)
            # Check if the file exists and delete
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"Deleted file: {file_path}")
            else:
                print(f"No such file: {file_path}")
        else:
            # Check if the directory exists
            if os.path.exists(self.ocr_dir):
                # Delete all contents of the directory
                for filename in os.listdir(self.ocr_dir):
                    file_path = os.path.join(self.ocr_dir, filename)
                    try:
                        if os.path.isfile(file_path) or os.path.islink(file_path):
                            os.unlink(file_path)
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                    except Exception as e:
                        print(f'Failed to delete {file_path}. Reason: {e}')
                print(f"All contents of {self.ocr_dir} have been deleted.")
            else:
                print(f"No such directory: {self.ocr_dir}")