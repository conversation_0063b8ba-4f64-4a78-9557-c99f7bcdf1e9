import json
from typing import List, Tu<PERSON>, Dict, Optional
from docvu_de_core.page_info_parser.BigFontClusterer import BigFontClusterer
import re


class Classify:
    def __init__(self, config):
        if isinstance(config, str):
            with open(config, 'r') as f:
                self.config = json.load(f)
        elif isinstance(config, dict):
            self.config = config

        self.max_upper_block = self.config.get('max_upper_block', 3)
        self.use_upper_split_percentage = self.config.get('use_upper_split_percentage', 0.45)
        self.max_lines_for_header = self.config.get('max_lines_for_header', 5)
        self.max_upper_lines_for_key_search = self.config.get('max_upper_lines_for_key_search', 8)
        self.debug = self.config.get('debug', True)

        # Enhanced multi-variant classification features
        self.enable_multi_variant = self.config.get('enable_multi_variant', True)
        self.confidence_threshold = self.config.get('confidence_threshold', 0.6)
        self.enable_confidence_scoring = self.config.get('enable_confidence_scoring', True)
        self.case_sensitive_matching = self.config.get('case_sensitive_matching', False)
        self.partial_string_matching = self.config.get('partial_string_matching', True)
        self.variant_priority_order = self.config.get('variant_priority_order', [])

        # Backward compatibility flag
        self.use_legacy_mode = self.config.get('use_legacy_mode', False)

    def sort_list(self, l: List[Tuple]) -> List[Tuple]:
        def is_upper_case(s: str) -> bool:
            return s.isupper()

        sorted_list = sorted(l, key=lambda x: (not is_upper_case(x[2]), -x[1], x[0]))
        return sorted_list

    def enhanced_pattern_matching(self, text_list: List[str], include_strings: List[str],
                                exclude_strings: List[str]) -> Tuple[bool, float, List[str]]:
        """
        Enhanced pattern matching with confidence scoring for multi-variant classification.

        Returns:
            Tuple[bool, float, List[str]]: (match_found, confidence_score, matched_patterns)
        """
        if not include_strings:
            return False, 0.0, []

        matched_patterns = []
        total_patterns = len(include_strings)

        for text in text_list:
            if not text or not text.strip():
                continue

            text_clean = text.strip()
            text_compare = text_clean if self.case_sensitive_matching else text_clean.lower()

            for pattern in include_strings:
                pattern_compare = pattern if self.case_sensitive_matching else pattern.lower()

                # Check for matches
                match_found = False
                if self.partial_string_matching:
                    match_found = pattern_compare in text_compare
                else:
                    match_found = pattern_compare == text_compare

                if match_found:
                    # Check exclude patterns
                    excluded = False
                    for exclude_pattern in exclude_strings:
                        exclude_compare = exclude_pattern if self.case_sensitive_matching else exclude_pattern.lower()
                        if exclude_compare in text_compare:
                            excluded = True
                            break

                    if not excluded and pattern not in matched_patterns:
                        matched_patterns.append(pattern)

        # Calculate confidence based on pattern matches
        match_ratio = len(matched_patterns) / total_patterns if total_patterns > 0 else 0
        confidence = min(match_ratio * 1.2, 1.0)  # Boost confidence slightly, cap at 1.0

        # Bonus for multiple matches
        if len(matched_patterns) > 1:
            confidence = min(confidence + 0.1, 1.0)

        match_found = len(matched_patterns) > 0

        if self.debug and match_found:
            print(f"Enhanced matching - Patterns: {matched_patterns}, Confidence: {confidence:.2f}")

        return match_found, confidence, matched_patterns

    def get_upper_blocks_data_from_all_pages(self, merged_page_json: str) -> Tuple[
        Dict[int, List[str]], Dict[int, List[int]]]:
        with open(merged_page_json) as temp_f:
            merged_pages_dict = json.loads(temp_f.read())

        upper_pages = {}
        text_height = {}
        page_count = merged_pages_dict.keys()
        for page in page_count:
            block_text = []
            height = []
            found_end = False
            page_json_data = merged_pages_dict[str(page)]['COMPOSED_BLOCKS'] if isinstance(merged_pages_dict[str(page)],
                                                                                           dict) else merged_pages_dict[
                str(page)]
            for index, block_data in enumerate(page_json_data):
                img_height = int(block_data["IMAGE_HEIGHT"]) if "IMAGE_HEIGHT" in block_data.keys() else -1
                max_upper_vpos_permissible = int(img_height * self.use_upper_split_percentage)
                if index > self.max_upper_block:
                    continue
                for block in block_data["TEXT_BLOCK"]:
                    text_lines = block["TEXT_LINE"]
                    for lines in text_lines:
                        strings = lines["STRING"]
                        for string in strings:
                            if img_height != -1 and int(string["VPOS"]) > max_upper_vpos_permissible:
                                continue
                            if found_end:
                                break
                            block_text.append(string["text"])
                            height.append(string["HEIGHT"])
                        if found_end:
                            break
                    if found_end:
                        break
                if found_end:
                    break
            if found_end:
                break
            upper_pages[int(page)] = block_text
            text_height[int(page)] = height
        return upper_pages, text_height

    def header(self, block_texts: Dict[int, List[str]], block_text_height: Dict[int, List[int]], config: Dict) -> Tuple[bool, int, int, float]:
        return self.check_section(block_texts, block_text_height, config)

    def body(self, block_texts: Dict[int, List[str]], block_text_height: Dict[int, List[int]], config: Dict) -> Tuple[bool, int, int, float]:
        return self.check_section(block_texts, block_text_height, config)

    def footer(self, block_texts: Dict[int, List[str]], block_text_height: Dict[int, List[int]], config: Dict) -> Tuple[bool, int, int, float]:
        return self.check_section(block_texts, block_text_height, config)

    def big_font(self, block_texts: Dict[int, List[str]], config: Dict) -> List[str]:
        return self.apply_big_font_clusterer(block_texts, config)

    def check_section(self, block_texts: Dict[int, List[str]], block_text_height: Dict[int, List[int]], config: Dict) -> Tuple[bool, int, int, float]:
        """Enhanced section checking with confidence scoring."""
        include_strings = config.get('include_strings', [])
        exclude_strings = config.get('exclude_strings', [])
        length_comparison = config.get('length_comparison', False)

        primary_keys = []
        page_stats = []
        num_pages = len(block_texts)
        confidence_scores = []

        # Use enhanced pattern matching if enabled
        if self.enable_multi_variant and not self.use_legacy_mode:
            all_texts = []
            for page, text_list in block_texts.items():
                all_texts.extend(text_list)

            match_found, confidence, matched_patterns = self.enhanced_pattern_matching(
                all_texts, include_strings, exclude_strings)

            if match_found:
                # Find the page where the best match was found
                best_page = 1
                for page, text_list in block_texts.items():
                    for text in text_list:
                        text_compare = text.strip() if self.case_sensitive_matching else text.strip().lower()
                        for pattern in matched_patterns:
                            pattern_compare = pattern if self.case_sensitive_matching else pattern.lower()
                            if pattern_compare in text_compare:
                                best_page = page
                                break
                        if best_page != 1:
                            break
                    if best_page != 1:
                        break

                return True, best_page, num_pages, confidence
            else:
                return False, -1, num_pages, 0.0

        # Legacy pattern matching for backward compatibility
        for (page, text), (_, height) in zip(block_texts.items(), block_text_height.items()):
            text_height_list = list(zip(text, height))
            for idx, (s, h) in enumerate(text_height_list):
                if any(v.lower() in s.strip().lower() for v in include_strings):
                    if length_comparison and len(s.strip()) == len(include_strings[0].strip()):
                        if (len(text_height_list) > idx + 1):
                            s_next, _ = text_height_list[idx + 1]
                            if not any(ex_str in s_next.strip().lower() for ex_str in exclude_strings):
                                primary_keys.append([int(page), int(h), s.strip()])
                        else:
                            primary_keys.append([int(page), int(h), s.strip()])
                    elif any(v.lower() == s.strip().lower() for v in include_strings):
                        primary_keys.append([int(page), int(h), s.strip()])
                    page_stats.append([int(page), int(h), s.strip()])

        primary_keys = self.sort_list(primary_keys)
        page_stats = self.sort_list(page_stats)

        if self.debug:
            print(page_stats)
            print(primary_keys)

        # Calculate basic confidence for legacy mode
        confidence = 0.8 if primary_keys else (0.6 if page_stats else 0.0)

        if primary_keys:
            return True, primary_keys[0][0], num_pages, confidence
        elif page_stats:
            return True, page_stats[0][0], num_pages, confidence
        else:
            return False, -1, num_pages, 0.0

    def apply_big_font_clusterer(self, block_texts: Dict[int, List[str]], config: Dict) -> List[str]:
        """Enhanced big font clustering with improved data handling."""
        big_font_config = config.get('big_font', {})
        height_threshold = big_font_config.get('height_threshold', 0.5)
        num_clusters = big_font_config.get('num_clusters', 3)
        include_strings = big_font_config.get('include_strings', [])
        exclude_strings = big_font_config.get('exclude_strings', [])

        # For enhanced multi-variant mode, use simplified big font detection
        if self.enable_multi_variant and not self.use_legacy_mode:
            all_texts = []
            for page, text_list in block_texts.items():
                all_texts.extend(text_list)

            # Simple big font detection based on text patterns
            large_font_texts = []
            for text in all_texts:
                text_clean = text.strip()
                if not text_clean:
                    continue

                # Check if text matches include patterns
                text_compare = text_clean if self.case_sensitive_matching else text_clean.lower()

                for pattern in include_strings:
                    pattern_compare = pattern if self.case_sensitive_matching else pattern.lower()

                    if self.partial_string_matching:
                        match_found = pattern_compare in text_compare
                    else:
                        match_found = pattern_compare == text_compare

                    if match_found:
                        # Check exclude patterns
                        excluded = False
                        for exclude_pattern in exclude_strings:
                            exclude_compare = exclude_pattern if self.case_sensitive_matching else exclude_pattern.lower()
                            if exclude_compare in text_compare:
                                excluded = True
                                break

                        if not excluded and text_clean not in large_font_texts:
                            large_font_texts.append(text_clean)
                            break

            return large_font_texts

        # Legacy big font clustering (for backward compatibility)
        try:
            big_font_clusterer = BigFontClusterer(height_threshold=height_threshold, num_clusters=num_clusters)

            # Convert block_texts to expected format for BigFontClusterer
            # This is a simplified approach - in real usage, you'd need proper OCR data structure
            mock_ocr_data = {
                "COMPOSED_BLOCKS": []
            }

            # Create mock structure for BigFontClusterer
            for page, texts in block_texts.items():
                for text in texts:
                    mock_block = {
                        "TEXT_BLOCK": [{
                            "TEXT_LINE": [{
                                "HEIGHT": 12,  # Default height
                                "STRING": [{"text": text}]
                            }]
                        }]
                    }
                    mock_ocr_data["COMPOSED_BLOCKS"].append(mock_block)

            features = big_font_clusterer.extract_features(mock_ocr_data)
            labels = big_font_clusterer.cluster_text_lines(features)
            large_font_texts = big_font_clusterer.extract_large_font_text(mock_ocr_data, features, labels)

            return [text for text in large_font_texts if any(
                    inc.lower() in text.lower() for inc in include_strings
            ) and not any(
                    exc.lower() in text.lower() for exc in exclude_strings
            )]

        except Exception as e:
            if self.debug:
                print(f"BigFontClusterer failed, using fallback: {e}")

            # Fallback to simple pattern matching
            all_texts = []
            for page, text_list in block_texts.items():
                all_texts.extend(text_list)

            return [text for text in all_texts if any(
                    inc.lower() in text.lower() for inc in include_strings
            ) and not any(
                    exc.lower() in text.lower() for exc in exclude_strings
            )]

    def classify_document(self, block_texts: Dict[int, List[str]], block_text_height: Dict[int, List[int]]) -> Dict:
        """Enhanced multi-variant document classification with confidence scoring."""

        if self.use_legacy_mode:
            return self._legacy_classify_document(block_texts, block_text_height)

        if self.enable_multi_variant and self.enable_confidence_scoring:
            return self._classify_with_confidence_scoring(block_texts, block_text_height)
        else:
            return self._legacy_classify_document(block_texts, block_text_height)

    def _classify_with_confidence_scoring(self, block_texts: Dict[int, List[str]],
                                        block_text_height: Dict[int, List[int]]) -> Dict:
        """Enhanced classification with confidence scoring for multiple variants."""

        document_scores = {}
        classification_details = {}

        for doc_type, sections in self.config['document_types'].items():
            return_value = sections.get('return')
            section_scores = {}
            section_details = {}
            total_confidence = 0.0
            section_count = 0
            all_conditions_met = True

            for section_name, section_config in sections.items():
                if section_name == 'return':
                    continue

                method = getattr(self, section_name, None)
                if method:
                    section_count += 1

                    if section_name == 'big_font':
                        # Handle big font analysis
                        result = method(block_texts, section_config)
                        if isinstance(result, list) and result:
                            confidence = 0.8  # High confidence for big font matches
                            section_scores[section_name] = confidence
                            section_details[section_name] = {
                                'matched': True,
                                'confidence': confidence,
                                'large_font_texts': result
                            }
                            total_confidence += confidence
                        else:
                            all_conditions_met = False
                            section_scores[section_name] = 0.0
                            section_details[section_name] = {
                                'matched': False,
                                'confidence': 0.0,
                                'large_font_texts': []
                            }
                    else:
                        # Handle header, body, footer sections
                        result = method(block_texts, block_text_height, section_config)
                        if isinstance(result, tuple) and len(result) >= 4:
                            matched, page, total_pages, confidence = result
                            section_scores[section_name] = confidence
                            section_details[section_name] = {
                                'matched': matched,
                                'confidence': confidence,
                                'starting_page': page,
                                'total_pages': total_pages
                            }
                            if matched:
                                total_confidence += confidence
                            else:
                                all_conditions_met = False
                        else:
                            # Fallback for legacy tuple format
                            matched = result[0] if len(result) > 0 else False
                            confidence = 0.6 if matched else 0.0
                            section_scores[section_name] = confidence
                            section_details[section_name] = {
                                'matched': matched,
                                'confidence': confidence
                            }
                            if not matched:
                                all_conditions_met = False

            # Calculate average confidence
            avg_confidence = total_confidence / section_count if section_count > 0 else 0.0

            # Apply priority boost if specified
            if self.variant_priority_order and doc_type in self.variant_priority_order:
                priority_index = self.variant_priority_order.index(doc_type)
                priority_boost = (len(self.variant_priority_order) - priority_index) * 0.05
                avg_confidence = min(avg_confidence + priority_boost, 1.0)

            document_scores[doc_type] = avg_confidence
            classification_details[doc_type] = {
                'return_value': return_value,
                'confidence': avg_confidence,
                'all_conditions_met': all_conditions_met,
                'section_scores': section_scores,
                'section_details': section_details
            }

        # Find the best match
        if document_scores:
            best_doc_type = max(document_scores.items(), key=lambda x: x[1])
            best_confidence = best_doc_type[1]
            best_type_name = best_doc_type[0]

            if self.debug:
                print(f"Classification scores: {document_scores}")
                print(f"Best match: {best_type_name} with confidence {best_confidence:.2f}")

            if best_confidence >= self.confidence_threshold:
                best_details = classification_details[best_type_name]
                return {
                    'document_type': best_details['return_value'],
                    'variant_name': best_type_name,
                    'confidence': best_confidence,
                    'classification_status': 'success',
                    'all_variant_scores': document_scores,
                    'section_details': best_details['section_details'],
                    'starting_page': self._get_best_starting_page(best_details['section_details']),
                    'total_pages': len(block_texts)
                }

        # Return default if no good match found
        return {
            'document_type': self.config.get('default_return'),
            'variant_name': 'default',
            'confidence': 0.0,
            'classification_status': 'low_confidence',
            'all_variant_scores': document_scores,
            'section_details': {},
            'starting_page': -1,
            'total_pages': len(block_texts)
        }

    def _get_best_starting_page(self, section_details: Dict) -> int:
        """Extract the best starting page from section details."""
        for section_name in ['header', 'body', 'footer']:
            if section_name in section_details and section_details[section_name].get('matched'):
                return section_details[section_name].get('starting_page', 1)
        return 1

    def _legacy_classify_document(self, block_texts: Dict[int, List[str]], block_text_height: Dict[int, List[int]]) -> Dict:
        """Original classification logic for backward compatibility."""
        for doc_type, sections in self.config['document_types'].items():
            return_value = sections.get('return')
            all_conditions_met = True
            last_result = None

            for section_name, section_config in sections.items():
                if section_name == 'return':
                    continue
                method = getattr(self, section_name, None)
                if method:
                    if section_name == 'big_font':
                        result = method(block_texts, section_config)
                        if isinstance(result, list) and not result:
                            all_conditions_met = False
                            break
                        last_result = result
                    else:
                        result = method(block_texts, block_text_height, section_config)
                        # Handle both old and new tuple formats
                        matched = result[0] if len(result) > 0 else False
                        if not matched:
                            all_conditions_met = False
                            break
                        last_result = result

            if all_conditions_met:
                return {
                    'document_type': return_value,
                    'section': section_name,
                    'starting_page': last_result[1] if isinstance(last_result, tuple) and len(last_result) > 1 else None,
                    'total_pages': last_result[2] if isinstance(last_result, tuple) and len(last_result) > 2 else None,
                    'large_font_texts': last_result if isinstance(last_result, list) else None
                }

        return {
            'document_type': self.config.get('default_return'),
            'section': 'none',
            'starting_page': -1,
            'total_pages': len(block_texts)
        }

    def process_form(self, combined_json_path: str) -> Dict:
        upper_block_text, upper_block_text_height = self.get_upper_blocks_data_from_all_pages(combined_json_path)
        return self.classify_document(upper_block_text, upper_block_text_height)


# Usage example

# Usage example
if __name__ == '__main__':
    config = {
        "max_upper_block": 3,
        "use_upper_split_percentage": 0.45,
        "max_lines_for_header": 5,
        "max_upper_lines_for_key_search": 8,
        "debug": True,
        "default_return": "docvu_de_core/de_config/9000001-1003_de.json",
        "document_types": {
            "old_form": {
                "return": "docvu_de_core/de_config/9000001-1003_de.json",
                "header": {
                    "include_strings": ["section 1","section 2", "section3", "personal information"],
                    "exclude_strings": [],
                    "length_comparison": False
                },
                "body": {
                    "include_strings": ["section 1","section 2", "section3", "personal information"],
                    "exclude_strings": [],
                    "length_comparison": False
                }
            }
        }
    }

    cls = Classify(config)

    print("-" * 50)
    json_path = '/home/<USER>/mohith_data_management/GIT_CLONE/SOFTWARE-SERVICE-%20document_extraction/ocr_output/07_1003_Submission_0608_kofax_ocr/combined.json'
    result = cls.process_form(combined_json_path=json_path)
    print(result)