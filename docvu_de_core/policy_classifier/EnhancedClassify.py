import json
from typing import List, Tu<PERSON>, Dict, Optional
from docvu_de_core.page_info_parser.BigFontClusterer import BigFontClusterer


class EnhancedClassify:
    """
    Enhanced document classifier for handling 5-6 document variants with improved
    classification logic, confidence scoring, and better pattern matching.
    """
    
    def __init__(self, config):
        if isinstance(config, str):
            with open(config, 'r') as f:
                self.config = json.load(f)
        elif isinstance(config, dict):
            self.config = config

        # Configuration parameters
        self.max_upper_block = self.config.get('max_upper_block', 3)
        self.use_upper_split_percentage = self.config.get('use_upper_split_percentage', 0.45)
        self.max_lines_for_header = self.config.get('max_lines_for_header', 5)
        self.max_upper_lines_for_key_search = self.config.get('max_upper_lines_for_key_search', 8)
        self.debug = self.config.get('debug', True)
        self.confidence_threshold = self.config.get('confidence_threshold', 0.6)
        
        # Enhanced features
        self.enable_confidence_scoring = self.config.get('enable_confidence_scoring', True)
        self.case_sensitive = self.config.get('case_sensitive', False)
        self.partial_match = self.config.get('partial_match', True)

    def sort_list(self, l: List[Tuple]) -> List[Tuple]:
        def is_upper_case(s: str) -> bool:
            return s.isupper()
        sorted_list = sorted(l, key=lambda x: (not is_upper_case(x[2]), -x[1], x[0]))
        return sorted_list

    def get_upper_blocks_data_from_all_pages(self, merged_page_json: str) -> Tuple[
        Dict[int, List[str]], Dict[int, List[int]]]:
        """Extract text and height data from upper blocks of all pages."""
        with open(merged_page_json) as temp_f:
            merged_pages_dict = json.loads(temp_f.read())

        upper_pages = {}
        text_height = {}
        page_count = merged_pages_dict.keys()
        
        for page in page_count:
            block_text = []
            height = []
            page_json_data = merged_pages_dict[str(page)]['COMPOSED_BLOCKS'] if isinstance(
                merged_pages_dict[str(page)], dict) else merged_pages_dict[str(page)]
            
            for index, block_data in enumerate(page_json_data):
                img_height = int(block_data.get("IMAGE_HEIGHT", -1))
                max_upper_vpos_permissible = int(img_height * self.use_upper_split_percentage)
                
                if index > self.max_upper_block:
                    continue
                    
                for block in block_data["TEXT_BLOCK"]:
                    text_lines = block["TEXT_LINE"]
                    for lines in text_lines:
                        strings = lines["STRING"]
                        for string in strings:
                            if lines["VPOS"] <= max_upper_vpos_permissible:
                                block_text.append(string["text"])
                                height.append(string["HEIGHT"])
                            if len(block_text) >= self.max_lines_for_header:
                                break
                        if len(block_text) >= self.max_lines_for_header:
                            break
                    if len(block_text) >= self.max_lines_for_header:
                        break
                if len(block_text) >= self.max_lines_for_header:
                    break

            upper_pages[int(page)] = block_text
            text_height[int(page)] = height

        return upper_pages, text_height

    def enhanced_pattern_matching(self, text_content: List[str], patterns: List[str], 
                                 exclude_patterns: List[str] = None) -> Tuple[bool, float, List[str]]:
        """
        Enhanced pattern matching with confidence scoring and better matching logic.
        
        Returns:
            - match_found: Boolean indicating if patterns were found
            - confidence: Float between 0-1 indicating match confidence
            - matched_patterns: List of patterns that were matched
        """
        if exclude_patterns is None:
            exclude_patterns = []
            
        # Combine all text into a single string for analysis
        combined_text = " ".join(text_content)
        if not self.case_sensitive:
            combined_text = combined_text.lower()
            patterns = [p.lower() for p in patterns]
            exclude_patterns = [p.lower() for p in exclude_patterns]
        
        # Check for exclusion patterns first
        for exclude_pattern in exclude_patterns:
            if self.partial_match:
                if exclude_pattern in combined_text:
                    if self.debug:
                        print(f"Excluded by pattern: {exclude_pattern}")
                    return False, 0.0, []
            else:
                if exclude_pattern == combined_text.strip():
                    if self.debug:
                        print(f"Excluded by exact pattern: {exclude_pattern}")
                    return False, 0.0, []
        
        # Check for inclusion patterns
        matched_patterns = []
        total_patterns = len(patterns)
        
        if total_patterns == 0:
            return False, 0.0, []
        
        for pattern in patterns:
            if self.partial_match:
                if pattern in combined_text:
                    matched_patterns.append(pattern)
            else:
                if pattern == combined_text.strip():
                    matched_patterns.append(pattern)
        
        # Calculate confidence based on pattern matches
        match_ratio = len(matched_patterns) / total_patterns
        confidence = min(match_ratio * 1.2, 1.0)  # Boost confidence slightly, cap at 1.0
        
        # Bonus for multiple matches
        if len(matched_patterns) > 1:
            confidence = min(confidence + 0.1, 1.0)
        
        match_found = len(matched_patterns) > 0
        
        if self.debug and match_found:
            print(f"Matched patterns: {matched_patterns}, Confidence: {confidence:.2f}")
        
        return match_found, confidence, matched_patterns

    def classify_document_with_confidence(self, block_texts: Dict[int, List[str]], 
                                        block_text_heights: Dict[int, List[int]]) -> Dict:
        """
        Enhanced document classification with confidence scoring for multiple variants.
        """
        document_scores = {}
        classification_details = {}
        
        # Process each document type
        for doc_type, doc_config in self.config.get('document_types', {}).items():
            total_confidence = 0.0
            section_matches = {}
            
            if self.debug:
                print(f"\n--- Analyzing document type: {doc_type} ---")
            
            # Check each section (header, body, footer, big_font)
            for section_name, section_config in doc_config.items():
                if section_name == 'return':
                    continue
                    
                include_strings = section_config.get('include_strings', [])
                exclude_strings = section_config.get('exclude_strings', [])
                
                if section_name == 'header':
                    # Use header text (upper blocks)
                    all_header_text = []
                    for page_texts in block_texts.values():
                        all_header_text.extend(page_texts[:self.max_lines_for_header])
                    
                    match_found, confidence, matched_patterns = self.enhanced_pattern_matching(
                        all_header_text, include_strings, exclude_strings)
                    
                elif section_name == 'body':
                    # Use all text for body analysis
                    all_body_text = []
                    for page_texts in block_texts.values():
                        all_body_text.extend(page_texts)
                    
                    match_found, confidence, matched_patterns = self.enhanced_pattern_matching(
                        all_body_text, include_strings, exclude_strings)
                    
                elif section_name == 'big_font':
                    # Use big font detection
                    try:
                        big_font_clusterer = BigFontClusterer(
                            height_threshold=section_config.get('height_threshold', 0.6),
                            num_clusters=section_config.get('num_clusters', 3)
                        )
                        
                        all_big_fonts = []
                        for page_num, heights in block_text_heights.items():
                            if page_num in block_texts:
                                page_texts = block_texts[page_num]
                                big_fonts = big_font_clusterer.get_big_fonts(page_texts, heights)
                                all_big_fonts.extend(big_fonts)
                        
                        match_found, confidence, matched_patterns = self.enhanced_pattern_matching(
                            all_big_fonts, include_strings, exclude_strings)
                            
                    except Exception as e:
                        if self.debug:
                            print(f"Big font analysis failed: {e}")
                        match_found, confidence, matched_patterns = False, 0.0, []
                
                else:
                    # Handle other sections (footer, etc.)
                    all_text = []
                    for page_texts in block_texts.values():
                        all_text.extend(page_texts)
                    
                    match_found, confidence, matched_patterns = self.enhanced_pattern_matching(
                        all_text, include_strings, exclude_strings)
                
                # Store section results
                section_matches[section_name] = {
                    'match_found': match_found,
                    'confidence': confidence,
                    'matched_patterns': matched_patterns
                }
                
                if match_found:
                    total_confidence += confidence
                else:
                    # If any section fails to match, reduce overall confidence
                    total_confidence -= 0.2
            
            # Calculate final confidence for this document type
            num_sections = len([s for s in doc_config.keys() if s != 'return'])
            if num_sections > 0:
                avg_confidence = max(total_confidence / num_sections, 0.0)
            else:
                avg_confidence = 0.0
            
            document_scores[doc_type] = avg_confidence
            classification_details[doc_type] = {
                'confidence': avg_confidence,
                'section_matches': section_matches,
                'return_config': doc_config.get('return')
            }
            
            if self.debug:
                print(f"Document type '{doc_type}' final confidence: {avg_confidence:.2f}")
        
        # Find best match
        if document_scores:
            best_doc_type = max(document_scores, key=document_scores.get)
            best_confidence = document_scores[best_doc_type]
            
            if best_confidence >= self.confidence_threshold:
                result = {
                    'document_type': classification_details[best_doc_type]['return_config'],
                    'classified_as': best_doc_type,
                    'confidence': best_confidence,
                    'classification_details': classification_details[best_doc_type],
                    'all_scores': document_scores
                }
                
                if self.debug:
                    print(f"\n=== CLASSIFICATION RESULT ===")
                    print(f"Best match: {best_doc_type} (confidence: {best_confidence:.2f})")
                    print(f"Return config: {result['document_type']}")
                
                return result
        
        # Fallback to default
        if self.debug:
            print(f"\n=== FALLBACK TO DEFAULT ===")
            print(f"No document type met confidence threshold ({self.confidence_threshold})")
            print(f"Best score was: {max(document_scores.values()) if document_scores else 0.0:.2f}")
        
        return {
            'document_type': self.config.get('default_return'),
            'classified_as': 'default',
            'confidence': 0.0,
            'classification_details': {},
            'all_scores': document_scores
        }

    def classify_document(self, block_texts: Dict[int, List[str]], 
                         block_text_heights: Dict[int, List[int]]) -> Dict:
        """
        Main classification method - uses enhanced classification if enabled,
        otherwise falls back to original logic.
        """
        if self.enable_confidence_scoring:
            return self.classify_document_with_confidence(block_texts, block_text_heights)
        else:
            # Original classification logic for backward compatibility
            return self._original_classify_document(block_texts, block_text_heights)

    def _original_classify_document(self, block_texts: Dict[int, List[str]], 
                                  block_text_heights: Dict[int, List[int]]) -> Dict:
        """Original classification logic for backward compatibility."""
        # Implementation of original logic would go here
        # For now, just use the enhanced version
        return self.classify_document_with_confidence(block_texts, block_text_heights)

    def process_form(self, combined_json_path: str) -> Dict:
        """Process a document form and return classification results."""
        upper_block_text, upper_block_text_height = self.get_upper_blocks_data_from_all_pages(combined_json_path)
        return self.classify_document(upper_block_text, upper_block_text_height)


# Usage example for testing
if __name__ == '__main__':
    # Example configuration for 6 document variants
    config = {
        "debug": True,
        "confidence_threshold": 0.6,
        "enable_confidence_scoring": True,
        "case_sensitive": False,
        "partial_match": True,
        "default_return": "docvu_de_core/de_config/default_de.json",
        "document_types": {
            "1003_application": {
                "return": "docvu_de_core/de_config/1003_application_de.json",
                "header": {
                    "include_strings": ["1003", "Application", "UNIFORM RESIDENTIAL LOAN APPLICATION"],
                    "exclude_strings": ["Closing Disclosure", "ACH Authorization"]
                },
                "body": {
                    "include_strings": ["Borrower", "Property Address", "Loan Amount"],
                    "exclude_strings": []
                }
            },
            "ach_authorization": {
                "return": "docvu_de_core/de_config/ach_authorization_de.json",
                "header": {
                    "include_strings": ["Sign Up For Autopay", "ACH Authorization", "Spring EQ"],
                    "exclude_strings": ["1003", "Closing Disclosure"]
                },
                "body": {
                    "include_strings": ["Routing Number", "Account Number", "authorize"],
                    "exclude_strings": []
                }
            },
            "closing_disclosure": {
                "return": "docvu_de_core/de_config/closing_disclosure_de.json",
                "header": {
                    "include_strings": ["Closing Disclosure", "final loan terms"],
                    "exclude_strings": ["1003", "ACH Authorization"]
                },
                "body": {
                    "include_strings": ["Loan Terms", "Closing Information", "Costs at Closing"],
                    "exclude_strings": []
                }
            }
        }
    }
    
    classifier = EnhancedClassify(config)
    
    # Test with sample file
    test_file = 'ocr_output/ACH_Authorization_Form_(1)_textract_ocr/combined.json'
    try:
        result = classifier.process_form(test_file)
        print(f"\nFinal Result: {result}")
    except Exception as e:
        print(f"Error processing file: {e}")
