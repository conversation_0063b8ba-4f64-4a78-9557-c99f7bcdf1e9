import json
import numpy as np
import os

class ClassifyOld:
    def __init__(self):
        self.max_upper_block = 3
        self.use_upper_split_percentage = 0.45
        self.max_lines_for_header = 5
        self.short_form_key = ["SHORT FORM"]
        self.long_form_key = ["SCHEDULE A", "SCHEDULEA","SCHEDULE -A", "Loan Policy of Title Insurance Schedule A",
                              "loan policy schedule a", "OWNER'S SCHEDULE A"]
        self.owner_form_key = ["SCHEDULE A", "SCHEDULEA", "SCHEDULE -A"]
        self.junior_policy_key = ["Junior Loan Policy"]
        self.unknown_form_key = ["EXHIBIT I"]
        self.max_upper_lines_for_key_search = 8
        self.debug = True

    def sort_list(self, l):
        # Helper function to check if a string is all upper case
        def is_upper_case(s):
            return s.isupper()

        # Sorting the list based on the given criteria with height in descending order
        sorted_list = sorted(l, key=lambda x: (not is_upper_case(x[2]), -x[1], x[0]))
        return sorted_list

    def sort_list_with_tolerance(self, l, height_tolerance=0):
        # Helper function to check if a string is all upper case
        def is_upper_case(s):
            return s.isupper()

        # Helper function to determine if two heights are within tolerance
        def height_key(height):
            if height_tolerance > 0:
                return -(height // (height_tolerance + 1))
            return -height

        # Sorting the list based on the given criteria with height in descending order
        sorted_list = sorted(l, key=lambda x: (not is_upper_case(x[2]), height_key(x[1]), x[1], x[0]))
        return sorted_list


    def get_upper_blocks_data_from_all_pages(self, merged_page_json):
        with open(merged_page_json) as temp_f:
            merged_pages_dict = json.loads(temp_f.read())

        upper_pages = {}
        text_height = {}
        page_count = merged_pages_dict.keys()
        for page in page_count:
            block_text = []
            height = []
            found_end = False
            page_json_data = merged_pages_dict[str(page)]['COMPOSED_BLOCKS'] if isinstance(merged_pages_dict[str(page)], dict) else merged_pages_dict[str(page)]
            for index, block_data in enumerate(page_json_data):
                img_height = int(block_data["IMAGE_HEIGHT"]) if "IMAGE_HEIGHT" in block_data.keys() else -1
                max_upper_vpos_permissible = int(img_height * self.use_upper_split_percentage)
                # print("max_upper_vpos_permissible: ", max_upper_vpos_permissible)
                if index > self.max_upper_block:
                    continue
                for block in block_data["TEXT_BLOCK"]:
                    text_lines = block["TEXT_LINE"]
                    for lines in text_lines:
                        strings = lines["STRING"]
                        for string in strings:
                            if img_height != -1 and int(string["VPOS"]) > max_upper_vpos_permissible:
                                continue
                            if found_end:
                                break
                            #print(string["VPOS"], string["text"])
                            block_text.append(string["text"])
                            height.append(string["HEIGHT"])
                        if found_end:
                            break
                    if found_end:
                        break
                if found_end:
                    break
            if found_end:
                break
            upper_pages[page] = block_text
            text_height[page] = height
        return upper_pages, text_height

    def is_short_form(self, block_texts, block_text_height):
        it_is_short_form = False
        page_has_short_form_key = []
        page_search_start_num = -1
        num_pages = len(block_texts)
        for (page, text), (_, h) in zip(block_texts.items(), block_text_height.items()):
            match_found = [s for s in text for v in self.short_form_key if v in s.strip()]
            if match_found:
                it_is_short_form = True
                page_has_short_form_key.append(int(page))
                page_search_start_num = min(page_has_short_form_key)
        # print("page_has_short_form_key: ", page_has_short_form_key)
        return [it_is_short_form, page_search_start_num, num_pages]

    def is_long_form(self, block_texts, block_text_height):
        page_stats = []
        primary_keys = []
        secondary_keys = []
        num_pages = len(block_texts)
        height_all_pages = [xss for i, xss in block_text_height.items()]
        height_list = [x for xs in height_all_pages for x in xs]

        for (page, text), (_, height) in zip(block_texts.items(), block_text_height.items()):
            line_no = 0
            text_height_list = list(zip(text, height))
            for idx, [s, h] in enumerate(text_height_list):
                line_no += 1
                for v in self.long_form_key:
                    index = s.strip().lower().find(v.strip().lower())
                    if v.lower() in s.strip().lower():
                        page_stats.append([int(page), int(h), s.strip()])
                        if self.debug:
                            print("Page: {}, Line NUmber: {}, Key_Height: {}".format(page, line_no, h))
                        # print("Page Stats: Found on LIne No. {}".format(line_no))
                        if index == 0:
                            secondary_keys.append([int(page), int(h), s.strip()])
                            # print("Secondary Key: Found on LIne No. {}".format(line_no))
                        if (((len(s.strip()) == len(v.strip())) or (v.strip().upper() in s.strip())) and
                                line_no <= self.max_upper_lines_for_key_search and len(s.strip()) - len(v.strip()) < len(v.strip())//2):
                                ## check if the length of the string is not too high
                            if (len(text_height_list) > idx+1): ## append it to primary list only if continued is not
                                                                ## followed
                                s_next, h_next = text_height_list[idx+1]
                                if (not ("continued".strip().lower() in s_next.strip().lower())) \
                                        and (not ("exhibit a".strip().lower() in s_next.strip().lower())):
                                    primary_keys.append([int(page), int(h), s.strip()])
                            else:
                                primary_keys.append([int(page), int(h), s.strip()])
                            # print("Primary Key: Found on LIne No. {}".format(line_no))

        primary_keys = self.sort_list(primary_keys)
        ## from sorted_list again sorting based on page number to avoid de_start page issue
        primary_keys = sorted(primary_keys, key=lambda x: (x[0]))
        page_stats = self.sort_list(page_stats)
        secondary_keys = self.sort_list(secondary_keys)
        if self.debug:
            print(page_stats)
            print(primary_keys)
            print(secondary_keys)

        all_h = []
        all_p = []
        if primary_keys:
            for p_key in primary_keys:
                p, h, key = p_key
                if key.isupper():
                    return p, num_pages
                else:
                    all_h.append(h)
                    all_p.append(p)
            max_h_index = all_h.index(max(all_h))
            return all_p[max_h_index], num_pages
        elif secondary_keys:
            for p_key in secondary_keys:
                p, h, key = p_key
                if key.isupper():
                    return p, num_pages
                else:
                    all_h.append(h)
                    all_p.append(p)
            max_h_index = all_h.index(max(all_h))
            return all_p[max_h_index], num_pages
        elif page_stats:
            for p_key in page_stats:
                p, h, key = p_key
                if key.isupper():
                    return p, num_pages
                else:
                    all_h.append(h)
                    all_p.append(p)
            max_h_index = all_h.index(max(all_h))
            return all_p[max_h_index], num_pages
        return -1, num_pages

    def is_unknown_form(self, block_texts, block_text_height):
        page_has_long_form_key = []
        page_search_start_num = -1
        num_pages = len(block_texts)
        height_all_pages = [xss for i, xss in block_text_height.items()]
        height_list = [x for xs in height_all_pages for x in xs]
        avg_h = np.average(height_list)
        for (page, text), (_, h) in zip(block_texts.items(), block_text_height.items()):
            match_found = [s for s in text for v in self.unknown_form_key if s.strip().lower() in v.lower()]
            match_found_index = [i for i, s in enumerate(text)
                                 for v in self.unknown_form_key if s.strip().lower() in v.lower()]

            avg_h_page = np.average(h)
            if match_found: # and h[match_found_index[0]] >= avg_h:
            #if match_found: # and h[match_found_index[0]] >= int(avg_h) and h[match_found_index[0]] >= avg_h_page:

                if self.debug:
                    print("Found : on page {}, avg height: {}, text is {}, text height {}"
                        .format(page, avg_h, text[match_found_index[0]], h[match_found_index[0]]))

                page_has_long_form_key.append(int(page))
                page_search_start_num = min(page_has_long_form_key)
        return page_search_start_num, num_pages

    def is_owner_form(self, block_texts, block_text_height):
        page_stats = []
        primary_keys = []
        secondary_keys = []
        num_pages = len(block_texts)
        height_all_pages = [xss for i, xss in block_text_height.items()]
        height_list = [x for xs in height_all_pages for x in xs]
        for (page, text), (_, height) in zip(block_texts.items(), block_text_height.items()):
            for s, h in zip(text, height):
                for v in self.owner_form_key:
                    index = s.strip().lower().find(v.strip().lower())
                    if v.lower() in s.strip().lower():
                        page_stats.append([int(page), int(h), s.strip()])
                        if index == 0:
                            secondary_keys.append([int(page), int(h), s.strip()])
                        if len(s.strip()) == len(v.strip()):
                            primary_keys.append([int(page), int(h), s.strip()])

        primary_keys = sorted(primary_keys, key=lambda x: (x[0]))
        page_stats = sorted(page_stats, key=lambda x: (x[0]))
        secondary_keys = sorted(secondary_keys, key=lambda x: (x[0]))
        # print(page_stats)
        # print(primary_keys)
        # print(secondary_keys)

        all_h = []
        all_p = []
        if primary_keys:
            for p_key in primary_keys:
                p, h, key = p_key
                if key.isupper():
                    return p, num_pages
                else:
                    all_h.append(h)
                    all_p.append(p)
            max_h_index = all_h.index(max(all_h))
            return all_p[max_h_index], num_pages
        elif secondary_keys:
            for p_key in secondary_keys:
                p, h, key = p_key
                if key.isupper():
                    return p, num_pages
                else:
                    all_h.append(h)
                    all_p.append(p)
            max_h_index = all_h.index(max(all_h))
            return all_p[max_h_index], num_pages
        elif page_stats:
            for p_key in page_stats:
                p, h, key = p_key
                if key.isupper():
                    return p, num_pages
                else:
                    all_h.append(h)
                    all_p.append(p)
            max_h_index = all_h.index(max(all_h))
            return all_p[max_h_index], num_pages
        return -1, num_pages

    def check_form_variants(self, upper_block_text, upper_block_text_height, is_owner=False):
        if is_owner:
            search_start_page, num_pages = self.is_owner_form(upper_block_text, upper_block_text_height)
            if self.debug:
                print("Owner Form")
            return ['owner_form', search_start_page, num_pages]
        else:
            short_form, search_start_page, num_pages = (
                self.is_short_form(upper_block_text, upper_block_text_height))
            if short_form:
                if self.debug:
                    print("Short Form")
                return ['short_form', search_start_page, num_pages]
            else:
                search_start_page, num_pages = (
                    self.is_long_form(upper_block_text, upper_block_text_height))
                if search_start_page != -1:
                    if self.debug:
                        print("Long Form")
                    return ['long_form', search_start_page, num_pages]
                else:
                    search_start_page, num_pages = (
                        self.is_unknown_form(upper_block_text, upper_block_text_height))
                    if search_start_page != -1:
                        if self.debug:
                            print("Exhibit Form")
                        return ['misc', search_start_page, num_pages]
                    else:
                        if self.debug:
                            print("None Form")
                        return ['misc', search_start_page, num_pages]

    def process_form(self, combined_json_path, is_owner=False):
        upper_block_text, upper_block_text_height = (
            self.get_upper_blocks_data_from_all_pages(combined_json_path))
        return self.check_form_variants(upper_block_text, upper_block_text_height, is_owner=is_owner)


if __name__ == '__main__':
    cls = ClassifyOld()

    print("-"*50)
    # json_path = '/Users/<USER>/Downloads/DE_SLS/SOFTWARE-SERVICE-%20document_extraction/ocr_output/03-1990_4126_K12_7896'
    json_path = "/Users/<USER>/Downloads/sls_de/SOFTWARE-SERVICE-%20document_extraction/ocr_output/1471672.1.402.2031644103_1"
    combined_json_path = os.path.join(json_path, 'combined.json')
    print(cls.process_form(combined_json_path))