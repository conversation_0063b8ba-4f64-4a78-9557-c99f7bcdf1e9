from docvu_de_core.policy_classifier import *
from collections import Counter


class VersionHist:
    def __init__(self, xml_save_dir):
        self.input_folder = xml_save_dir
        self.classify_policy = Classify()
        pass

    def get_version_hist(self, is_owner=False):
        de_hist = {}
        de_start_page = {}
        de_num_page = {}
        for dirpath, directories, files in os.walk(self.input_folder):
            for dirs in directories:
                dirname = os.path.join(dirpath, dirs)
                json_path = os.path.join(dirname, 'combined.json')
                isFile = os.path.isfile(json_path)
                if isFile:
                    form_type, search_start_page, num_pages = (
                        self.classify_policy.process_form(json_path, is_owner=is_owner))
                    if form_type == 'misc':
                        print("none_form: ", json_path)
                    if form_type == 'misc':
                        print("exhibit_form: ", json_path)
                    if form_type in de_hist.keys():
                        de_hist[form_type] += 1
                        de_start_page[form_type].append(search_start_page)
                        de_num_page[form_type].append(num_pages)
                    else:
                        de_hist[form_type] = 1
                        de_start_page[form_type] = [search_start_page]
                        de_num_page[form_type] = [num_pages]
        for k, v in de_start_page.items():
            de_start_page[k] = Counter(v)
        for k, v in de_num_page.items():
            de_num_page[k] = Counter(v)
        return de_hist, de_start_page, de_num_page


if __name__ == "__main__":
    logger.info("Running Locally")
    xml_save_dir = '../../ocr_json_files/pytesseract_ocr_xml/pytesseract_lender_ocr_xml'
    #xml_save_dir = './ocr_json_files/kofax_ocr_xml/kofax_lender_ocr_xml'
    local_test = VersionHist(xml_save_dir)
    hist = local_test.get_version_hist(is_owner=False)
    print(hist)

