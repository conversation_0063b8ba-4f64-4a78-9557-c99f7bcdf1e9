from docvu_de_core.search.direction_based_search.BaseSearchOption import BaseSearchOption
from docvu_de_core.io import <PERSON><PERSON>eyFieldsOutputFormat
from docvu_de_core.search import <PERSON><PERSON>ey<PERSON>ieldHelper
from docvu_de_core.utils import logger


class UpDirectionSearch(BaseSearchOption):
    def __init__(self, debug=False):
        super().__init__()
        self.helper = SearchKeyFieldHelper(logger)
        self.debug = debug

    def get_value_in_up_direction(self, blocks_data, tl_id, ts_id, found_field_data, field_info, field_key):
        possible_answer_list = []
        secondary_possible_answer_list = []
        found_value = False
        # Continue searching the data from the place where key was found
        for blocks in blocks_data:
            for tl, text_line in enumerate(blocks["TEXT_LINE"]):
                for ts, text_string in enumerate(text_line["STRING"]):
                    # for the value consider everything in the line.
                    if (text_string["text"].lower() == found_field_data["text"].lower() and
                            tl == tl_id and ts == ts_id):
                        found_value = True
                        break
                    else:
                        if self.helper.dist_utils.is_center_on_vertical_line(found_field_data, text_string):
                            if ("probable_place" in field_info.keys() and
                                    field_info["probable_place"] == "just_above"):
                                if text_string:
                                    possible_answer_list.append(text_string)
                        if text_string:
                            secondary_possible_answer_list.append(text_string)
                if found_value:
                    break
            if found_value:
                break
        if self.debug:
            print("possible_answer_list: ", possible_answer_list)
        if self.debug:
            print("secondary_possible_answer_list :", secondary_possible_answer_list)
        if secondary_possible_answer_list:
            left_zone, right_zone = self.helper.dist_utils.get_zones(secondary_possible_answer_list)
            if self.debug:
                print("Left ZOne = {}\nRight Zone = {}".format(left_zone, right_zone))
            if "additional_info" in field_info.keys() and "has_keyword" in field_info["additional_info"].keys():
                has_keyword = field_info["additional_info"]["has_keyword"]
                found_match = False
                if left_zone:
                    ltext = " ".join(v["text"] for v in left_zone)
                    if self.helper.process_utils.get_match(has_keyword, ltext):
                        found_match = True
                if found_match:
                    secondary_possible_answer_list = left_zone
                else:
                    if right_zone:
                        rtext = " ".join(v["text"] for v in right_zone)
                        if self.helper.process_utils.get_match(has_keyword, rtext):
                            found_match = True
                    if found_match:
                        secondary_possible_answer_list = right_zone
                    else:
                        secondary_possible_answer_list = left_zone
            else:
                secondary_possible_answer_list = left_zone

        if secondary_possible_answer_list:
            if "include_key" in field_info.keys() and field_info["include_key"]:
                if "with_keyword" in field_info.keys():
                    final_split, final_elements = self.helper.process_utils.split_list_based_on_delimiter(
                                                                secondary_possible_answer_list, '\n')
                    for i, (fs, fe) in enumerate(zip(final_split, final_elements)):
                        # Find the first match where any keyword is present in the split part, case-insensitively
                        match = [idx for idx, keyword in enumerate(field_info["with_keyword"]) if keyword.lower() in fs.lower()]
                        if match:
                            # Join parts of final_split starting from the matched part and also collect corresponding elements
                            concatenated_string = " ".join(final_split[i:])
                            corresponding_elements = [elem for sublist in final_elements[i:] for elem in sublist]
                            # return " ".join(final_split[match[0]::])
                            bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(corresponding_elements)
                            return SearchKeyFieldsOutputFormat(value=concatenated_string,
                                                               search_next_page=False,
                                                               success=True,
                                                               elements=corresponding_elements,
                                                               is_table=False,
                                                               line_number=line_number,
                                                               bbox=bbox,
                                                               field_key=field_key)

        if possible_answer_list:
            if "multi_line_value" in field_info.keys():
                if field_info["multi_line_value"]:
                    # return " ".join(v["text"] for v in possible_answer_list)
                    concatenated_string = " ".join(v["text"] for v in possible_answer_list)
                    bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(possible_answer_list)
                    return SearchKeyFieldsOutputFormat(value=concatenated_string,
                                                       search_next_page=False,
                                                       success=True,
                                                       elements=possible_answer_list,
                                                       is_table=False,
                                                       line_number=line_number,
                                                       bbox=bbox,
                                                       field_key=field_key)
            # return possible_answer_list[-1]
            concatenated_string = possible_answer_list[-1]['text']
            return SearchKeyFieldsOutputFormat(value=concatenated_string,
                                               search_next_page=False,
                                               success=True,
                                               elements=possible_answer_list[-1],
                                               is_table=False,
                                               line_number=possible_answer_list[-1]['LINE_NUMBER'],
                                               bbox=[possible_answer_list[-1]['HPOS'],
                                                            possible_answer_list[-1]['VPOS'],
                                                            possible_answer_list[-1]['END_HPOS'],
                                                            possible_answer_list[-1]['END_VPOS']],
                                               field_key=field_key)
        elif secondary_possible_answer_list:
            # Sorting the all the value before to get the nearest value
            sorted_answer_list = sorted(secondary_possible_answer_list,
                                        key=lambda x: self.helper.dist_utils.get_distance(found_field_data, x))
            if "multi_line_value" in field_info.keys():
                sorted_answer_list = sorted(sorted_answer_list, key=lambda d: (d["VPOS"], d["HPOS"]))
                if field_info["multi_line_value"]:
                    all_vals = " ".join(v["text"] for v in sorted_answer_list)
                    # print("all_vals: ", all_vals)
                    f_index = self.helper.process_utils.find_index_using_identifiers(field_info["end_identifier"], all_vals)
                    if f_index != -1:
                        temp_field_value = all_vals[:f_index]
                    else:
                        temp_field_value = all_vals
                    # Extract the contributing elements up to the identified index
                    length_accumulated = 0
                    contributing_elements = []
                    for v in sorted_answer_list:
                        length_accumulated += len(v["text"]) + 1  # Adding 1 for the space
                        contributing_elements.append(v)
                        if length_accumulated >= f_index:
                            break
                    # return " ".join(v["text"] for v in sorted_answer_list)
                    # return temp_field_value
                    bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(contributing_elements)
                    return SearchKeyFieldsOutputFormat(value=temp_field_value,
                                                       search_next_page=False,
                                                       success=True,
                                                       elements=contributing_elements,
                                                       is_table=False,
                                                       line_number=line_number,
                                                       bbox=bbox,
                                                       field_key=field_key)
            # return sorted_answer_list[0]
            concatenated_string = sorted_answer_list[0]['text']
            return SearchKeyFieldsOutputFormat(value=concatenated_string,
                                               search_next_page=False,
                                               success=True,
                                               elements=sorted_answer_list[0],
                                               is_table=False,
                                               line_number=sorted_answer_list[0]['LINE_NUMBER'],
                                               bbox=[sorted_answer_list[0]['HPOS'],
                                                            sorted_answer_list[0]['VPOS'],
                                                            sorted_answer_list[0]['END_HPOS'],
                                                            sorted_answer_list[0]['END_VPOS']],
                                               field_key=field_key)
        else:
            return SearchKeyFieldsOutputFormat(value=None,
                                               search_next_page=False,
                                               success=False,
                                               elements=None,
                                               is_table=False,
                                               line_number=None,
                                               bbox=None,
                                               field_key=field_key)