from docvu_de_core.search.direction_based_search.BaseSearchOption import Base<PERSON>earchOption
from docvu_de_core.io import <PERSON><PERSON><PERSON><PERSON>ieldsOutputFormat
from docvu_de_core.search import <PERSON><PERSON>ey<PERSON>ieldHelper
from docvu_de_core.utils import logger


class DownTillEndSearch(BaseSearchOption):
    def __init__(self, debug=False):
        super().__init__()
        self.helper = SearchKeyFieldHelper(logger)
        self.debug = debug

    # def search(self, data, *args, **kwargs):
    #     text_block = data.get("text_block")
    #     extraction_item = data.get("extraction_item")
    #     field_key = data.get("field_key")
    #     return self.get_all_values_down_till_end(text_block, extraction_item, field_key)

    def get_all_values_down_till_end(self,
                                     text_block,
                                     extraction_item,
                                     field_key):
        str_text = ""
        key_to_start = -1
        key_to_end = -1
        for i1, block in enumerate(text_block):
            for i2, text_line in enumerate(block["TEXT_LINE"]):
                for i3, string in enumerate(text_line["STRING"]):
                    str_text = string["text"]
                    if key_to_start == -1:
                        found_key = self.helper.process_utils.get_match(extraction_item["key"], str_text)
                        if found_key:
                            key_to_start = i1 + 1
                    if key_to_end == -1:
                        found_key = self.helper.process_utils.get_match(extraction_item["end_identifier"], str_text)
                        if found_key:
                            key_to_end = i1

        # print('Start Key {}, End key {}'.format(key_to_start, key_to_end))
        final = []
        if key_to_end == 0:
            key_to_end = len(text_block)
        for i in range(key_to_start, key_to_end):
            text_line = text_block[i]["TEXT_LINE"]
            str = ""
            for line in text_line:
                for texts in line["STRING"]:
                    str += texts["text"]
            # print(str)
            final.append(str)

        sorted_list = self.helper.process_utils.sort_vpos_block_string(text_block[key_to_start:key_to_end])

        # return sorted_list
        value = self.helper.process_utils.get_string_from_elements(sorted_list)
        bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(sorted_list)
        return SearchKeyFieldsOutputFormat(value=value,
                                           search_next_page=False,
                                           success=True,
                                           elements=sorted_list,
                                           is_table=False,
                                           line_number=line_number,
                                           bbox=bbox,
                                           field_key=field_key)

