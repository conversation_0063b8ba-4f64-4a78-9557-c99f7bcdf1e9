from docvu_de_core.search.direction_based_search.BaseSearchOption import Base<PERSON>earchOption
from docvu_de_core.io import OutputFormat
from docvu_de_core.search import Search<PERSON>ey<PERSON>ieldHelper
from docvu_de_core.utils import logger
from docvu_de_core.search.direction_based_search.WholeValueDownSearch import WholeValueDownSearch


class WholeValueDownInlineSearch(BaseSearchOption):

    def __init__(self, debug=False):
        super().__init__()
        self.helper = SearchKeyFieldHelper(logger)
        self.wholevaluedownsearch = WholeValueDownSearch()
        self.debug = debug

    def get_whole_value_down_inline(self, whole_page_data, found_tl_id, found_ts_id, found_field_data, field_info,
                                    field_key):
        """
        Extract values directly below a specified key in the document structure.

        :param whole_page_data: The entire structured data of the page.
        :param found_tl_id: Text line ID where the key was found.
        :param found_ts_id: Text string ID where the key was found.
        :param found_field_data: Data of the found field to use as reference.
        :param field_info: Information about the field, including filters and conditions.
        :param field_key: The key field information, including position and dimensions.
        :return: OutputFormat object with extracted values and relevant metadata.
        """

        # Call the original get_whole_value_down function to get initial results
        initial_output = self.wholevaluedownsearch.get_whole_value_down(
                whole_page_data, found_tl_id, found_ts_id, found_field_data, field_info, field_key
        )

        # If the initial extraction was successful, filter the results
        if initial_output.success:
            filtered_elements = []

            # Iterate over the extracted elements and filter those directly below the field_key
            for element in initial_output.elements:
                if self.helper.dist_utils.is_value_down_inline(field_key, element):
                    filtered_elements.append(element)

            if filtered_elements:
                # Prepare the final output with filtered elements
                bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(filtered_elements)
                value = self.helper.process_utils.get_string_from_elements(filtered_elements)

                return OutputFormat(
                        value=value,
                        search_next_page=False,
                        success=True,
                        elements=filtered_elements,
                        is_table=False,
                        line_number=line_number,
                        bbox=bbox,
                        field_key=field_key,
                        is_text_merged=False
                )

        # If no valid elements are found, get the closest down inline element
        closest_element = None
        min_distance = float('inf')
        
        if initial_output.elements:
            for element in initial_output.elements:
                vertical_distance = element['VPOS'] - field_key['VPOS']
                if vertical_distance > 0 and vertical_distance < min_distance:
                    min_distance = vertical_distance
                    closest_element = element

        if closest_element:
            # Prepare the final output with the closest down inline element
            bbox, line_number = self.helper.process_utils.get_bbox_and_line_number([closest_element])
            value = self.helper.process_utils.get_string_from_elements([closest_element])

            return OutputFormat(
                    value=value,
                    search_next_page=False,
                    success=True,
                    elements=[closest_element],
                    is_table=False,
                    line_number=line_number,
                    bbox=bbox,
                    field_key=field_key,
                    is_text_merged=False
            )

        # If no valid elements are found or the initial extraction failed, return failure
        return OutputFormat(
                value=None,
                search_next_page=False,
                success=False,
                elements=None,
                is_table=False,
                line_number=None,
                bbox=None,
                field_key=field_key,
                is_text_merged=None
        )
