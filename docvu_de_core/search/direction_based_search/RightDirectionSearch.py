from docvu_de_core.search.direction_based_search.BaseSearchOption import BaseSearchOption
from docvu_de_core.io import <PERSON><PERSON>ey<PERSON>ieldsOutputFormat
from docvu_de_core.utils import ElementUtils, logger
from docvu_de_core.search.SearchKeyFieldHelper import SearchKeyFieldHelper


class RightDirectionSearch(BaseSearchOption):
    def __init__(self, debug=False,):
        super().__init__()
        self.helper = SearchKeyFieldHelper(logger)
        self.debug = debug

    def search_down_in_block(self, block_data, curr_block_id, extraction_item):
        # block_data, curr_block_id, extraction_item = item["TEXT_BLOCK"], tb_id, extraction_item
        search_block_limit_upto = 1
        if "search_block_limit_upto" in extraction_item.keys():
            search_block_limit_upto = extraction_item["search_block_limit_upto"]
        curr_block = block_data[curr_block_id]
        next_block = block_data[curr_block_id + 1: curr_block_id + search_block_limit_upto + 1]
        text_line_string = ""
        test_line_elements = []

        last_str_in_curr_block = curr_block["TEXT_LINE"][-1]["STRING"][-1]
        for nb in next_block:
            sorted_block_vpos = (sorted(nb["TEXT_LINE"], key=lambda d: int(d['VPOS'])))
            sorted_block_vpos = self.helper.process_utils.rearrange_based_on_vpos_hpos(sorted_block_vpos)
            for sorted_block in sorted_block_vpos:
                for str_in_next_block in sorted_block["STRING"]:
                    # str_in_next_block = sorted_block["STRING"][0]
                    if self.helper.process_utils.if_nearby_vpos(last_str_in_curr_block, str_in_next_block):
                        text_line_string += str_in_next_block['text']
                        test_line_elements.append(str_in_next_block)
                        last_str_in_curr_block = str_in_next_block

        if self.helper.process_utils.get_match(extraction_item["end_identifier"], text_line_string):
            identifier_found_index = (
                self.helper.process_utils.find_index_using_identifiers(
                    extraction_item["end_identifier"], text_line_string))
            element_start_index, element_end_index = ElementUtils.find_element_indices_from_string_indices(
                                                                                            test_line_elements,
                                                                                             0,
                                                                                             identifier_found_index)
            # Check if there is any text before end_identifier
            if identifier_found_index != -1:
                bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(test_line_elements)
                # return text_line_string[:identifier_found_index]
                return SearchKeyFieldsOutputFormat(value= text_line_string[:identifier_found_index],
                                                   search_next_page=True,
                                                   success=True,
                                                   elements=test_line_elements[:element_end_index],
                                                   is_table=False,
                                                   line_number=line_number,
                                                   bbox=bbox,
                                                   field_key=None)

        bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(test_line_elements)
        # return text_line_string
        return SearchKeyFieldsOutputFormat(value=text_line_string,
                                           search_next_page=False,
                                           success=True,
                                           elements=test_line_elements,
                                           is_table=False,
                                           line_number=line_number,
                                           bbox=bbox,
                                           field_key=None)

    def get_remaining_search_text_for_individual_key(self, extraction_item,
                                                     text_line_string,
                                                     item_text_block,
                                                     tb_id, tl_id, ts_id,
                                                     multiline = True,):
        all_elements = [
            text_string
            for idx, block in enumerate(item_text_block) if idx < tb_id
            for tl, text_line in enumerate(block["TEXT_LINE"]) if tl < tl_id
            for ts, text_string in enumerate(text_line["STRING"]) if ts < ts_id
        ]
        if self.debug:
            print('item_text_block =', item_text_block)
        if "probable_place" in extraction_item.keys() and extraction_item["probable_place"].lower() in ["individual", "strict_individual"]:
            possible_answer_list = []
            block_text_list = []
            found_value = False
            found_end = False
            found_tl = -1
            found_tb = -1
            found_ts = -1
            found_k = False
            key_end_idx = None
            for tb, block in enumerate(item_text_block):
                if tb < tb_id:
                    continue
                if found_end:
                    break
                for tl, text_line in enumerate(block["TEXT_LINE"]):
                    if tb <= tb_id and tl < tl_id:
                        continue
                    if found_end:
                        break
                    if found_value and not multiline:
                        if ("additional_info" in extraction_item.keys() and "find_in_zone" in
                                extraction_item["additional_info"].keys() and
                                extraction_item["additional_info"]["find_in_zone"]):
                            # print("here")
                            lzone, rzone = self.helper.dist_utils.get_zones(possible_answer_list)
                            # print(f"Lzone = {lzone}\nRzone = {rzone}")
                            found_match = False
                            if lzone:
                                ltext = " ".join(v["text"] for v in lzone)
                                if self.helper.process_utils.get_match(extraction_item["key"], ltext):
                                    found_match = True
                            if found_match:
                                secondary_possible_answer_list = lzone
                            else:
                                if rzone:
                                    rtext = " ".join(v["text"] for v in rzone)
                                    if self.helper.process_utils.get_match(extraction_item["key"], rtext):
                                        found_match = True
                                if found_match:
                                    secondary_possible_answer_list = rzone
                                else:
                                    secondary_possible_answer_list = lzone
                            text_line_string_new = " ".join(v["text"].strip() for v in secondary_possible_answer_list) \
                                if secondary_possible_answer_list else text_line_string
                        else:
                            text_line_string_new = " ".join(v["text"].strip() for v in possible_answer_list) \
                                if possible_answer_list else text_line_string
                        if self.debug:
                            print(1, 'text_line_string_new =', text_line_string_new)
                        elements_new = possible_answer_list if possible_answer_list else all_elements
                        bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(elements_new)
                        # return text_line_string_new
                        return SearchKeyFieldsOutputFormat(value=text_line_string_new,
                                                       search_next_page=False,
                                                       success=True,
                                                       elements=elements_new,
                                                       is_table=False,
                                                       line_number=line_number,
                                                       bbox=bbox,
                                                       field_key=None)
                    for ts, text_string in enumerate(text_line["STRING"]):
                        if tb <= tb_id and tl <= tl_id and ts < ts_id:
                            continue
                        block_text_list.append(text_string["text"])
                        if not found_value and not found_k:
                            found_k, key_end_idx, found_k_str = self.helper.process_utils.check_if_key_found_is_individual(
                                    block_text_list, extraction_item["key"], return_found_key=True)
                            if self.debug:
                                print(1, 'found value')
                            if found_k:
                                text_string = ElementUtils.remove_from_beginning(text_string, found_k_str)
                                found_value = True
                                found_tl = tl
                                found_ts = ts
                                found_tb = tb
                        if found_value:
                            if text_string:
                                possible_answer_list.append(text_string)
                            found_end_identifier, pos_info = self.helper.process_utils.check_end_identifiers(text_string,
                                                                                                      extraction_item[
                                                                                                          "end_identifier"])
                            if self.debug:
                                print('found_end_identifier =', found_end_identifier, pos_info)
                            if found_end_identifier and pos_info[0] != pos_info[1]:
                                if self.debug:
                                    print(1, 'found_end')
                                found_end = True
                                last_element = ElementUtils.filter_elements_by_string_indices(
                                        [possible_answer_list[-1]], 0, pos_info[0])
                                possible_answer_list = possible_answer_list[:-1]
                                if last_element and pos_info[0] != 0:
                                    possible_answer_list.append(last_element[0])
                            if not multiline and not extraction_item.get("consider_complete_line", True):
                                if self.helper.process_utils.get_remaining_text(" ".join(v["text"].strip() for v in possible_answer_list).strip(), extraction_item)[0]:
                                    if self.debug:
                                        print('consider_complete_line is Flase, so exiting.', " ".join(v["text"].strip() for v in possible_answer_list).strip())
                                        print(2, 'found_end')
                                    found_end = True
                            if found_end:
                                break
                    if found_end:
                        break
                    if (found_value and "multi_line_value" in extraction_item.keys() \
                        and not extraction_item["multi_line_value"]):
                        if self.debug:
                            print(3, 'found_end')
                        found_end = True
                if found_end:
                    break
            text_line_string_new = " ".join(v["text"].strip() for v in possible_answer_list)
            # return text_line_string_new
            elements_new = possible_answer_list
            bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(elements_new)
            return SearchKeyFieldsOutputFormat(value=text_line_string_new,
                                               search_next_page=False,
                                               success=True,
                                               elements=elements_new,
                                               is_table=False,
                                               line_number=line_number,
                                               bbox=bbox,
                                               field_key=None)

        text_line_string_new = text_line_string
        elements_new = all_elements
        bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(elements_new)
        return SearchKeyFieldsOutputFormat(value=text_line_string_new,
                                           search_next_page=False,
                                           success=False,
                                           elements=elements_new,
                                           is_table=False,
                                           line_number=line_number,
                                           bbox=bbox,
                                           field_key=None)

    def get_remaining_search_text_for_non_individual_key(
            self, extraction_item,
            text_line_string,
            item_text_block,
            tb_id, tl_id, ts_id,
            multiline=True, ):
        
        all_elements = [
            text_string
            for idx, block in enumerate(item_text_block) if idx >= tb_id
            for tl, text_line in enumerate(block["TEXT_LINE"]) if idx > tb_id and tl >= tl_id
            for ts, text_string in enumerate(text_line["STRING"]) if idx > tb_id and tl >= tl_id and ts >= ts_id
        ]
        found_tl = None
        
        if self.debug:
            print('item_text_block =', item_text_block)
        
        if extraction_item.get("probable_place", "").lower() not in ["individual", "strict_individual"]:
            possible_answer_list = []
            block_text_list = []
            found_value = False
            found_end = False
            found_tl = -1
            found_tb = -1
            found_ts = -1
            found_k = False
            key_end_idx = None
            
            for tb, block in enumerate(item_text_block):
                if tb < tb_id:
                    continue
                if found_end:
                    break
                for tl, text_line in enumerate(block["TEXT_LINE"]):
                    if tb <= tb_id and tl < tl_id:
                        continue
                    if found_end:
                        break
                    if found_value and not multiline:
                        if ("additional_info" in extraction_item.keys() and "find_in_zone" in
                                extraction_item["additional_info"].keys() and
                                extraction_item["additional_info"]["find_in_zone"]):
                            lzone, rzone = self.helper.dist_utils.get_zones(possible_answer_list)
                            found_match = False
                            
                            if lzone:
                                ltext = " ".join(v["text"] for v in lzone)
                                if self.helper.process_utils.get_match(extraction_item["key"], ltext):
                                    found_match = True
                            
                            if found_match:
                                secondary_possible_answer_list = lzone
                            else:
                                if rzone:
                                    rtext = " ".join(v["text"] for v in rzone)
                                    if self.helper.process_utils.get_match(extraction_item["key"], rtext):
                                        found_match = True
                            
                                if found_match:
                                    secondary_possible_answer_list = rzone
                                else:
                                    secondary_possible_answer_list = lzone
                            
                            text_line_string_new = " ".join(v["text"].strip() for v in secondary_possible_answer_list) \
                                if secondary_possible_answer_list else text_line_string
                        else:
                            text_line_string_new = " ".join(v["text"].strip() for v in possible_answer_list) \
                                if possible_answer_list else text_line_string

                        # text_line_string_new = " ".join(v["text"].strip() for v in possible_answer_list) \
                        #                                    if possible_answer_list else text_line_string
                        
                        if self.debug:
                            print(1, 'text_line_string_new =', text_line_string_new)
                        
                        elements_new = possible_answer_list if possible_answer_list else all_elements
                        bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(elements_new)
                        # return text_line_string_new
                        
                        return SearchKeyFieldsOutputFormat(
                            value=text_line_string_new,
                            search_next_page=False,
                            success=True,
                            elements=elements_new,
                            is_table=False,
                            line_number=line_number,
                            bbox=bbox,
                            field_key=None
                        )
                    
                    for ts, text_string in enumerate(text_line["STRING"]):
                        if tb <= tb_id and tl <= tl_id and ts < ts_id:
                            continue
                        block_text_list.append(text_string["text"])
                        # for the value consider everything in the line.
                        
                        if not found_value and not found_k:
                            found_k, key_end_idx = self.helper.process_utils.check_if_key_found_is_present(
                                block_text_list, extraction_item["key"])
                            
                            if self.debug:
                                print(1, 'found value')
                            
                            if found_k:
                                found_value = True
                                text_string = ElementUtils.filter_elements_by_string_indices(
                                    text_string, key_end_idx, -1)
                                found_tl = tl
                                found_ts = ts
                                found_tb = tb
                        
                        if found_value:
                            if text_string:
                                possible_answer_list.append(text_string)

                            found_end_identifier, pos_info = self.helper.process_utils.check_end_identifiers(
                                text_string, extraction_item["end_identifier"])
                        
                            if self.debug:
                                print('found_end_identifier =', found_end_identifier, pos_info)
                        
                            if found_end_identifier and pos_info[0] != pos_info[1]:
                                if self.debug:
                                    print(1, 'found_end')
                                
                                found_end = True
                                last_element = ElementUtils.filter_elements_by_string_indices(
                                        [possible_answer_list[-1]], 0, pos_info[0])
                                possible_answer_list = possible_answer_list[:-1]
                                
                                if last_element and pos_info[0] != 0 and last_element:
                                    possible_answer_list.append(last_element[0])
                            
                            if not multiline and not extraction_item.get("consider_complete_line", True):
                                remaining_text, start_idx, end_idx = self.helper.process_utils.get_remaining_text(
                                    " ".join(v["text"].strip() for v in possible_answer_list).strip(), extraction_item)
                                if remaining_text:
                                    if self.debug:
                                        print('consider_complete_line is Flase, so exiting.', " ".join(v["text"].strip() for v in possible_answer_list).strip())
                                        print(2, 'found_end')
                                    
                                    possible_answer_list = ElementUtils.filter_elements_by_string_indices(possible_answer_list, start_idx, end_idx)
                                    found_end = True
                            
                            if found_end:
                                break
                    if found_end:
                        break
                    
                    if (found_value and "multi_line_value" in extraction_item.keys() \
                        and not extraction_item["multi_line_value"]):
                        if self.debug:
                            print(3, 'found_end')
                        found_end = True
                
                if found_end:
                    break
            text_line_string_new = " ".join(v["text"].strip() for v in possible_answer_list)
            remaining_text, start_idx, end_idx = self.helper.process_utils.get_remaining_text(text_line_string_new, extraction_item)
            
            if remaining_text:
                if self.debug:
                    print('consider_complete_line is Flase, so exiting.',
                          " ".join(v["text"].strip() for v in possible_answer_list).strip())
                    print(2, 'found_end')
                possible_answer_list = ElementUtils.filter_elements_by_string_indices(possible_answer_list, start_idx,
                                                                                      end_idx)
            # return text_line_string_new
            elements_new = possible_answer_list
            text_line_string_new = " ".join(v["text"].strip() for v in possible_answer_list)
            bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(elements_new)
            return SearchKeyFieldsOutputFormat(
                value=text_line_string_new,
                search_next_page=False,
                success=True,
                elements=elements_new,
                is_table=False,
                line_number=line_number,
                bbox=bbox,
                field_key=None
            )

        text_line_string_new = text_line_string
        elements_new = all_elements
        bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(elements_new)
        
        return SearchKeyFieldsOutputFormat(
            value=text_line_string_new,
            search_next_page=False,
            success=False,
            elements=elements_new,
            is_table=False,
            line_number=line_number,
            bbox=bbox,
            field_key=None)

    def get_value_in_right_direction_new(self, extraction_item, text_line_string,
                                         text_string, item_text_block, tb_id, tl_id, ts_id):
        if self.debug:
            print("text_line_string: ", text_line_string)
        
        if "probable_place" in extraction_item.keys() and \
            extraction_item["probable_place"].lower() in ["individual", "strict_individual"]:
            output = self.get_remaining_search_text_for_individual_key(
                extraction_item,
                text_line_string,
                item_text_block,
                tb_id, tl_id, ts_id,
                extraction_item.get("multi_line_value")
            )
        else:
            output = self.get_remaining_search_text_for_non_individual_key(
                extraction_item,
                text_line_string,
                item_text_block,
                tb_id, tl_id, ts_id,
                extraction_item.get("multi_line_value")
            )
            
        new_text_line_string = output.value
        new_text_line_elements = output.elements
        line_number = output.line_number
        bbox = output.bbox
        
        if self.debug:
            print("1. New text_line_string for Individual Key: ", new_text_line_string)
        
        temp_field_value, remaining_string_start_index, remaining_string_end_index = self.helper.process_utils.get_remaining_text(
            new_text_line_string,
            extraction_item,
            use_key=False
        )

        if self.debug:
            print("Temp field value: ", temp_field_value)
        
        if temp_field_value is not None:
            filtered_temp_value = ''
            splitted_val = temp_field_value.split('\n')
            for v in splitted_val:
                if v.strip() == ':' or len(v.strip()) <= 1:
                    continue
                else:
                    filtered_temp_value += v + '\n'
            temp_field_value = filtered_temp_value

            if "multi_line_value" in extraction_item.keys() and extraction_item["multi_line_value"]:
                if type(temp_field_value) is list:
                    temp_field_value = " ".join(v for v in temp_field_value)
            else:
                temp_field_value = temp_field_value.split('\n')[0]
            # print("Temp field value: ", temp_field_value)
            if self.helper.process_utils.get_match(extraction_item["end_identifier"], text_string["text"]):
                new_elements = ElementUtils.filter_elements_by_string_indices(
                    new_text_line_elements,
                    remaining_string_start_index,
                    remaining_string_end_index
                )
                temp_field_value = ' '.join([ne['text'] for ne in new_elements])
                bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(new_elements)
                return SearchKeyFieldsOutputFormat(
                    value=temp_field_value,
                    search_next_page=False,
                    success=False,
                    elements=new_elements,
                    is_table=False,
                    line_number=line_number,
                    bbox=bbox,
                    field_key=text_string
                )
        else:
            if self.debug:
                print("Searching down in block")
            output = self.search_down_in_block(item_text_block, tb_id, extraction_item)
            temp_field_value = output.value
            if temp_field_value == "":
                temp_field_value = None
            # return text_string, temp_field_value
            
            return SearchKeyFieldsOutputFormat(
                value=temp_field_value,
                search_next_page=output.search_next_page,
                success=True,
                elements=output.elements if temp_field_value == "" else None,
                is_table=False,
                line_number=output.line_number if temp_field_value == "" else None,
                bbox=output.bbox if temp_field_value == "" else None,
                field_key=text_string
            )

        if remaining_string_start_index != -1:
            new_elements = ElementUtils.filter_elements_by_string_indices(
                output.elements,
                remaining_string_start_index,
                remaining_string_end_index
            )
            temp_field_value = ' '.join([ne['text'] for ne in new_elements])
            bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(new_elements)
            
            return SearchKeyFieldsOutputFormat(
                value=temp_field_value,
                search_next_page=output.search_next_page,
                success=True,
                elements=new_elements,
                is_table=False,
                line_number=line_number,
                bbox=bbox,
                field_key=text_string
            )

        else:
            bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(new_text_line_elements)
            temp_field_value = ' '.join([ne['text'] for ne in new_text_line_elements])
            
            return SearchKeyFieldsOutputFormat(
                value=temp_field_value,
                search_next_page=output.search_next_page,
                success=True,
                elements=new_text_line_elements,
                is_table=False,
                line_number=line_number,
                bbox=bbox,
                field_key=text_string
            )
