from docvu_de_core.search.direction_based_search.BaseSearchOption import Base<PERSON>earchOption
from docvu_de_core.search import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>elper
from docvu_de_core.utils import logger


class DownDirectionSearch(BaseSearchOption):
    def __init__(self, debug=False):
        super().__init__()
        self.helper = SearchKeyFieldHelper(logger)
        self.debug = debug

    def get_value_from_key_if_exists(self, extraction_item, text_line_string):
        if self.debug:
            print(extraction_item)
            print(text_line_string)
        temp_field_value, remaining_string_start_index, remaining_string_end_index = \
                                                self.helper.process_utils.get_remaining_text(text_line_string["text"],
                                                                 extraction_item, use_start_identifier=True)
        if temp_field_value:
            temp_field_value1 = self.helper.process_utils.get_remaining_text_from_start_identifier(temp_field_value,
                                                                                            extraction_item)
            return temp_field_value1
        return temp_field_value