from docvu_de_core.search.direction_based_search.BaseSearchOption import BaseSearchOption
from docvu_de_core.search import <PERSON><PERSON><PERSON><PERSON>ieldHelper
from docvu_de_core.utils import logger


class LeftDirectionSearch(BaseSearchOption):
    def __init__(self, debug=False):
        super().__init__()
        self.helper = SearchKeyFieldHelper(logger)
        self.debug = debug

    @staticmethod
    def get_value_in_left_direction(text_string, field_info):
        for ed in field_info["end_identifier"]:
            splitted_str = text_string.split(ed)
            if len(splitted_str) > 1:
                if 'No'.lower() in splitted_str[-1].lower():
                    return "No"
                else:
                    return "Yes"
        return "No"

