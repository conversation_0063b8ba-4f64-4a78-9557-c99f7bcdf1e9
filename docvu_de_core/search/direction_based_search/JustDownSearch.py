from docvu_de_core.search.direction_based_search.BaseSearchOption import Base<PERSON>earchOption
from docvu_de_core.io import <PERSON><PERSON>ey<PERSON>ieldsOutputFormat
from docvu_de_core.search import <PERSON><PERSON>ey<PERSON>ieldHelper
from docvu_de_core.utils import logger


class JustDownSearch(BaseSearchOption):
    def __init__(self, debug=False):
        super().__init__()          # placeholder for future use
        self.helper = SearchKeyFieldHelper(logger)
        self.debug = debug

    def get_just_down_value(self, whole_page_data, found_tl_id, found_ts_id, found_field_data,
                            field_info, field_key = None):
        possible_answer_list = []
        possible_answer_list_elements = []
        consider_data = False
        found_end = False
        # Continue searching the data from the place where key was found
        for i, item in enumerate(whole_page_data):
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        if (text_string["text"].lower() == found_field_data["text"].lower() and
                                found_tl_id == tl_id and found_ts_id == ts_id):
                            consider_data = True
                            if not ("include_key" in field_info.keys() and field_info["include_key"]):
                                continue
                        if consider_data:
                            if self.helper.dist_utils.is_value_just_down(found_field_data, text_string, threshold=40):
                                if text_string:
                                    possible_answer_list.append(text_string)
                            if (possible_answer_list and
                                    self.helper.process_utils.get_match(field_info["end_identifier"], text_string["text"])):
                                found_end = True
                        if found_end:
                            break
                    if found_end:
                        break
                if found_end:
                    break
            if found_end:
                break
        if self.debug:
            print("get_just_down_value | Possible Answer List in DOWN: ", possible_answer_list)
        if possible_answer_list:
            if "multi_line_value" in field_info.keys() and field_info["multi_line_value"]:
                # return possible_answer_list
                elements = possible_answer_list
                bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(elements)
                return SearchKeyFieldsOutputFormat(value=self.helper.process_utils.get_string_from_elements(elements),
                                                   search_next_page=False,
                                                   success=True,
                                                   elements=elements,
                                                   is_table=False,
                                                   line_number=line_number,
                                                   bbox=bbox,
                                                   field_key=field_key)
            else:
                answer_list = [possible_answer_list[0]]
                i = 1
                while "consider_next_line_on_delimiter" in field_info and \
                        field_info['consider_next_line_on_delimiter'] and \
                        any(answer_list[-1]['text'].endswith(d) for d in [',', ';', ':']):
                    if len(possible_answer_list) <= i:
                        break
                    answer_list.append(possible_answer_list[i])
                    i += 1
                bbox, line_number = self.helper.process_utils.get_bbox_and_line_number(answer_list)
                value = ''
                for fl in answer_list:
                    value += fl['text'] + " "
                value = value.strip()
                return SearchKeyFieldsOutputFormat(value=value,
                                                   search_next_page=False,
                                                   success=True,
                                                   elements=answer_list,
                                                   is_table=False,
                                                   line_number=line_number,
                                                   bbox=bbox,
                                                   field_key=field_key)
        return SearchKeyFieldsOutputFormat(value=None,
                                           search_next_page=False,
                                           success=False,
                                           elements=None,
                                           is_table=False,
                                           line_number=None,
                                           bbox=None,
                                           field_key=field_key)