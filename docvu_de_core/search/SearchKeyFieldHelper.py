from docvu_de_core.modules import SplitByCheckedBox
from docvu_de_core.utils import DistanceUtils, ProcessUtils, GeneralUtils
from docvu_de_core.config import *

class SearchKeyFieldHelper:
    dist_utils = DistanceUtils()
    process_utils = ProcessUtils()
    cb = None
    general_utils = GeneralUtils()
    img_shape = None
    extra_padded_vpos_hpos = None
    debug = True

    def __init__(self, logger):
        pass

    def update_image_dim(self, img_shape):
        SearchKeyFieldHelper.img_shape = img_shape

    def is_in_footer(self, text_string):
        img_height, img_width, _ = self.img_shape
        text_hpos, text_vpos = int(text_string["HPOS"]), int(text_string["VPOS"])
        footer_starts = int(float(img_height) * float(page_bound_configs["footer_starts"]))

        if text_vpos >= footer_starts:
            return True
        return False

    def is_in_header(self, text_string):
        img_height, img_width, _ = self.img_shape
        text_hpos, text_vpos = int(text_string["HPOS"]), int(text_string["VPOS"])
        header_ends = int(float(img_height) * float(page_bound_configs["header_ends"]))
        tolerance = 100
        vpos_decision_boundary = header_ends + tolerance
        if self.extra_padded_vpos_hpos:
            shifted_vpos = self.extra_padded_vpos_hpos[0]
            vpos_decision_boundary = header_ends + shifted_vpos
        if text_vpos <= vpos_decision_boundary:
            return True
        return False

    def is_on_page_boundary(self, text_string):
        img_height, img_width, _ = self.img_shape
        text_hpos, text_vpos = int(text_string["HPOS"]), int(text_string["VPOS"])
        header_ends = int(float(img_height) * float(page_bound_configs["header_ratio"]))
        if text_vpos < header_ends:
            return True
        return False

    def check_if_matching_header_config(self, extraction_item,
                                        block_text_list, tb_id, tl_id, block_index, up_block, page_json_data):
        if ('probable_type' in extraction_item.keys() and
                extraction_item["probable_type"].lower() == 'header'):

            if ("probable_place" in extraction_item.keys() and
                    extraction_item["probable_place"].lower() == 'individual'):
                check_if_individual = (
                    self.process_utils.check_if_key_found_is_individual(
                        block_text_list, extraction_item["key"]))[0]
                if not check_if_individual:
                    return True, up_block
                else:
                    if ((extraction_item["type"] in ["number", "text", "date", "address"] and
                         extraction_item["direction"] == "up") and len(block_text_list) <= 1
                            and block_index > 0):
                        if self.debug:
                            print("Appending Block")
                        # item["TEXT_BLOCK"] = refined_page_json_data[i-1]["TEXT_BLOCK"]
                        up_block.insert(len(up_block) - 1, page_json_data[block_index - 1]["TEXT_BLOCK"])
                    return False, up_block
        return False, up_block

