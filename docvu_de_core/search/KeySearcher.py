from copy import deepcopy
from docvu_de_core.io import OutputFormat
from docvu_de_core.search.SearchKeyFieldHelper import Search<PERSON>eyFieldHelper
from docvu_de_core.search.StringMatcher import StringMatcher


class KeySearcher:
    """
    A class to search for specific key fields within OCR data or elements using a fuzzy matching approach
    and additional checks like footer, header, and individual handling.
    """

    def __init__(self, logger, min_threshold=70, max_threshold=90, length_threshold=5, debug=False):
        """
        Initialize the KeySearcher with adaptive fuzzy matching parameters.

        Parameters:
        logger (logging.Logger): Logger instance for logging.
        min_threshold (int): Minimum threshold for long strings.
        max_threshold (int): Maximum threshold for short strings.
        length_threshold (int): The string length at which the threshold starts to decrease.
        debug (bool): If True, print debug information during the search.
        """
        self.matcher = StringMatcher(min_threshold, max_threshold, length_threshold)
        self.debug = debug
        self.search_key_field_helper = SearchKeyFieldHelper(logger)
        self.search_key_field_helper.update_image_dim((2000, 2500, 3))

    def search_key(self, extraction_item, refined_page_json_data, prev_cb_id=0, prev_tb_id=0, prev_tl_id=0):
        """
        Search for a specific key field within the OCR data.

        Parameters:
        extraction_item (dict): The extraction item containing metadata about what to extract.
        refined_page_json_data (list): The OCR data parsed into JSON format.

        Returns:
        OutputFormat: A formatted output containing found_key, key_text, up_block, text_line_string, and other relevant information.
        """
        refined_page_json_data = deepcopy(refined_page_json_data)
        keys_list = extraction_item.get("key", [])
        use_partial_matching = extraction_item.get("use_partial_matching", {}).get("key", True)
        check_individual = ("probable_place" in extraction_item.keys() and
                            extraction_item["probable_place"].lower() == 'individual')
        in_footer = False
        include_footer = False
        up_block = []

        # Iterate through all the data until a match to the required field key is found
        for block_index, item in enumerate(refined_page_json_data):
            if block_index < prev_cb_id:
                continue
            for tb_id, blocks in enumerate(item["TEXT_BLOCK"]):
                if tb_id < prev_tb_id:
                    continue
                up_block.append(item["TEXT_BLOCK"])  # Keep track of the blocks

                # Iterate over each text line in the block
                for tl_id, text_line in enumerate(blocks["TEXT_LINE"]):
                    if tl_id <= prev_tl_id:
                        continue
                    same_line_str = []
                    same_line_element = []

                    for same_line in blocks["TEXT_LINE"]:
                        # Handle footer and zones logic
                        if in_footer and ("additional_info" in extraction_item.keys() and
                                          "find_in_zone" in extraction_item["additional_info"].keys() and
                                          extraction_item["additional_info"]["find_in_zone"]):
                            left_zone, right_zone = self.dist_utils.get_zones(same_line["STRING"])
                            string = " ".join(v["text"] for v in left_zone)
                            same_line_str.append(string)
                            same_line_element.extend(left_zone)
                        else:
                            string = " ".join(v["text"] for v in same_line["STRING"])
                            same_line_str.append(string)
                            same_line_element.extend(same_line)

                    # Combine strings for the text line
                    text_line_string = "\n".join(v for v in same_line_str)

                    for ts_id, text_string in enumerate(text_line["STRING"]):
                        # Handle specific footer inclusion conditions
                        if ("additional_info" in extraction_item.keys() and
                                "alternate_locations" in extraction_item["additional_info"].keys() and
                                extraction_item["additional_info"]["alternate_locations"] == "in_footer"):
                            include_footer = True

                        if self.search_key_field_helper.is_in_footer(text_string):
                            in_footer = True

                        if ("probable_place" in extraction_item.keys() and
                                extraction_item["probable_place"] == "in_footer"):
                            if in_footer:
                                include_footer = True
                            else:
                                continue

                        if not include_footer and in_footer:
                            continue

                        # Process block text list and check if matching header config is needed
                        block_text_list = [text_string["text"]]

                        to_continue, up_block = self.search_key_field_helper.check_if_matching_header_config(
                            extraction_item, block_text_list, tb_id, tl_id, block_index, up_block, refined_page_json_data
                        )
                        if to_continue:
                            continue

                        # Perform the matching using the StringMatcher
                        is_matched, matched_key = self.matcher.match_key(
                            text_string["text"], keys_list, use_partial_matching=use_partial_matching, check_individual=check_individual
                        )

                        if is_matched:
                            if self.debug:
                                print(f"Match found: {text_string['text']} in block {tb_id}, line {tl_id}")

                            # Header specific handling
                            if ("probable_type" in extraction_item.keys() and
                                    extraction_item["probable_type"].lower() == 'header' and
                                    not self.search_key_field_helper.is_in_header(text_string)):
                                continue

                            # Return the formatted output, encapsulating all relevant data
                            return OutputFormat(
                                found_key=True,
                                key_text=matched_key,
                                text_line=text_line,
                                text_line_string=text_line_string,
                                cb_id=block_index,
                                tb_id=tb_id,
                                tl_id=tl_id,
                                up_block=up_block
                            )

        if self.debug:
            print("No match found.")
        return OutputFormat(
            found_key=False,
            key_text=None,
            text_line=None,
            text_line_string=None,
            cb_id=None,
            tb_id=None,
            tl_id=None,
            up_block=up_block  # Return None if no match is found, along with up_block
        )

    def search_key_from_elements(self, extraction_item, element_list, prev_tl_id=0):
        """
        Search for a specific key field within a list of elements.

        Parameters:
        extraction_item (dict): The extraction item containing metadata about what to extract.
        element_list (list): List of elements, where each element is a dictionary containing 'text', 'HPOS', 'VPOS', 'WIDTH', 'HEIGHT'.

        Returns:
        OutputFormat: A formatted output containing found_key, key_text, and other relevant information.
        """
        keys_list = extraction_item.get("key", [])
        use_partial_matching = extraction_item.get("use_partial_matching", {}).get("key", True)
        check_individual = ("probable_place" in extraction_item.keys() and
                            extraction_item["probable_place"].lower() == 'individual')
        up_block = []

        # Iterate through the list of elements
        for index, element in enumerate(element_list):
            if index <= prev_tl_id:
                continue

            text = element["text"]

            # Perform the matching using the StringMatcher
            is_matched, matched_key = self.matcher.match_key(
                text, keys_list, use_partial_matching=use_partial_matching, check_individual=check_individual
            )

            if is_matched:
                if self.debug:
                    print(f"Match found: {text} in element {index}")

                # Return the formatted output, encapsulating all relevant data
                return OutputFormat(
                    found_key=True,
                    key_text=matched_key,
                    text_line=element,
                    text_line_string=text,
                    cb_id=None,  # Not applicable for elements
                    tb_id=None,  # Not applicable for elements
                    tl_id=index,  # Use index as tl_id for element
                    up_block=up_block  # Returning up_block
                )

        if self.debug:
            print("No match found in elements.")
        return OutputFormat(
            found_key=False,
            key_text=None,
            text_line=None,
            text_line_string=None,
            cb_id=None,
            tb_id=None,
            tl_id=None,
            up_block=up_block  # Return None if no match is found in elements
        )