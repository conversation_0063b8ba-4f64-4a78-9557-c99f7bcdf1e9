import os
parentDirectory = os.path.abspath(os.path.join(os.getcwd(), os.pardir))

import sys
from pathlib import Path  # path tricks so we can import wherever the module is
sys.path.append(os.path.abspath(Path(os.path.dirname(__file__)) / Path("../..")))
_sourcepath = os.path.abspath(Path(os.path.dirname(__file__)) / Path(''))
import fileinput
from datetime import date, datetime
from azure.storage.blob import BlobServiceClient
from docvu_de_core.config import azure_storage_connection_string, model_blob_container_name, blob_name_vm
import yaml
import time
import logging

# Setting up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class VersionManager:
    def __init__(self):
        self.version_file = os.path.join(os.path.dirname(__file__), '_version.py')
        self.container_name = model_blob_container_name
        self.blob_name = blob_name_vm
        self.connection_string = azure_storage_connection_string
        self.todays_date = date.today()
        self.todays_date_str = self.todays_date.strftime("%Y.%m.%d")
        self.current_version = self.fetch_version_from_blob()  # Initial fetch

    def load_config(self, config_file):
        with open(config_file, 'r') as file:
            return yaml.safe_load(file)['blob']

    def fetch_version_from_blob(self):
        """ Fetch the version directly from the blob after wheel generation """
        blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
        blob_client = blob_service_client.get_blob_client(container=self.container_name, blob=self.blob_name)
        version_data = blob_client.download_blob().readall().decode('utf-8')
        for line in version_data.splitlines():
            if line.startswith("__version__"):
                return line.split('=')[1].strip().replace("'", "")
        return '0.0.0.0'  # Default if no version found

    def save_version_locally(self):
        """ Save the current version to a local file (_version.py) """
        try:
            # Step 1: Fetch the current version from the blob
            self.current_version = self.fetch_version_from_blob()

            # Step 2: Increment the version as required
            new_version = self.get_version()  # This will increment the version to the next one

            # Step 3: Update the version in the blob
            self.update_version_in_blob(new_version)

            # Step 4: Now that the version in the blob has been updated, save the new version locally
            version_content = f"__version__ = '{new_version}'\n__all__ = ['__version__']\n"
            with open(self.version_file, 'w') as file:
                file.write(version_content)

        except Exception as e:
            print(f"Failed to save version locally: {e}")


    @staticmethod
    def split_version(current_version):
        return current_version.split('.')

    def get_version_str(self, current_version, index):
        return self.split_version(current_version)[index]

    def get_version_date(self, current_version):
        date_time_str = ".".join(s for s in self.split_version(current_version)[0:-1])
        date_time_obj = datetime.strptime(date_time_str, "%Y.%m.%d")
        return date_time_obj.date()

    def check_if_date_changed(self, current_version):
        version_date = self.get_version_date(current_version)
        if version_date < self.todays_date:
            return True
        return False

    def get_version(self):
        patch = self.get_version_str(self.current_version, 3)
        patch = 1 if self.check_if_date_changed(self.current_version) else int(patch) + 1
        new_version = self.todays_date_str + '.' + str(patch)

        # Update the version in the blob
        self.update_version_in_blob(new_version)

        return new_version

    def update_version_in_blob(self, new_version):
        updated_version_content = f"__version__ = '{new_version}'\n__all__ = ['__version__']"
        try:
            blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
            blob_client = blob_service_client.get_blob_client(container=self.container_name, blob=self.blob_name)
            blob_client.upload_blob(updated_version_content, overwrite=True)
            # Adding a delay to allow time for changes to propagate
            time.sleep(5)
        except Exception as e:
            print(f"Failed to update version in blob: {e}")

    def get_version_major_minor(self, build=True):
        current_version = self.current_version
        major = int(self.get_version_str(current_version, 0))
        minor = int(self.get_version_str(current_version, 1))
        minor += 1
        if minor > 9:
            major += 1
            minor = 0
        if build:
            new_version = str(major) + '.' + str(minor)  # + '.0-build'
        else:
            new_version = str(major) + '.' + str(minor)  # + '.0-release'
        # Optionally, update the version in the blob
        self.update_version_in_blob(new_version)
        return new_version

if __name__ == '__main__':
    # Step 1: Generate the wheel file
    os.system("python setup.py sdist bdist_wheel")

    # Step 2: Instantiate VersionManager
    vm = VersionManager()

    # Step 3: Increment the version and update the Blob
    updated_version = vm.get_version()  # Calculates and updates the new version in Blob
    logging.info(f"Updated version in Blob: {updated_version}")

    # Step 4: Re-fetch the version from the Blob after update
    final_version = vm.fetch_version_from_blob()
    logging.info(f"Re-fetched version from Blob after update: {final_version}")

    # Step 5: Save the re-fetched version locally in _version.py
    version_content = f"__version__ = '{final_version}'\n__all__ = ['__version__']\n"
    with open(vm.version_file, 'w') as file:
        file.write(version_content)
    logging.info(f"Saved the version locally in _version.py: {final_version}")

    # Step 6: Assert for consistency
    assert final_version == updated_version, "Version mismatch between Blob and local file!"
    logging.info("Version update process completed successfully.")



