#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""
from docvu_de_core.modules import SplitByBullets
from docvu_de_core.modules import MSTGraph
from docvu_de_core.search import Search<PERSON><PERSON><PERSON><PERSON>Helper
from docvu_de_core.utils import logger
import re


class ExceptionSearch:
    """
    Handles the extraction and processing of data based on bullet points and
    specific field information within a structured document.

    Attributes:
        debug (bool): Enables debug logging for additional details.
        helper (SearchKeyFieldHelper): Helper instance for searching key fields.
        is_key_found (bool): Tracks if the key field is found in the document.
        curr_page_bullet_point (int or None): The bullet point identifier for the current page.
        prev_page_bullet_point (int or None): The bullet point identifier for the previous page.
    """
    def __init__(self, debug=False):
        """
        Initializes the ExceptionSearch class.

        Args:
            debug (bool, optional): Enable or disable debug mode. Defaults to False.
        """
        super().__init__()
        self.helper = SearchKeyFieldHelper(logger)  # Assuming 'logger' is defined elsewhere
        self.debug = debug
        self.is_key_found = False
        self.curr_page_bullet_point = None
        self.prev_page_bullet_point = None

    def _extract_bullet_point_from_data(self, data):
        """
        Extracts and rearranges bullet point data from the provided structured input.

        Args:
            data (list): List of data items containing text blocks.

        Returns:
            list: Processed bullet points extracted from the data.
        """
        possible_answer_list = []

        # Flatten the data and extract valid text strings
        for item in data:
            for blocks in item.get("TEXT_BLOCK", []):
                for text_line in blocks.get("TEXT_LINE", []):
                    for text_string in text_line.get("STRING", []):
                        if text_string:
                            possible_answer_list.append(text_string)

        # Re-arrange based on vertical and horizontal positions
        sorted_list = self.helper.process_utils.rearrange_based_on_vpos_hpos(possible_answer_list)

        # Split the sorted list by bullet points
        sbb_output = SplitByBullets.split_by_bullets(sorted_list)

        return sbb_output.value

    def get_curr_page_bullet_point(self, whole_page_data):
        """
        Identifies the current page's bullet point number.

        Args:
            whole_page_data (list): The data from the current page.

        Returns:
            int or None: Current bullet point number if found, otherwise None.
        """
        # Reset current bullet point value before processing
        self.curr_page_bullet_point = None

        # Extract the bullet points from the current page
        value = self._extract_bullet_point_from_data(whole_page_data)

        if value:
            # Loop through value[0] and value[1] so on to find bullet point
            for point in [value[0].strip() if len(value) > 0 else None,
                          value[1].strip() if len(value) > 1 else None,
                          value[2].strip() if len(value) > 2 else None,
                          value[3].strip() if len(value) > 3 else None,
                          value[4].strip() if len(value) > 4 else None]:
                if point:  # Check if point is not None
                    match = re.match(r"(\d+)[.]?", point)
                    if match:
                        self.curr_page_bullet_point = int(match.group(1))
                        break
        return self.curr_page_bullet_point

    def get_prev_page_bullet_point(self, prev_page_json_data):
        """
        Identifies the previous page's bullet point number.

        Args:
            prev_page_json_data (list): The data from the previous page.

        Returns:
            int or None: Previous bullet point number if found, otherwise None.
        """
        # Extract the bullet points from the previous page
        value = self._extract_bullet_point_from_data(prev_page_json_data)

        if value and len(value) > 0:
            # Extract bullet point from the last element of the list (value[-1])
            last_bp_val = value[-1].strip()
            match = re.match(r"(\d+)\.", last_bp_val)
            if match:
                self.prev_page_bullet_point = int(match.group(1))
        else:
            self.prev_page_bullet_point = None

        return self.prev_page_bullet_point

    def get_exception_data_with_start_end_identifiers(self, whole_page_data, field_info, search_after,
                                                       use_start_identifier=False, use_key=False):
        """
        Extracts relevant data from a page based on start and end identifiers.

        Args:
            whole_page_data (list): List of text data on the page.
            field_info (dict): Information about the field, including identifiers.
            search_after (int): Vertical position after which to search.
            use_start_identifier (bool, optional): Whether to use start identifiers. Defaults to False.
            use_key (bool, optional): Whether to use a key for matching. Defaults to False.

        Returns:
            list: List of possible data matches based on the provided identifiers.
        """
        consider_data = False
        possible_answer_list = []
        secondary_possible_answer_list = []
        third_possible_answer_list = []
        start_identifier_vpos_info = []
        key_vpos_info = []
        previous_text = None  # Initialize a variable to store the previous text

        for ts_id, text_string in enumerate(whole_page_data):
            # Combine the current text and the previous text
            if previous_text:
                combined_text = previous_text + " " + text_string[
                    "text"]  # Concatenate with a space or your desired delimiter
            else:
                combined_text = text_string["text"]  # If there's no previous text, use just the current text

            # Check if we are considering data using the start identifier, and combine the current and previous text
            if (not consider_data and
                    self.helper.process_utils.check_if_key_found_is_individual(
                        [combined_text], field_info["start_identifier"])[0]) and self.helper.is_in_header(text_string):
                consider_data = True
                previous_text = text_string["text"]  # Update previous_text immediately after considering data

            # Check if we should use the key and if it matches the combined text (current + previous)
            if use_key:
                if self.helper.process_utils.get_match(field_info["key"], combined_text):
                    key_vpos_info.append(text_string["VPOS"])
                    break

            # Update previous_text for the next iteration
            previous_text = text_string["text"]

            if self.helper.process_utils.check_if_key_found_is_individual(
                    combined_text, field_info["start_identifier"])[0]:
                start_identifier_vpos_info.append(text_string["VPOS"])
            if text_string:
                if not consider_data:
                    # if text_string["VPOS"] > search_after:
                        secondary_possible_answer_list.append(text_string)
                else:
                    possible_answer_list.append(text_string)
        if use_start_identifier:
            vpos_info = start_identifier_vpos_info
        else:
            vpos_info = key_vpos_info if key_vpos_info else start_identifier_vpos_info
        if vpos_info:
            if len(vpos_info) > 1:
                mst_graph = MSTGraph(len(vpos_info))
                mst_graph.add_edge(vpos_info)
                clustered_points = mst_graph.cluster_using_mst()
                sorted_key = sorted(clustered_points)
                search_after = clustered_points[sorted_key[0]][-1]
            else:
                search_after = vpos_info[0]
            for ts_id, text_string in enumerate(whole_page_data):
                if text_string["VPOS"] > search_after:
                    if text_string:
                        third_possible_answer_list.append(text_string)

        if self.debug:
            print("1. possible_answer_list: ", possible_answer_list)
            print("1. secondary_possible_answer_list: ", secondary_possible_answer_list)
            print(f"\n VPOS INFO = {vpos_info}")
        if third_possible_answer_list:
            if self.debug:
                print("third_possible_answer_list =", third_possible_answer_list)
            return third_possible_answer_list
        else:
            if self.debug:
                print("possible_answer_list: ", possible_answer_list)
                print("secondary_possible_answer_list: ", secondary_possible_answer_list)
        return possible_answer_list if possible_answer_list else secondary_possible_answer_list
