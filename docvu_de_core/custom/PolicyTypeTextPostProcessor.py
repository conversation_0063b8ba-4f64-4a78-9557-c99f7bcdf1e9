from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils import ProcessUtils
from docvu_de_core.search import Search<PERSON>eyFieldHelper
from docvu_de_core.utils import ElementUtils
import re


class PolicyTypeTextPostProcessor:
    def __init__(self, **kwargs):
        logger = kwargs.get('logger')
        self.pu = ProcessUtils()
        self.skfh = SearchKeyFieldHelper(logger)

    def _find_policy_from_footer(self, data, field_value):
        down_elements = [] ## store prev line elements, can be used to fetch up dir
                                ## value from "File No" or "File Number"
        found_key = False ## boolean to track "File No" or "File Number" as key
        in_footer = False
        consider_elements = False
        ultimate_value = None
        ultimate_elements = None
        for item in data:
            for text_block in (item["TEXT_BLOCK"]):
                for text_line in (text_block["TEXT_LINE"]):
                    all_text = " ".join(v['text'] for v in text_line["STRING"])
                    if 'file no' in all_text.lower() or 'file number' in all_text.lower():
                        found_key = True
                        down_elements = []
                    for string in (text_line["STRING"]):
                        if in_footer and consider_elements:
                            if len(string.get("text", "")) > 15 and "page" not in string.get("text", "").lower():
                                down_elements.append(string)

                        text = string.get("text", "")
                        if not self.skfh.is_in_footer(string):
                            continue
                        in_footer = True
                        raw_key_str, raw_found_str = [field_value], text
                        raw_key_found_similarity_ratio = self.pu.similar(raw_key_str, raw_found_str)
                        if raw_key_found_similarity_ratio >= 0.5 and self.ends_with_date_or_year(text):
                            ultimate_value = text
                            ultimate_elements = [string]
                            break
                        if raw_key_found_similarity_ratio >= 0.8 and self.has_year(text):
                            ultimate_value = text
                            ultimate_elements = [string]
                            break
                    if in_footer and found_key:
                        if down_elements:
                            consider_elements = False
                        else:
                            consider_elements = True
                    if ultimate_value:
                        break
                if ultimate_value:
                    break
            if ultimate_value:
                break

        if not ultimate_value and down_elements:
            possible_ultimate_value = down_elements[0]['text']
            raw_key_found_similarity_ratio = self.pu.similar([field_value], possible_ultimate_value)
            if raw_key_found_similarity_ratio >= 0.25 and self.ends_with_date_or_year(possible_ultimate_value):
                ultimate_value = possible_ultimate_value
                ultimate_elements = [down_elements[0]]
            if raw_key_found_similarity_ratio >= 0.4 and self.has_year(possible_ultimate_value):
                ultimate_value = possible_ultimate_value
                ultimate_elements = [down_elements[0]]

        return ultimate_value, ultimate_elements

    def find_policy_from_footer(self, page_json_data, prev_page_json_data,  value):
        ultimate_value, ultimate_elements = self._find_policy_from_footer(page_json_data, value)

        if not ultimate_value and prev_page_json_data:
            ultimate_value, ultimate_elements = self._find_policy_from_footer(prev_page_json_data, value)

        return ultimate_value, ultimate_elements

    def ends_with_date_or_year(self, value):
        # Simple regex for date formats (you might need to adjust this based on your date formats)
        # date_regex = r"\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b"
        date_regex = r"(?:\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b|\b\d{4}\b)$"
        return bool(re.search(date_regex, value))

    def has_year(self, value):
        # Regex to check if a date or year occurs anywhere in the string
        date_regex = r"(?:\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b|\b\d{4}\b)"
        return bool(re.search(date_regex, value))

    def __call__(self, **kwargs):
        elements = kwargs.get('elements')
        page_json_data = kwargs.get('page_json_data')
        prev_page_json_data = kwargs.get('prev_page_json_data')

        value = " ".join(e['text'] for e in elements)
        success = False
        field_value = None
        value_has_year = False

        if not (value and self.ends_with_date_or_year(value)):
            value_has_year = False
        else:
            value_has_year = True
        if value:
            field_value, elements = self.find_policy_from_footer(page_json_data, prev_page_json_data,  value)
        if field_value is None:
            field_value = value
        else:
            if not value_has_year:
                success = True
            else:
                if self.pu.similar(field_value, value) > 0.3:
                    success = True
                else:
                    field_value = value

        bbox, line_number = ElementUtils.get_bbox_and_line_number(elements)
        # Format the output
        output_format = PostProcessOutputFormat(
                                                    bbox=bbox,
                                                    line_number=line_number,
                                                    value=field_value,
                                                    success=success,
                                                    is_table=True
                                            )

        return output_format
