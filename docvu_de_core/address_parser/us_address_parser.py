#!/usr/bin/python
# -*- coding: utf-8 -*-

import usaddress
import collections


class USAddressParser:
    def __init__(self, debug=False):
        self.recipient_label = ['Recipient']
        self.address_line_1_label = ['AddressNumberPrefix',
                                     'AddressNumber',
                                     'AddressNumberSuffix',
                                     'StreetNamePreModifier',
                                     'StreetNamePreDirectional',
                                     'StreetNamePreType',
                                     'StreetName',
                                     'StreetNamePostType',
                                     'StreetNamePostDirectional']
        self.address_line_2_label = ['SubaddressType',
                                     'SubaddressIdentifier',
                                     'BuildingName',
                                     'OccupancyType',
                                     'OccupancyIdentifier',
                                     'CornerOf',
                                     'LandmarkName']
        self.address_line_2_label = ['SubaddressType',
                                     'SubaddressIdentifier',
                                     'OccupancyType',
                                     'OccupancyIdentifier',
                                     'LandmarkName']
        self.address_line_3_label = ['SubaddressType',
                                     'SubaddressIdentifier',

                                     'OccupancyType',
                                     'OccupancyIdentifier',

                                     'LandmarkName']
        self.city_label = ['PlaceName']
        self.state_label = ['StateName']
        self.zip_code_label = ['ZipCode']
        self.p_o_box_label = ['USPSBoxType',
                              'USPSBoxID',
                              'USPSBoxGroupType',
                              'USPSBoxGroupID']
        self.intersection_label = ['IntersectionSeparator']
        self.address_labels = [self.recipient_label,
                               self.address_line_1_label,
                               self.address_line_2_label,
                               self.city_label,
                               self.state_label,
                               self.zip_code_label,
                               self.p_o_box_label]
        self.debug = debug
        pass

    def extract(self, dict_val, data_type='dict'):
        address = {'recipient': None,
                   'address_line_1': None,
                   'address_line_2': None,
                   'city': None,
                   'state': None,
                   'zip_code': None,
                   'p.o.box': None}

        if data_type == 'dict':
            for label, value in dict_val.items():
                for temp_labels, k_label in zip(self.address_labels, address):
                    if label in temp_labels:
                        if address[k_label] is None:
                            address[k_label] = value
                        else:
                            address[k_label] = address[k_label] + ' ' + value
        else:
            for val in dict_val:
                value, label = val
                for temp_labels, k_label in zip(self.address_labels, address):
                    if label in temp_labels:
                        if address[k_label] is None:
                            address[k_label] = value
                        else:
                            address[k_label] = address[k_label] + ' ' + value

        return address

    def parse_text(self, text):
        try:
            address_tag = usaddress.tag(text)
            if self.debug:
                print(f"Address Tags = {address_tag}")
            for val in address_tag:
                if type(val) == collections.OrderedDict:
                    dict_val = dict(val)
                    return self.extract(dict_val)

        except usaddress.RepeatedLabelError as e:
            if self.debug:
                print(f"Address Tags = {e.parsed_string}")
            return self.extract(e.parsed_string, data_type='tuple')
        return
