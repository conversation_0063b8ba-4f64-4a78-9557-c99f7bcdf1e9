import os
import cv2
import json
from tabulate import tabulate
from docvu_de_core.table_processor.extract_from_contours import ExtractTableDataFromClosedContours
from docvu_de_core.io import TableProcessorOutputFormat
from .table_utils import TableUtils


class ExtractDataFromTable:
    """
    Class for extracting data from tables in images or JSON-structured data.
    """

    def __init__(self, **kwargs):
        """
        Initialize the extractor. Currently, no specific parameters are handled.
        """
        pass

    def load_image(self, image_path):
        """
        Loads an image from the specified path.

        Args:
            image_path (str): Path to the image file.

        Returns:
            np.ndarray: The loaded image as a NumPy array.
        """
        return cv2.imread(image_path)
    
    def __call__(self, ext_item, img_path, json_data):
        """
        Process a table from the given image and JSON data.

        Args:
            ext_item (dict): Configuration for extraction, including table structure and desired field.
            img_path (str): Path to the image file.
            json_file (str): Path to the JSON file containing table metadata.

        Returns:
            TableProcessorOutputFormat: Result of the extraction, including the extracted value and success status.
        """
        value = None
        success = False
        line_number = None
        bbox = None

        # Initialize the table extraction utility with specified configurations
        extract_table_data = ExtractTableDataFromClosedContours(
            results_path='./results/',
            save_results=True,
            use_nested_table_config=True
        )
        
        # Load the image
        image = self.load_image(img_path)

        # Extract tables from the JSON and image
        table_df_dict, line_text_info = extract_table_data.extract_from_table(
            json_data, image, ext_item, first_row_as_header=True
        )
        
        if table_df_dict:
            for key, df in table_df_dict.items():
                # Display the extracted table
                print("Table - {}".format(key))                    
                print(tabulate(df, headers='keys', tablefmt='psql', floatfmt=".2f"))

                # Check if the current table matches the specified sequence in the extraction config
                if key in ext_item["table_processor"]["extract_data_from_table"]["table_sequence"]:
                    
                    # Modify the DataFrame to make headers the first row
                    modified_df = extract_table_data.make_headers_first_row(df)

                    # Extract the field value based on the configuration
                    field_table_config = ext_item["table_processor"]["extract_data_from_table"]
                    cell_value = extract_table_data.get_field_value(modified_df, field_table_config)

                    # Print the extracted value
                    print("Extracted Value from table: " + str(cell_value))
                    
                    if cell_value:
                        success = True

                        bbox = TableUtils.get_bounding_box(line_text_info[key], cell_value)
                    
                    # Return the result in the specified output format
                    return TableProcessorOutputFormat(
                        value=cell_value, 
                        success=success,
                        line_number=line_number,
                        bbox=bbox
                    )

        # Return the default output format if no value was extracted
        return TableProcessorOutputFormat(
            value=value, 
            success=success,
            line_number=line_number,
            bbox=bbox
        )
