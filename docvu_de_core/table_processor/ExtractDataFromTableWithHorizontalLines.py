import cv2
import os
import json
from tabulate import tabulate
from docvu_de_core.table_processor.extract_from_contours import ExtractTableDataFromClosedContours
from docvu_de_core.io import TableProcessorOutputFormat
from .table_utils import TableUtils


class ExtractDataFromTableWithHorizontalLines:
    """
    Class to extract data from tables with horizontal lines in an image.
    """

    def __init__(self, **kwargs):
        """
        Initialize the extractor with optional configurations.
        """
        pass

    def load_image(self, image_path):
        """
        Loads an image from the specified path.

        Args:
            image_path (str): Path to the image file.

        Returns:
            np.ndarray: Loaded image as a NumPy array.
        """
        return cv2.imread(image_path)

    def __call__(self, ext_item, img_path, json_data):
        """
        Main method to extract data from a table with horizontal lines.

        Args:
            ext_item (dict): Configuration for the extraction, including table structure and key-value mapping.
            img_path (str): Path to the image file.
            json_file (str): Path to the JSON file containing table metadata.

        Returns:
            TableProcessorOutputFormat: Contains the extracted value and success flag.
        """
        value = None  # Default value if no data is extracted
        success = False  # Flag to indicate whether extraction was successful
        line_number = None
        bbox = None

        # Initialize the table extraction utility
        extract_table_data = ExtractTableDataFromClosedContours(
            results_path='./results/',  # Directory to save results
            save_results=True,          # Whether to save intermediate results
            use_nested_table_config=True  # Use nested table configuration if applicable
        )

        # Load the image
        image = self.load_image(img_path)

        # Extract table data from the image and JSON metadata
        table_df, line_text_info = extract_table_data.extract_from_open_table_with_horizontal_lines(json_data, image, ext_item)

        if table_df is not None:
            # Print the extracted table in tabular format
            print(tabulate(table_df, headers='keys', tablefmt='psql', floatfmt=".2f"))

            # Retrieve the configuration for field extraction
            field_table_config = ext_item["table_processor"]["extract_data_from_table_with_horizontal_lines"]

            # Extract the desired field value from the table
            value = extract_table_data.get_field_value(table_df, field_table_config)
            # print(value)  # Print the extracted value for debugging

            # Set success flag to True if a value was extracted
            if value is not None and value != "":
                success = True

                bbox = TableUtils.get_bounding_box(line_text_info, value)
        
        # Return the extracted value and success status in the expected format
        return TableProcessorOutputFormat(
            value=value, 
            success=success,
            line_number=line_number,
            bbox=bbox
        )
