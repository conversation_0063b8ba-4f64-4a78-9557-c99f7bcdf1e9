import cv2
import numpy as np
import os
import shutil


# Config parameters for detecting table coordinates from image.
# These values are fine-tuned and are for Nested tables.

class NestedTableConfig:
    def __init__(self):
        self.min_table_area = 500  # 10000
        self.min_table_height = 10 #30
        self.min_table_width = 50
        self.min_nested_area = 0.0005  # 0.005
        self.max_table_area_normalised = 0.75
        self.close_boundary_limits = 10
        pass


# Config parameters for detecting table coordinates from image.
# These values are fine-tuned and are for Parent table.

class ParentTableConfig:
    def __init__(self):
        self.min_table_area = 5000  # 10000
        self.min_table_height = 10
        self.min_table_width = 50
        self.min_nested_area = 0.0005  # 0.005
        self.max_table_area_normalised = 0.75
        self.close_boundary_limits = 10
        pass


# Config parameters for detecting Line coordinates from image.
# These values are fine-tuned for detecting multiple lines, except broken one.

class HorizontalLineConfig:
    def __init__(self):
        self.canny_edge_min_th = 50
        self.canny_edge_max_th = 150
        self.canny_edge_aperture_size = 3
        self.hough_lines_dist_resolution_pixels = 1
        self.hough_lines_min_vote_for_valid_line = 1000
        self.hough_lines_min_allowed_length = 100
        self.hough_lines_max_line_gap = 1
        self.same_line_merged_pixel_gap_th = 10
        pass


class TableDetection:
    def __init__(self, results_path='./../results',
                 save_results=False,
                 use_nested_table_config=False,
                 debug=False, display=False):
        self.debug = debug
        self.display = display
        self.use_nested_table_config = use_nested_table_config
        if use_nested_table_config:
            self.config = NestedTableConfig()
        else:
            self.config = ParentTableConfig()

        self.line_config = HorizontalLineConfig()
        self.results_path = results_path
        self.save_results = save_results
        if self.save_results:
            self.delete_files_from_folder()

        pass

    def delete_files_from_folder(self):
        folder = self.results_path
        if not os.path.isdir(folder):
            os.mkdir(folder)
        else:
            for filename in os.listdir(folder):
                file_path = os.path.join(folder, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                except Exception as e:
                    if self.debug:
                        print('Failed to delete %s. Reason: %s' % (file_path, e))
                    pass
        return

    @staticmethod
    def display_image(win_name, img):
        cv2.imshow(win_name, img)
        cv2.waitKey()
        cv2.destroyWindow(win_name)
        return

    def sorted_stats(self, nested_stats, merge_cells=False):
        sorted_stats = {}
        table_id = 1
        for k, value in nested_stats.items():
            x = [v[0] for v in value]
            y = [v[1] for v in value]
            # print(value)

            sort_index = np.argsort(y)
            y_sorted = [y[i] for i in sort_index]
            clustered_y = {}
            clustered_x = {}
            cluster_count = 1
            for index, (si, y1) in enumerate(zip(sort_index, y_sorted)):
                if index == 0:
                    if cluster_count in clustered_y.keys():
                        clustered_y[cluster_count].append(y1)
                        clustered_x[cluster_count].append(x[si])
                    else:
                        clustered_y[cluster_count] = [y1]
                        clustered_x[cluster_count] = [x[si]]
                else:
                    if y1 - y_sorted[index - 1] <= self.config.close_boundary_limits and merge_cells:
                        clustered_y[cluster_count].append(y1)
                        clustered_x[cluster_count].append(x[si])
                    else:
                        cluster_count += 1
                        if cluster_count in clustered_y.keys():
                            clustered_y[cluster_count].append(y1)
                            clustered_x[cluster_count].append(x[si])
                        else:
                            clustered_y[cluster_count] = [y1]
                            clustered_x[cluster_count] = [x[si]]
            # print("clustered_y = ", clustered_y)
            # print("clustered_x = ", clustered_x)
            table_sorted = {}
            for (k1, x_value), (k2, y_value) in zip(clustered_x.items(), clustered_y.items()):
                sort_index = np.argsort(x_value)
                x_sorted = [x_value[i] for i in sort_index]
                y_sorted = [y_value[i] for i in sort_index]
                for x1, y1 in zip(x_sorted, y_sorted):
                    val_index = [v for v in value if v[0] == x1 and v[1] == y1]
                    if val_index:
                        val_index = val_index[0]
                    else:
                        continue
                    # print(val_index)
                    if k1 in table_sorted.keys():
                        table_sorted[k1].append(val_index)
                    else:
                        table_sorted[k1] = [val_index]
            if table_sorted:
                sorted_stats[table_id] = table_sorted
                table_id += 1
        return sorted_stats

    def detect_table(self, image, line_stats, draw_virtual_line=False, thick_lines=False,
                     nested_kernel_len=7):
        if draw_virtual_line and not line_stats:
            combined_image = self.get_skeleton(image, thick_lines=thick_lines)
            indices = np.where(combined_image > 0)
            x_min, x_max = min(indices[1]) + 1, max(indices[1]) - 1
            y_min, y_max = min(indices[0]) + 1, max(indices[0]) - 1
            cv2.line(image, (x_min, y_min), (x_min, y_max), (0, 0, 0), 1)
            cv2.line(image, (x_max, y_min), (x_max, y_max), (0, 0, 0), 1)
            file_name = os.path.join(self.results_path, 'virtual_lines_1.jpg')
            cv2.imwrite(file_name, image)

        if self.save_results:
            combined_image = self.get_skeleton(image, thick_lines=thick_lines, kernel_len=nested_kernel_len)
            file_name = os.path.join(self.results_path, 'closed_contours.jpg')
            cv2.imwrite(file_name, combined_image)

        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        thresh = cv2.adaptiveThreshold(gray, 255,
                                       cv2.ADAPTIVE_THRESH_MEAN_C,
                                       cv2.THRESH_BINARY_INV, 11, 2)

        page_contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if self.save_results:
            cnt_image = image.copy()
            cv2.drawContours(cnt_image, page_contours, -1, (0, 255, 0), 3)
            file_name = os.path.join(self.results_path, 'detected_closed_contours.jpg')
            cv2.imwrite(file_name, cnt_image)
            if self.display:
                self.display_image('detected_closed_contours', cnt_image)
        table_coordinates = []
        for contour in page_contours:
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            if area >= self.config.min_table_area and h >= self.config.min_table_height:
                table_coordinates.append((x, y, w, h, area))
        sorted_by_vpos = sorted(table_coordinates, key=lambda tup: tup[1])
        return sorted_by_vpos

    def get_nested_table(self, image, table_stats, nested_kernel_len=7, thick_lines=False):
        nested_stats = {}
        for index, t_stats in enumerate(table_stats):
            # print("t_stats = ", t_stats)
            nested_contours = []
            tx, ty, tw, th, ta = t_stats
            t_image = image[ty:ty + th, tx:tx + tw]
            combined_image = self.get_skeleton(t_image, thick_lines=thick_lines, kernel_len=nested_kernel_len)
            contours, hierarchy = cv2.findContours(combined_image, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

            if self.save_results:
                cnt_image = t_image.copy()
                cv2.drawContours(cnt_image, contours, -1, (0, 255, 0), 3)
                file_name = os.path.join(self.results_path, f'{index}_closed_contours.jpg')
                cv2.imwrite(file_name, cnt_image)
                file_name = os.path.join(self.results_path, f'{index}__skeleton.jpg')
                cv2.imwrite(file_name, combined_image)

            count = 1
            for contour in contours:
                area = cv2.contourArea(contour)
                x, y, w, h = cv2.boundingRect(contour)

                if (0.5 <= (float(tw) / w) <= 1.5 or 0.5 <= (float(th) / h) <= 1.5) and not self.use_nested_table_config:
                    if self.display:
                        self.display_image('temp', t_image[y:y + h, x:x + w])
                        print('HERE1', float(tw) / w, float(th) / h, area, ta, ta * self.config.max_table_area_normalised, x, y,
                              h, w)
                    continue
                if (area > ta * self.config.max_table_area_normalised or area == ta or
                        area < ta * self.config.min_nested_area):
                    if self.display:
                        self.display_image('temp', t_image[y:y + h, x:x + w])
                        print('HERE2', float(tw) / w, float(th) / h, area, ta, ta * self.config.max_table_area_normalised, x, y,
                              h, w)
                    continue

                if h < self.config.min_table_height or w < self.config.min_table_width:
                    if self.display:
                        self.display_image('temp', t_image[y:y + h, x:x + w])
                        print('HERE3', float(tw) / w, float(th) / h, area, ta, ta * self.config.max_table_area_normalised, x, y,
                              h, w)
                    continue

                if self.save_results:
                    patch = t_image[y:y + h, x:x + w]
                    file_name = os.path.join(self.results_path, f'{index}_closed_contours_{count}.jpg')
                    cv2.imwrite(file_name, patch)
                    if self.display:
                        self.display_image(file_name, patch)
                count += 1
                # print(x, y, w, h, area)
                block_cords = (tx + x, ty + y, w, h, area)
                nested_contours.append(block_cords)

            nested_stats[index] = nested_contours
        return nested_stats

    def detect(self, image, nested_kernel_len=7, thick_lines=False, merge_cells=False, draw_virtual_line=False):
        line_stats = []
        if draw_virtual_line:
            line_stats, image = self.detect_lines(image, draw_virtual_line=draw_virtual_line)
        table_stats = self.detect_table(image, line_stats, draw_virtual_line=draw_virtual_line,
                                        thick_lines=thick_lines, nested_kernel_len=nested_kernel_len)
        if self.debug:
            print("Detected Primary Table Stats: {}".format(table_stats))
        nested_stats = self.get_nested_table(image, table_stats, thick_lines=thick_lines,
                                             nested_kernel_len=nested_kernel_len)
        if self.debug:
            print("Detected Nested Table Stats: {}".format(nested_stats))
        sorted_stats = self.sorted_stats(nested_stats, merge_cells)
        if self.debug:
            print("Sorted Table Stats: {}".format(sorted_stats))
        return sorted_stats

    def detect_lines(self, image, draw_virtual_line=False):
        line_stats = self.detect_horizontal_vertical_lines(image)
        # print(line_stats)
        if draw_virtual_line and line_stats:
            w = [i[2] for i in line_stats]
            max_w = max(w, key=w.count)
            x = [i[0] for i in line_stats]
            min_x = max(x, key=x.count)
            height, width, channels = image.shape
            cv2.line(image, (min_x, 0), (min_x, height - 1), (0, 0, 0), 1)
            cv2.line(image, (min_x + max_w, 0), (min_x + max_w, height - 1), (0, 0, 0), 1)
            file_name = os.path.join(self.results_path, 'virtual_lines.jpg')
            cv2.imwrite(file_name, image)
        return line_stats, image

    def draw_virtual_lines(self, image, line_stats):
        w = [i[2] for i in line_stats]
        max_w = max(w, key=w.count)
        x = [i[0] for i in line_stats]
        min_x = max(x, key=x.count)
        height, width, channels = image.shape
        cv2.line(image, (min_x, 0), (min_x, height - 1), (0, 0, 0), 1)
        cv2.line(image, (min_x + max_w, 0), (min_x + max_w, height - 1), (0, 0, 0), 1)
        file_name = os.path.join(self.results_path, 'virtual_lines.jpg')
        cv2.imwrite(file_name, image)

    @staticmethod
    def get_table_start_coordinates(table):
        """
                {0: {1: [(236, 1764, 298, 41, 11878.0), (534, 1764, 413, 41, 16478.0),
                        (947, 1764, 298, 41, 11878.0), (1245, 1764, 301, 41, 11998.0)],
                    2: [(236, 1805, 298, 41, 11878.0), (534, 1805, 413, 41, 16478.0),
                        (947, 1805, 298, 41, 11878.0), (1245, 1805, 301, 41, 11998.0)],
                    3: [(236, 1846, 298, 41, 11878.0), (534, 1846, 413, 41, 16478.0),
                        (947, 1846, 298, 41, 11878.0), (1245, 1846, 301, 41, 11998.0)],
                    4: [(236, 1887, 298, 41, 11612.0), (534, 1887, 413, 40, 16066.0),
                        (947, 1887, 298, 40, 11581.0), (1245, 1887, 301, 40, 11698.0)],
                    5: [(236, 1928, 298, 41, 11878.0), (534, 1928, 413, 41, 16478.0),
                        (947, 1928, 298, 41, 11878.0), (1245, 1928, 301, 41, 11998.0)],
                    6: [(236, 1970, 297, 78, 22790.0), (534, 1970, 413, 78, 31722.0),
                    (947, 1970, 298, 78, 22867.0), (1245, 1970, 301, 78, 23098.0)]}, 1: {}}
                """
        table_limits = {}
        for table_count, table_cords in table.items():
            x_min, y_min = np.inf, np.inf
            x_max, y_max = 0, 0
            for row, cols in table_cords.items():
                x_min = min(min([v[0] for v in cols]), x_min)
                y_min = min(min([v[1] for v in cols]), y_min)
                x_max = max(max([v[0] + v[2] for v in cols]), x_max)
                y_max = max(max([v[1] + v[3] for v in cols]), y_max)

            table_limits[table_count] = [x_min, y_min, x_max, y_max]
        sorted_table_limits = dict(sorted(table_limits.items(), key=lambda item: item[1][0]))
        return sorted_table_limits

    @staticmethod
    def get_skeleton(img, kernel_len=7, thick_lines=False):
        grey = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        threshold_image = cv2.threshold(grey, 200, 255, cv2.THRESH_BINARY)[1]
        inverted_image = cv2.bitwise_not(threshold_image)
        hor = np.array([[1] * kernel_len])
        vertical_lines_eroded_image = cv2.erode(inverted_image, hor, iterations=10)
        vertical_lines_eroded_image = cv2.dilate(vertical_lines_eroded_image, hor, iterations=10)
        ver = np.array([[1]] * kernel_len)
        horizontal_lines_eroded_image = cv2.erode(inverted_image, ver, iterations=10)
        horizontal_lines_eroded_image = cv2.dilate(horizontal_lines_eroded_image, ver, iterations=10)
        combined_image = cv2.add(vertical_lines_eroded_image, horizontal_lines_eroded_image)
        if thick_lines:
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
            combined_image = cv2.dilate(combined_image, kernel, iterations=5)
            combined_image = cv2.erode(combined_image, kernel, iterations=5)
        return combined_image

    def merge_same_lines(self, sorted_line_tuple):
        merged_line = []
        for index, points in enumerate(sorted_line_tuple):
            points = list(points)
            if index == 0:
                #x1, y1, x2, y2 = points[0]
                merged_line.append(points)
            else:
                last_point = merged_line[-1]
                lx1, ly1, lx2, ly2 = last_point
                cx1, cy1, cx2, cy2 = points
                if abs(cy1 - ly1) <= self.line_config.same_line_merged_pixel_gap_th:
                    min_x, max_x = min(cx1, lx1), max(cx2, lx2)
                    min_y, max_y = min(cy1, ly1), max(cy2, ly2)
                    merged_line[-1] = (min_x, min_y, max_x, max_y)
                else:
                    merged_line.append(points)
        return merged_line

    def detect_horizontal_vertical_lines(self, image):
        #gray = self.get_skeleton(image)
        #self.display_image('img', gray)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, self.line_config.canny_edge_min_th,
                          self.line_config.canny_edge_max_th, apertureSize=self.line_config.canny_edge_aperture_size)
        if self.save_results:
            file_name = os.path.join(self.results_path, 'edge_image.jpg')
            cv2.imwrite(file_name, edges)

        lines_list = []
        lines = cv2.HoughLinesP(
            edges,  # Input edge image
            rho=self.line_config.hough_lines_dist_resolution_pixels,  # Distance resolution in pixels
            theta=np.pi / 180,  # Angle resolution in radians
            threshold=self.line_config.hough_lines_min_vote_for_valid_line,  # Min number of votes for valid line
            minLineLength=self.line_config.hough_lines_min_allowed_length,  # Min allowed length of line
            maxLineGap=self.line_config.hough_lines_max_line_gap  # Max allowed gap between line for joining them
        )
        # print(lines)
        if lines is not None and len(lines) > 0:
            line_image = image.copy()
            lines_sorted_by_vpos = sorted(lines, key=lambda tup: tup[0][1])
            sorted_lines_list = []
            for points in lines_sorted_by_vpos:
                x1, y1, x2, y2 = points[0]
                sorted_lines_list.append((x1, y1, x2, y2))
            lines_sorted_by_vpos = self.merge_same_lines(sorted_lines_list)
            for index, points in enumerate(lines_sorted_by_vpos):
                x1, y1, x2, y2 = points
                if self.save_results:
                    cv2.putText(line_image, str(index), (x1-5, y1),
                                cv2.FONT_HERSHEY_SIMPLEX, 1,(255, 0, 0), 2, cv2.LINE_AA)
                    cv2.line(line_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                x, y, w, h = x1, y1, x2 - x1, y2 - y1
                lines_list.append((x, y, w, h))

            if self.save_results:
                file_name = os.path.join(self.results_path, 'detectedLines.jpg')
                cv2.imwrite(file_name, line_image)
            # print(len(lines_list))
        return lines_list


if __name__ == '__main__':
    pass
