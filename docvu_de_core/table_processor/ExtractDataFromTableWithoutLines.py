import cv2
import os
import json
from tabulate import tabulate
from docvu_de_core.table_processor.extract_from_contours import ExtractTableDataFromClosedContours
from docvu_de_core.io import TableProcessorOutputFormat
from .table_utils import TableUtils


class ExtractDataFromTableWithoutLines:
    """
    A class to extract data from tables without lines.
    """

    def __init__(self, **kwargs):
        """
        Initialize the ExtractDataFromTableWithoutLines class.
        """
        pass  # No initialization-specific logic required currently

    def load_image(self, image_path):
        """
        Load an image from the specified file path.

        Args:
            image_path (str): Path to the image file.

        Returns:
            np.ndarray: Loaded image as a NumPy array.
        """
        return cv2.imread(image_path)

    def __call__(self, ext_item, img_path, json_data, **kwargs):
        """
        Main method to extract data from a table without lines.

        Args:
            ext_item (dict): Configuration for table extraction and field mapping.
            img_path (str): Path to the image file.
            json_file (str): Path to the JSON file containing table metadata.
            **kwargs: Additional keyword arguments for future extensions.

        Returns:
            TableProcessorOutputFormat: Contains the extracted value and a success flag.
        """
        success = False  # Flag to indicate successful data extraction
        value = None  # Default value if extraction fails
        line_number = None
        bbox = None

        # Initialize the table data extraction utility
        extract_table_data = ExtractTableDataFromClosedContours(
            results_path='./results/',  # Directory to save results
            save_results=True,          # Whether to save intermediate results
            use_nested_table_config=True  # Use nested table configuration if applicable
        )

        # Load the image
        image = self.load_image(img_path)

        # Extract table data from the image and JSON metadata
        table_df, line_text_info = extract_table_data.extract_from_open_table_without_lines_v2(json_data, image, ext_item)
        
        if table_df is not None:
            # Print the extracted table in a formatted table view
            print(tabulate(table_df, headers='keys', tablefmt='psql', floatfmt=".2f"))

            # # Extract specific field values based on configuration
            # field_table_config = ext_item["table_processor"]["extract_data_from_table_without_lines"]
            # value = extract_table_data.get_field_value(table_df, field_table_config)
            # print("Extracted Value from table: " + str(value))
            
            # if isinstance(value, list):
            #     # Remove empty elements
            #     value = [item for item in value if item]
            #     print("Cleaned list:", value)

            #     if not value:
            #         value = " "

            # # Set success flag if value is valid
            # if value not in [None, ""]:
            #     success = True

            #     if not isinstance(value, list):
            #         bbox = TableUtils.get_bounding_box(line_text_info, value)
            
            field_table_config = ext_item["table_processor"]["extract_data_from_table_without_lines"]

            if "table_group_rows_by_column" in field_table_config and field_table_config["table_group_rows_by_column"]:
                group_target_col = field_table_config["field_string_col"]
                grouped = extract_table_data.group_rows_by_column(table_df, group_target_col)

                value = extract_table_data.get_data_sources(grouped, field_table_config["group_key"], field_table_config["probable_field_value_col"])

            else:
                # Extract specific field values based on configuration
                # field_table_config = ext_item["table_processor"]["extract_data_from_table_without_lines"]
                value = extract_table_data.get_field_value(table_df, field_table_config)

            print("Extracted Value from table: " + str(value))
            
            if isinstance(value, list):
                # Remove empty elements
                value = [item for item in value if item]
                print("Cleaned list:", value)

                if not value:
                    value = " "

            # Set success flag if value is valid
            if value not in [None, ""]:
                success = True

                if not isinstance(value, list):
                    bbox = TableUtils.get_bounding_box(line_text_info, value)


        # Return the extracted value and success status
        return TableProcessorOutputFormat(
            value=value, 
            success=success,
            line_number=line_number,
            bbox=bbox
        )
