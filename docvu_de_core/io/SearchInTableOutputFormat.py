from docvu_de_core.io import OutputFormat

class SearchInTableOutputFormat(OutputFormat):
    def __int__(self, value, success, elements, line_number, bbox, field_key, found_val=[], **kwargs):
        super().__init__(value = value,
                        success = success,
                        elements = elements,
                        line_number = line_number,
                        bbox = bbox,
                        field_key = field_key,
                        found_val=found_val,
                        **kwargs)
