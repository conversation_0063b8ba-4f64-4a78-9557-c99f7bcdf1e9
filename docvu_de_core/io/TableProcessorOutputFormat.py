from docvu_de_core.io import OutputFormat

# Define a custom class that extends the OutputFormat class
class TableProcessorOutputFormat(OutputFormat):
    # Constructor method to initialize the TableProcessorOutputFormat instance
    def __int__(self, value, success, line_number, bbox, **kwargs):
        """
        Initializes the TableProcessorOutputFormat object with specified parameters.

        Args:
            value: The primary data or output that this object encapsulates.
            success: A boolean indicating whether the operation was successful.
            **kwargs: Additional keyword arguments to be passed to the parent class.
        """
        # Call the parent class constructor to initialize inherited properties
        super().__init__(
            value=value,  # Set the primary value for this output
            success=success,  # Indicate the success status of the operation
            line_number=line_number,
            bbox=bbox,
            **kwargs)  # Pass any additional arguments
