
import os
import logging
from docvu_de_core import de_config_path 

class FormTypeConfig:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
      
        self.form_type_dict = {
            "452": {
                "9000001_452":'9000001-1003_new_PostClose.json',
                "9000017_452":'9000017-Ability_To_Repay_PostClose_classify.json',
                "9000019_452":'9000019-ACH_Authorization_Form_classify.json',
                "9001740_452":'9001740-Advance_Fee_Agreement_PostClose.json',
                "9000033_452":'9000033-Address_Certification_PostClose.json',
                "9000053_452":'9000053-Appraisal_Acknowledgement_PostClose.json',
                "9000051_452":'9000051-Application_Disclosure_PostClose.json',
                "9000304_452":'9000304-Fair_Lending_Notice_PostClose.json',
                "9000869_452":'9000869-security_instrument_PostClose.json',
                "9000671_452":'9000671-note_classify_PostClose.json',
                "9000181_452":'9000181-closing_disclosure_PostClose.json',
                "9000031_452":'9000031-Addendum_to_Loan_Application_PostClose.json',
                "9000026_452":'9000026-Acknowledgement_of_Receipt_of_the_Closing_Disclosure_PostClose.json',
                "9001992_452":'9001992-Anti_Coercion_Disclosure_PostClose.json',
                "9000116_452":'9000116-Borrower_Authorization_for_Counselling_PostClose.json',
                "9002202_452":'9002202-California_All_Purpose_Acknowledgement_PostClose.json',
                "9000045_452":'9000045-Error_Omissions_Compliance_Agreement_PostClose.json',
                "9000558_452":'9000558-Loan_Amortization_Schedule_PostClose.json',
                "9000004_452":'9000004-Form_1040_Tax_Returns_PostClose.json',
                "9000936_452":'9000936-Collateral_Protection_Insurance_Disclosure_PostClose.json',
                "9000743_452":'9000743-Owners_Affidavit_PostClose_classify.json',
                "9000075_452":'9000075-Appraisal_Waiver_Receipt_Form_PostClose.json',
                "9000759_452":'9000759-Planned_Unit_Development_Rider_PostClose.json',
                "9000866_452":'9000866-Second_Home_Rider_PostClose.json',
                "9000142_452":'9000142-Buydown_agreement_PostClose.json',
                "9000938_452":'9000938-Texas_Home_Equity_Affidavit_And_Agreement_First_Lien_Disclosure_PostClose.json',
                "9000036_452":'9000036-Adjustable_Rate_Mortgage_Disclosure_PostClose.json',
                "9000298_452":'9000298-Exhibit_A_Legal_Description_PostClose.json',
                "9000806_452":'9000806-Rate_Lock_Agreement_PostClose_Classify.json',
                "9000709_452":'9000709-Notice_Of_Right_To_Cancel_Borrower_PostClose.json',
                "8011370_452":'8011370-Notice_Of_Right_To_Cancel_Coborrower_PostClose.json',
                "9000050_452":'9000050-Anti_steering_disclosure_PostClose.json',
                "9000215_452":'9000215-Condominium_Rider_PostClose.json',
                "9000297_452":'9000297-Evidence_of_joint_application_PostClose.json',
                "9000762_452":'9000762-Power_of_Attorney_PostClose.json',
                "9000041_452":'9000041-Affidavit_of_Occupancy_PostClose.json',
                "9000115_452":'9000115-Borrower_Authorization_PostClose.json',
                "9000882_452":'9000882-Signature_and_name_affidavit_Borrower_PostClose.json',
                "8011371_452":'8011371-Signature_and_name_affidavit_Coborrower_PostClose.json',
                "9000960_452":'9000960-Trust_Agreement_PostClose.json',
                "9000501_452":'9000501-Indemnit_and_Affidavit_to_Debts_and_Liens_PostClose.json',
                "9000010_452":'9000010-1-4_Family_Rider_PostClose.json',
                "9000673_452":'9000673-Note_Allonge_PostClose.json',
                "9000432_452":'9000432-GA_Foreclosure_Disclosure_PostClose.json',
                "9000961_452":'9000961-Trust_Certification_PostClose.json',
                "9000480_452":'9000480-HUD_92900A-1820A_PostClose.json',
                "9000820_452":'9000820-Recorded_Subordination_Agreement_PostClose_classify.json',
                "9000205_452":'9000205-Compliance_Agreement_PostClose.json',
                "9002148_452":'9002148-Identification_Verification_Patriot_Act_PostClose.json',
                "8011232_452":'8011232-Identification_Verification_Patriot_Act_PostClose.json',
                "9000029_452":'9000029-Addendum_to_Closing_Disclosure_PostClose_Classify.json',
                "9000263_452":'9000263-Disclosure_Notices_PostClose.json',
                "9000283_452":'9000283-Equal_Credit_Opportunity_Act_Form_PostClose.json',
                "9002216_452":'9002216-4506-C_PostClose.json',
                "9000670_452":'9000670-No_HUD_Warranty_PostClose.json',
                "9000932_452":'9000932-Taxpayer_Consent_PostClose.json',
                "9001956_452":'9001956-Notice_to_Purchasers_PostClose.json',
                "9000143_452":'9000143-CA_Per_Diem_Interest_Disclosure_PostClose.json',
                "9000504_452":'9000504-Informed_Consumer_Choice_Disclosure_Notice_PostClose.json',
                "9000293_452":'9000293-Escrow_Waiver_PostClose.json',
                "9000494_452":'9000494-Impound_Authorization_PostClose.json',
                "9000044_452":'9000044-Affiliated_Business_Disclosure_Statement_Postclose.json',
                "9001996_452":'9001996-Title_Commitment_PostClose.json',
                "9000370_452":'9000370-First_Lien_Letter_PostClose.json',
                "9000222_452":'9000222-Correction_Agreement_Limited_POA_PostClose.json',
                "9000856_452":'9000856-Right_To_Select_An_Attorney_PostClose.json',
                "9002223_452":'9002223-Consumer_Explanation_Letter_PostClose.json',
                "9000189_452":'9000189-Closing_Instructions_PostClose.json',
                "9000323_452":'9000323-FHA_Appraisal_Certificate_PostClose.json',
                "9000448_452":'9000448-Hazard_and_Flood_Authorization_PostClose.json',
                "9000121_452":'9000121-Borrower_Debt_Certification_PostClose.json',
                "9002153_452":'9002153-Affidavit_Of_Affixation_PostClose.json',
                "9000371_452":'9000371-First_Payment_Letter_PostClose.json',
                "9000488_452":'9000488-Designation_Of_Homestead_Affidavit_PostClose.json',
                "9000879_452":'9000879-Settlement_Statement_PostClose.json',
                "9001060_452":'9001060-Wire_Transfer_Disbursement_Detail_PostClose.json',
                "9000506_452":'9000506-Initial_Escrow_Account_Disclosure_Statement_PostClose.json',
                "9000020_452":'9000020-Acknowledgement_Of_Fair_Market_Value_PostClose.json',
                "9000703_452":'9000703-Notice_Of_No_Oral_Agreement_PostClose.json',
                "9002213_452":'9002213-Survey_affidavit_PostClose.json',
                "9000719_452":'9000719-Notice_to_Homeowner_PostClose.json',
                "9000634_452":'9000634-Mineral_Rights_Acknowledgement_and_Agreement_PostClose.json',
                "9000301_452":'9000301-FACT_Act_Notice_To_Home_Loan_Applicant_PostClose.json',
                "9001003_452":'9001003-VA_26-0592_Military_Checklist_PostClose.json',
                "9000691_452":'9000691-Notice_of_Furnishing_Negative_Information_PostClose.json',
                "9000663_452":'9000663-Net_Tangible_Benefit_PostClose.json',
                "9000924_452":'9000924-Tax_Information_Sheet_PostClose.json',
                "9000482_452":'9000482-HUD_Appraisal_Value_Disclosure_PostClose.json',
                "9001053_452":'9001053-W9_IRS_PostClose.json',
                "8011228_452":'8011228-W9_IRS_PostClose.json',
                "9000554_452":'9000554-Line_of_Credit_Payoff_Affidavit_PostClose.json',
                "8011144_452":'8011144-Price_Agreement_PostClose.json',
                "9000228_452":'9000228-Cover_Sheet_PostClose_classify.json',
                "8010382_452":'8010382-Disbursement_of_Proceeds_PostClose.json',
                "9000916_452":'9000916-Tax_Authorization_PostClose.json',
                "9000377_452":'9000377-Flood_Determination_Disclosure_PostClose.json',
                "9000828_452":'9000828-Report_And_Certificate_Of_Loan_Disbursemt_PostClose_classify.json',
                "9000461_452":'9000461-Hold_Harmless_Agreement_PostClose.json',
                "9000481_452":'9000481-Importance_Notice_to_Homebuyers_PostClose.json',
                "9000513_452":'9000513-Instruction_to_Escrow_PostClose.json',
                "9002218_452":'9002218-Wisconsin_Property_Tax_Escrow_Option_PostClose.json',
                "9001964_452":'9001964-SSA_Form_89_Verification_Report_PostClose.json',
                "8011229_452": '8011229-SSA_Form_89_Verification_Report_PostClose.json',
                "9000672_452":'9000672-Note_Addendum_PostClose.json',
                "9000830_452":'9000830-Certificate_of_Eligibility_PostClose.json',
                "8011230_452":'8011230-Certificate_of_Eligibility_PostClose.json',
                "9001002_452":'9001002-VA_26-0503_Federal_Collection_PostClose.json',
                "8011231_452": '8011231-VA_26-0503_Federal_Collection_PostClose.json',
            },
                
            "401": {
                "9000671_401":'9000671-note_RoundPoint.json',
                "9000377_401":'9000377-Standard_Flood_Hazard_Determination_RoundPoint.json',
                "9000673_401":'9000673-Note_Allonge_RoundPoint.json',
                "9000712_401":'9000712-Notice_of_loan_servicing_transfer_disclosure_RoundPoint.json',
                "9000069_401":'9000069-Appraisal_RoundPoint.json',
                "9000109_401":'9000109-Billing_Statement_RoundPoint.json',
                "9000617_401":'9000617-Lost_Note_Affidavit_RoundPoint.json',
                "8008352_401":'8008352-Step_Rate_Loan_Servicing_RoundPoint.json',
                "9000440_401":'9000440-Servicing_Transfer_Statement_RoundPoint.json',
                "8008047_401":'8008047-Goodbye_Letter_RoundPoint.json',
                "9000506_401":'9000506-Initial_Escrow_Account_Disclosure_RoundPoint.json',
                "9000181_401":'9000181-Closing_Disclosure_TRID_RoundPoint.json',
                "8008033_401":'8008033-Note_Copy_RoundPoint.json',
                "8008097_401":'8008097-Escrow_Analysis_Annual_Notice_RoundPoint.json',
                "9000290_401":'9000290-Escrow_Analysis_RoundPoint.json',
                "8007736_401":'8007736-Payoff_Quote_RoundPoint.json',
                "9000019_401":'9000019-ACH_Form_RoundPoint.json',
                "9000752_401":'9000752-Payoff_Statement_RoundPoint.json',
                "8007263_401":'8007263-ACH_Verification_RoundPoint.json',
                "8008082_401":'8008082-Escrow_Cease_Letter_RoundPoint.json',
                "8007872_401":'8007872-Repayment_Plan_Letter_RoundPoint.json',
                "8008290_401":'8008290-Foreclosure_Invoices_RoundPoint.json',
                "8008213_401":'8008213-LM_Modification_Agreement-Fully_Executed_RoundPoint.json',
                "9000291_401":'9000291-Escrow_Letter_to_Borrower_RoundPoint.json',
                "8008080_401":'8008080-Account_Paid_In_Full-Letter_RoundPoint.json',
                "8008426_401":'8008426-Force_Placement_Notice-Flood_RoundPoint.json',
                "8008434_401":'8008434-Insurance_Document-Hazard_RoundPoint.json',
                "8008433_401":'8008433-LPI_Cancellation_Notice-Other_RoundPoint.json',
                "8008055_401":'8008055-Solicitation_Letter_Loss_Mit_RoundPoint.json',
                "9001057_401":'9001057-Welcome_Letter_RoundPoint.json',
                "8008430_401":'8008430-Insufficient_Policy_Notice-Other_RoundPoint.json',
                "8008343_401":'8008343-Loss_Mit_Complete_Application_Acknowledgement_Letter_RoundPoint.json',
                "8008028_401":'8008028-Rider_to_Note_RoundPoint.json',
                "8008029_401":'8008029-Deed_of_Trust_Rider_RoundPoint.json',
                "9000371_401":'9000371-First_Payment_Letter_RoundPoint.json',
                "9000253_401":'9000253-Deed_of_Trust_RoundPoint.json',
                "9000081_401":'9000081-Assignment_Of_Mortgage_RoundPoint.json',
                "8007854_401":'8007854-Deed_of_Trust-Recorded_RoundPoint.json',
                "8007831_401":'8007831-Servicer_Correspondence_RoundPoint.json',
                "9000392_401":'9000392-Forbearance_Agreement_RoundPoint.json',
                "8008042_401":'8008042-Rate_Change_Letter_RoundPoint.json',
                "9000581_401":'9000581-Loan_Modification_Agreement_RoundPoint.json',
                "8008083_401":'8008083-Short_Year_History_RoundPoint.json',
                "9000942_401":'9000942-Authorization_Form_3rd_Party_RoundPoint.json',
                "8007257_401":'8007257-Notice_of_Sale_or_Transfer_of_Mortgage_Loan_404_RoundPoint.json',
                "8007791_401":'8007791-Payoff_Request_RoundPoint.json',
                "9000036_401":'9000036-ARM_Servicing_RoundPoint.json',
                "8007641_401":'8007641-Late_Charge_Letter_RoundPoint.json',
                "8008248_401":'8008248-Reconveyance_Documentation_RoundPoint.json',
                "8007889_401":'8007889-SCRA_Notice_RoundPoint.json',
                "8008476_401":'8008476-Deed_of_Trust-re-recorded_RoundPoint.json',
                "8008478_401":'8008478-Welcome_Email_RoundPoint.json',
                "8008487_401":'8008487-URLA_RoundPoint.json',
                "8008073_401":'8008073-Deed_in_Lieu_Agreement_RoundPoint.json',
                "8008065_401":'8008065-Short_Sale_Agreement_RoundPoint.json',
                "8008120_401":'8008120-Notice_of_Sale_or_Transfer_of_Mortgage_Loan_RoundPoint.json',
                "8008481_401":'8008481-Deferral_Letter_RoundPoint.json',
                "9000959_401":'9000959_Transmittal_Summary_RoundPoint.json',
                "9000001_401":'9000001-1003_RoundPoint.json',
                "9002254_401":'9002254-Opt_Out_Notice_RoundPoint.json',
            },
            
            "439": {
                "9000001_439":'9000001-1003_Phl.json',
                "9000069_439":'9000069-Appraisal_Phl.json',
                "9000377_439":'9000377-Flood_cert_Phl.json',
                "9000181_439":'9000181-Closing_Disclosure_Phl.json',
                "9000253_439":'9000253-Deed_of_trust_Phl.json',
                "9000671_439":'9000671_Note_Phl.json',
                "9001033_439":'9001033-Misc_Phl.json',
                "9000003_439":'9000003-Misc_Phl.json',
             
            },
            "449": {
                "8011134_449": '8011134-PSA_MrCooper_Classify.json',
                "8011406_449": '8011406-Appendix_A_MrCooper.json',
                "8011135_449": '8011135-MSA_MrCooper_Classify.json',
                "8011139_449": '8011139-Indenture_MrCooper_Classify.json',
                "8011137_449": '8011137-Servicing_Agreement_MrCooper.json',
                "8011381_449": '8011381-SSA_MrCooper_Classify.json',
            },

            "403": {
                "9000001_403":'9000001-1003_UnderWritter.json',
                "9000089_403":'9000089-AUS_DU_classify_UW.json',
                "9000618_403":'9000618-LP_UnderWritter.json',
                "9000618_403":'9000618-LP_UnderWritter1.json',
                "9000618_403":'9000618-LP_UnderWritter_classify_UW.json',
                "9000377_403":'9000377-Flood_Hazard_Determination_Flood_Certificate_UnderWritter.json',
                "9000383_403":'9000383-Flood_Insurance_Policy_classify_UW.json',
                "9000181_403":'9000181-Closing_Disclosure_UnderWritter.json',
                "9000916_403":'9000916-Tax_Authorization_UnderWritter.json',
                "9000194_403":'9000194-Closing_Protection_Lette_UnderWritter.json',
                "9000246_403":'9000246-Credit_Supplement_UnderWritter.json',
                "9000364_403":'9000364-Final_Title_Policy_UnderWritter.json',
                "9000449_403":'9000449-Hazard_Insurance_Policy_classify_UW.json',
                "9000753_403":'9000753-Paystub_classify_UnderWritter.json',
                "9000802_403":'9000802-Purchase_and_Sale_agreement_UnderWritter.json',
                "9000806_403":'9000806-Rate_Lock_Agreement_UnderWritter.json',
                "9000918_403":'9000918-Tax_Certificate_UnderWritter_classify.json',
                "9000069_403":'9000069-Appraisal_Report_UnderWritter.json',
                "9001955_403":'9001955-Credit_Report_UnderWritter.json',
            },
            
            "443": {
                "9000001_443":'9000001-1003_DocVuDemo.json',
                "9000069_443":'9000069-appraisal_DocVuDemo.json',
                "9000089_443":'9000089-AUS_DU_classify_DocVuDemo.json',
                "9000115_443":'9000115-Borrower_Authorization_DocVuDemo.json',
                "9000181_443":'9000181-closing_disclosure_DocVuDemo.json',
                "9000293_443":'9000293-Escrow_Waiver_DocVuDemo.json',
                "9000301_443":'9000301-FACT_Act_Notice_To_Home_Loan_Applicant_DocVuDemo.json',
                "9000377_443":'9000377-Flood_Cert_DocVuDemo.json',
                "9000671_443":'9000671-note_DocVuDemo.json',
                "9000709_443":'9000709-Notice_Of_Right_To_Cancel_DocVuDemo.json',
                "9000869_443":'9000869-security_instrument_DocVuDemo.json',
                "9000882_443":'9000882-Signature_and_name_affidavit_DocVuDemo.json',
            },

            "440": {
                "9000001_440":'9000001-1003_classify.json',
                "9000003_440":'9000003-underwriting_summary_de.json',
                "9000181_440":'9000181-closing_disclosure_de.json',
                "9000780_440":'9000780-pmi_canc_disclosure_de.json',
                "9000069_440":'9000069-appraisal_de.json',
                "9000377_440":'9000377-flood_cert_de.json',
                "9000671_440":'9000671-note_classify.json',
                "9000253_440":'9000253-deed_of_trust_de.json',
                "9000468_440":'9000468-Home_Equity.json',
                "9002218_440":'9002218-Tax_Escrow_Option_Election.json',   

            },

            "433": {
                "9000001_433":'9000001-1003_SEQ.json',
                "9000181_433":'9000181-Closing_Disclosure_SEQ.json',
                # "9000671_433":'9000671-note_classify_SEQ.json',
                "9000671_433":'9000671-note_SEQ.json',
                # "9000671_433":'9000671-note_SEQ1.json',
                "9000377_433":'9000377-Flood_Hazard_Determination_Flood_Certificate_SEQ.json',
                "9000570_433":'9000570-Loan_Estimate_SEQ.json',
                "9000568_433":'9000568-Loan_Disbursement_Instruction_SEQ.json',
                "9000486_433":'9000486-Identification_Docuements_SEQ.json',
                "9000069_433":'9000069-appraisal_SEQ.json',
                "9000709_433":'9000709-Notice_of_Right_to_Cancel_SEQ.json',
                "9000019_433":'9000019-ACH_Authorization_classify_SEQ.json',
                "9000019_433":'9000019-ACH_Authorization_SEQ.json',
                "9000019_433":'9000019-ACH_Authorization_SEQ1.json',
                "9001043_433":'9001043-Verbal_Verification_of_Employment_VVOE_SEQ.json',
                "9000987_433":'9000987-USA_Patriot_Act_Disclosure_SEQ.json',
                "9000449_433":'9000449-Hazard_Insurance_Policy_SEQ.json',
                "9002427_433":'9002427-Mortgage_Document_SEQ.json',
                "9000007_433":'9000007-Alliant_Membership_Form_SEQ.json',
                "9000383_433":'9000383-Flood_Insurance_Policy_SEQ.json',
            },

        "sub_keys": os.path.join(de_config_path,'sub_keys.json'),
        "identifier":os.path.join(de_config_path,'stewart_policy_de.json'),
        "default": {
                "9002231": os.path.join(de_config_path,'LenderPolicy_LongForm_de.json'),
                "9002233": os.path.join(de_config_path,'LenderPolicy_ShortForm_de.json'),
                "9002236": os.path.join(de_config_path,'OwnerPolicy_de.json'),
                "9000001_new":os.path.join(de_config_path,'9000001-1003_Loan_Application_new_de.json'),
                "9000089":os.path.join(de_config_path,'9000089_aus_de.json'),
                "9000593":os.path.join(de_config_path,'9000593-LP_de.json'),
                "9000001_old":os.path.join(de_config_path,'9000001_old-1003_de.json'),
                "9000001_npf":os.path.join(de_config_path,'9000001-1003_npf_de.json'),
            }

        }

    def get_form_path(self, client_id, document_id):
        """
        Returns the specific path for a document_id based on client_id.
        Falls back to 'default' if key is not found.
        """
        client_id = str(client_id) if client_id is not None else "default"
        document_id = str(document_id)
        
        # Check if document_id already ends with "_<client_id>"; if not, append it
        if f"_{client_id}" in document_id:
            combined_key = document_id
        else:
            combined_key = f"{document_id}"

        # Get the forms mapping for the given client_id, or an empty dict if none exists
        client_forms = self.form_type_dict.get(client_id, {})
    
        if combined_key in client_forms:
            self.logger.warning(f"Key {combined_key} found for client {client_id}. Using client-specific form.") 
            return client_forms[combined_key] 
        
        self.logger.error(f"Document ID {document_id} (key: {combined_key}) not found for client {client_id} or in default.")
        return None
    
    
