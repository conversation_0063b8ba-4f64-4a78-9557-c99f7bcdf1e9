import re
from dateutil import parser as date_parser
from docvu_de_core.io import OutputFormat

class DateParserOutputFormat:
    def __init__(self, **kwargs):
        # Regex pattern to match dates in DD/MM/YYYY format
        # self.date_pattern = r"\b\d{2}/\d{2}/\d{4}\b"
        # self.date_pattern = r"(?:\D|^)(\d{2}/\d{2}/\d{4})(?:\D|$)"
        # self.date_pattern = r"(?<!\d)(\d{1,2}[-/]\d{1,2}[-/]\d{4})(?!\d)"
        # self.date_pattern = r"(?<!\d)(\d{1,2}[-/]\d{1,2}[-/](?:\d{2}|\d{4}))(?!\d)"
        # self.date_pattern = (
        #     r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)'
        #     r'\s+\d{1,2},\s+\d{4}\b'
        #     r'|(?<!\d)\d{1,2}[-/]\d{1,2}[-/](?:\d{2}|\d{4})(?!\d)'
        # )
        self.date_pattern = (
            r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)'
            r'\s+\d{1,2},\s?\d{4},?\b'  # October 26,2018 or October 26, 2018 or October 26, 2018,
            r'|(?<!\d)\d{1,2}[-/]\d{1,2}[-/](?:\d{2}|\d{4})(?!\d)'  # 26/10/2018 or 26-10-18
            r'|\b\d{1,2}-(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Sept|Oct|Nov|Dec)-\d{4}\b'  # 26-Oct-2018
            r'|\b\d{4}-\d{2}-\d{2}\b'  # 2018-10-26
            r'(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Sept|Oct|Nov|Dec)\s+\d{1,2},\s?\d{4}'
            r'|\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Sept|Oct|Nov|Dec)[A-Z]*\.?\s+\d{1,2}[.,]?\s+\d{4}\b'  # Oct 26. 2018 or Oct 26, 2018 or October 1,2051,
            r'|\b(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|SEPT|OCT|NOV|DEC)[.]?\s+\d{1,2}[.,]?\s+\d{4}\b'  # OCT 26. 2018 or OCT 26 2018
            r'|\b(?:JANUARY|FEBRUARY|MARCH|APRIL|MAY|JUNE|JULY|AUGUST|SEPTEMBER|OCTOBER|NOVEMBER|DECEMBER)\s+\d{1,2}[.,]?\s+\d{4}\b'  # DECEMBER 1 2021
            r'|\b\d{1,2}\s(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s\d{4}\b' #16 Jun 1975
            
        )

        # self.date_pattern = r"(?<!\d)(\d{1,2}[-/]\d{1,2}[-/](?:\d{2}|\d{4}))(?!\d)"
        self.date_format = kwargs.get('date_format', None)
        self.return_format = kwargs.get('return_format', None)

        # self.extract_multiple = kwargs.get('extract_multiple', True)  # Extract both dates
        self.borrower_date = kwargs.get('borrower_date', False)
        self.coborrower_date = kwargs.get('coborrower_date', False)
        self.primary_date = kwargs.get('primary_date', False)
        self.secondary_date = kwargs.get('secondary_date', False)
        self.expiration_date = kwargs.get('expiration_date', False)
        self.date_position = kwargs.get('date_position', None)
        self.purchase_contract_date = kwargs.get('purchase_contract_date', False)

    def purchase_contract_extract_date(self, text):
        # Regex to handle optional noise number like 193
        pattern = r'(\d{1,2}\s+day\s+of\s+(?:\d{3}\s+)?([A-Za-z]+)[^0-9]*(\d{4}))'

        match = re.search(pattern, text)

        if match:
            day_part = re.search(r'\d{1,2}\s+day\s+of', match.group(0)).group(0)
            month = match.group(2)
            year = match.group(3)
            result = f"{day_part} {month} , {year}"
            return result
        return text

    def parse_dates(self, text):
        """
        Extract borrower and co-borrower dates from the given text.
        """
        result = None

        if not text:
            return None
        
        # 🔁 Normalize spaced-out date-like patterns (e.g., "1 1 /1 0/2022" → "11/10/2022")
        text = re.sub(r'(?<!\d)(\d)\s+(\d)\s*/\s*(\d)\s*(\d)\s*/\s*(\d{4})(?!\d)', r'\1\2/\3\4/\5', text)
        text = re.sub(r'(?<!\d)(\d{1,2})\s*/\s*(\d{1,2})\s*/\s*(\d{4})(?!\d)', r'\1/\2/\3', text)
        text = re.sub(r'(?<!\d)(\d{1,2})\s*-\s*(\d{1,2})\s*-\s*(\d{4})(?!\d)', r'\1-\2-\3', text)

        if self.purchase_contract_date:
            date_text = self.purchase_contract_extract_date(text)
            return date_text
        
        # Find all dates in the text
        pattern = self.date_format if self.date_format else self.date_pattern
        dates = re.findall(pattern, text)
        print("Extracted Dates:", dates)

        # NEW: Extract based on date_position (1-based index)
        if self.date_position is not None:
            index = self.date_position - 1  # Convert to 0-based index
            return dates[index] if len(dates) > index else None

        if self.borrower_date or self.primary_date:
            result = dates[0] if len(dates) > 0 else None

        if self.coborrower_date or self.secondary_date:
            result = dates[1] if len(dates) > 1 else None

        # Below logic has been implementated for 9002148 - Identification Verification (Patriot Act) subtype
        # for 3000774 - patriot_act_identification_borrower_id1_expiration_date field  
        if self.expiration_date:
            if len(dates) == 1:
                result =  dates[0]
            elif len(dates) == 2:
                result = dates[1]
            else:
                result = None


        return result

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Execute the date parsing operation on the given input.
        :param processed_output: Dictionary containing 'value' which is the input text to be processed.
        :param extraction_items: Additional fields for potential extraction if needed.
        :return: Processed output with the extracted dates.
        """
        success = False
        date = None

        # Extract the text to be processed
        text = processed_output.get('value', '')

        if not text:  # Ensure text is not empty or None
            text = ''

        # Strip any leading/trailing spaces
        text = text.strip()

        # Extract dates
        date = self.parse_dates(text)
        
        if self.return_format and date:
            try:
                parsed_date = date_parser.parse(date)
                date = parsed_date.strftime(self.return_format)
            except Exception:
                pass  # Keep the original date if formatting fails

        if date:
            success = True

       
        processed_output['value'] = date
        processed_output['post_processing_value'] = processed_output['value']

        return OutputFormat(item=processed_output, success=success, multi_item=False)

# Example usage
if __name__ == "__main__":
    # input_text = """
    # 167FG Rccect 4/07/2022 06:03:45 PM MDT - BORROWER - Lawrence G Rocco - DATE - 736828EE4id9Ec1 04/07/2022 06:25:15 PM MDT - BORROWER - Barbara J Rocco - DATE -
    # """

    # # Initialize the date parser
    # date_parser = DateParserOutputFormat(extract_multiple=True)

    # # Parse the dates from input text
    # result = date_parser({'value': input_text})
    # print(f"Extracted Dates: {result.item['value']}")


    # input_text = """
    # Gross Pay Paycheck Information Wayne J Messenger Pay Period Start Date Pay Period End Date Issuance Date 
    # Voucher Gross Amount Net Amount 10/30/2022 11/05/2022 11/10/2022 3645071 987.64 638.75
    # """

    # input_text = """
    # I will pay principal and interest by making payments each month of U.S. $63.74. I will make my payments on the 1st day of each month beginning on February 1, 2023. I will make these payments every month 
    # until I have paid all of the principal and interest and any other charges, described below, that I may owe under this Note. If, on January 1, 2033, I still owe amounts under this Note, I will pay all those amounts, 
    # in full, on that date. I will make my monthly payments at Essex Mortgage ATTN: Cashiering 2100 SE 17th Street #112 Ocala, FLORIDA 34471 or at a different place if required by the Note Holde
    # """

    # input_text = """Voucher Gross Amount Net Amount 10/30/2022 11/05/2022 11/10/2022 3645071 987.64 638.75"""

    input_text = """OCT 26 2018 to OCT 26 2019"""

    # Extract the 3rd date (e.g., Issuance Date)
    date_parser = DateParserOutputFormat(date_position=3)
    result = date_parser({'value': input_text})
    print(f"Extracted 3rd Date: {result.item['value']}")