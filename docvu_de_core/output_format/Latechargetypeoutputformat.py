import re
from docvu_de_core.io import OutputFormat

class Latechargetypeoutputformat:
    def __init__(self, from_field=None, **kwargs):
        self.from_field = from_field
        self.get_percentage = kwargs.get('get_percentage', False)  # Option to extract percentage
        self.get_amount = kwargs.get('get_amount', False)  # Option to extract amount
        # Refined regex patterns
        self.contains_percentage_pattern = r"\b\d+\s*\.\s*\d{1,4}(\s*\d{0,4})*\s*%" # More specific percentage pattern
        self.contains_amount_pattern = r"\b\d+(\.\d{1,3})?\b(?!\s?%)"  # Amount pattern, not followed by '%'

    def parse_percentage(self, text):
        """Check if the text contains a percentage."""
        print(f"Parsing for percentage in text: '{text}'")  # Debugging line with quotes to show spaces
        if not text:
            return ''
        match = re.search(self.contains_percentage_pattern, text)
        if match:
            print(f"Found percentage match: {match.group()}")  # Debugging line to show matched value
            return "PERCENT"
        return ''

    def parse_amount(self, text):
        """Check if the text contains a numeric amount."""
        print(f"Parsing for amount in text: '{text}'")  # Debugging line with quotes to show spaces
        if not text:
            return ''
        match = re.search(self.contains_amount_pattern, text)
        if match:
            print(f"Found amount match: {match.group()}")  # Debugging line to show matched value
            return "AMOUNT"
        return ''

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Execute the configured parsing operations on the given input.
        :param processed_output: Dictionary containing 'value' which is the input text to be processed.
        :param extraction_items: Additional fields for potential extraction if needed.
        :return: Processed output with the extracted value.
        """
        # Extracting the text to be processed
        text = processed_output.get('value', '')

        if self.from_field and extraction_items:
            for item in extraction_items:
                if item['name'] == self.from_field:
                    text = item['value']
                    break

        if not text:  # Ensure text is not empty or None
            text = ''

        # Strip any leading/trailing spaces for more accurate matching
        text = text.strip()

        print(f"Final text to process: '{text}'")  # Debugging line with quotes to show spaces
        
        output_value = ''
        success = False

        # If percentage is required, prioritize percentage detection first
        if self.get_percentage:
            output_value = self.parse_percentage(text)
        
        # If percentage was not found, check for amount
        if not output_value and self.get_amount:
            output_value = self.parse_amount(text)

        success = bool(output_value)

        processed_output['value'] = output_value
        processed_output['post_processing_value'] = output_value

        return OutputFormat(item=processed_output, success=success, multi_item=False)


# # Example usage for percentage and amount detection:
# late_charge_parser = LateChargeTypeOutputFormat(get_percentage=True, get_amount=True)
# result = late_charge_parser({
#     'value': "A) Late Charge for Overdue Payments If the Note Holder has not received the full amount of any monthly payment by the end of 15 calendar days after the date it is due, I will pay a late charge to the Note Holder. The amount of the charge will be 3.000 % of the overdue payment of Principal and Interest (P&I). I will pay this late charge promptly but only once on each late payment."
# })
# print(result)  # Expected output: 'PERCENT'

# # Example usage for amount and percentage detection with variations in spacing:
# late_charge_parser = LateChargeTypeOutputFormat(get_percentage=True, get_amount=True)
# result2 = late_charge_parser({
#     'value': "A) Late Charge for Overdue Payments If the Note Holder has not received the full amount of any monthly payment by the end of 15 calendar days after the date it is due, I will pay a late charge to the Note Holder. The amount of the charge will be 3.000% of the overdue payment of Principal and Interest (P&I). I will pay this late charge promptly but only once on each late payment."
# })
# print(result2)  # Expected output: 'PERCENT'

# # Example usage for amount detection:

# # Example usage for amount detection:
# late_charge_parser = Latechargetypeoutputformat(get_percentage=True, get_amount=True)
# result3 = late_charge_parser({'value': "A) Late Charge for Overdue Payments If the Note Holder has not received the full amount of any monthly payment by the end of 15 calendar days after the date it is due, I will pay a late charge to the Note Holder. The amount of the charge will be 3.000 of the overdue payment of Principal and Interest (P&I). I will pay this late charge promptly but only once on each late payment."})

