#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""

from sympy import false
from docvu_de_core.io import OutputFormat
from docvu_de_core.address_parser.us_address_parser import USAddress<PERSON>arser
from docvu_de_core.config.us_address_map import valid_states, state_code_name
import re



class AddressParserOutputFormat:
    def __init__(self, from_field=None, **kwargs):
        """
        Initialize AddressParserOutputFormat with various address processing options.

        Supported arguments:
        - from_field: Specify the field to extract the address from.
        - get_line1: Boolean to extract address line 1.
        - get_line1_and_2: Boolean to extract address line 1 and line 2.
        - get_line2: Boolean to extract address line 2.
        - get_city: Boolean to extract the city.
        - get_state: Boolean to extract the state.
        - get_zip_code: Boolean to extract the zip code.
        - get_po_box: Boolean to extract the P.O. Box.

        Only one extraction option should be set to True at a time.
        """
        self.from_field = from_field
        self.get_line1 = kwargs.get('get_line1', False)
        self.get_line2 = kwargs.get('get_line2', False)
        self.get_line1_and_line2 = kwargs.get('get_line1_and_line2', False)
        self.get_city = kwargs.get('get_city', False)
        self.get_state = kwargs.get('get_state', False)
        self.get_zip_code = kwargs.get('get_zip_code', False)
        self.get_po_box = kwargs.get('get_po_box', False)
        self.get_line1_and_city = kwargs.get('get_line1_and_city', False)
        self.get_line1_line2_city_state_zip = kwargs.get('get_line1_line2_city_state_zip', False)
        self.get_state_zip_code = kwargs.get('get_state_zip_code', False)
        self.get_line1_line2_city = kwargs.get('get_line1_line2_city',False)
        self.get_recipient = kwargs.get('get_recipient', False)
        self.parser = USAddressParser()


    def remove_special_chars(self, input_string):
        """
        Remove special characters from the input string, leaving only alphanumeric characters.

        :param input_string: The string from which to remove special characters.
        :return: A cleaned string with special characters removed.
        """
        if input_string is None:
            return None

        # Use regular expression to remove special characters
        cleaned_string = re.sub(r'[^a-zA-Z0-9\s]', '', input_string)

        # Return the cleaned string
        return cleaned_string
    
    def extract_street_address(self, text):
        """Extract and return only the street address (ignoring duplicate segments like city and ZIP code)."""
        if not text:
            return ''

        # Parse the address using the address parser
        address = self.parser.parse_text(text)
        line1 = address.get("address_line_1", "")
        line2 = address.get("address_line_2", "")
        city = address.get("city", "")
        state = address.get("state", "")
        zip_code = address.get("zip_code", "")

        # If line1 (street address) is found, return it
        if line1:
            return line1.strip()
        else:
            return ""

    def parse_line1_and_2(self, text):
        """Extract and return address line 1 and line 2 together."""
        if not text:
            return ''

        address = self.parser.parse_text(text)
        line1 = address.get("address_line_1", "")
        line2 = address.get("address_line_2", "")

        if not line1:
            line1 = ''
        if not line2:
            line2 = ''
        return line1 + " " + line2

    def parse_line1_and_city(self, text):
        """Extract and return address line 1 and line 2 together."""
        if not text:
            return ''

        address = self.parser.parse_text(text)
        line1 = address.get("address_line_1", "")
        city = address.get("city", "")

        if not line1:
            line1 = ''
        if not city:
            city = ''
        return line1 + " " + city

    def parse_line1_line2_city_state_zip(self, text):
        """Extract and return address line 1, line 2, city, state, and zip code based on the flag."""
        if not text:
            return ''

        address = self.parser.parse_text(text)
        line1 = address.get("address_line_1", "")
        line2 = address.get("address_line_2", "")
        city = address.get("city", "")
        state = address.get("state", "")
        zip_code = address.get("zip_code", "")

        if not line1:
            line1 = ''
        if not line2:
            line2 = ''
        if not city:
            city = ''
        if not state:
            state = ''
        if not zip_code:
            zip_code = ''

        if not line1.strip().endswith(',') and line1 != "":
            line1 += ','
        if not line2.strip().endswith(',') and line2 != "":
            line2 += ','

        if not city.strip().endswith(',') and city != "":
            city += ','

        return line1 + " " + line2 + " " + city + " " + state + " " + zip_code
    

    def parse_line1(self, text):
        """Extract and return address line 1."""
        if not text:
            return ''

        address = self.parser.parse_text(text)  # returns dict

        return address.get("address_line_1", "")


    def parse_line2(self, text):
        """Extract and return address line 2."""
        if not text:
            return ''

        address = self.parser.parse_text(text)
        return address.get("address_line_2", "")

    def parse_city(self, text):
        """Extract and return the city."""
        if not text:
            return ''

        address = self.parser.parse_text(text)

        probable_city = self.remove_special_chars(address.get("city", ""))

        # Fallback if city is not extracted
        if not probable_city:
            # Try extracting city using known state names as anchors
            for abbr, full_name in state_code_name.items():
                pattern = rf'([A-Za-z\s]+?)\s*(?:\[.*?\])*\s+{re.escape(full_name)}\b'
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    probable_city = match.group(1).strip()
                    break

        if not probable_city:
            return ''

        probable_city = probable_city.strip()
        return probable_city


    def parse_state(self, text):
        """Extract and return the state."""
        if not text:
            return ''

        address = self.parser.parse_text(text)
        probable_state = self.remove_special_chars(address.get("state", ""))

        # Fallback if parser fails to extract state
        if not probable_state:
            # Search for any US state name or abbreviation in raw text
            for abbr, full_name in state_code_name.items():
                if re.search(r'\b' + re.escape(full_name) + r'\b', text, re.IGNORECASE):
                    probable_state = full_name
                    break
                elif re.search(r'\b' + re.escape(abbr) + r'\b', text, re.IGNORECASE):
                    probable_state = abbr
                    break

        if not probable_state:
            return ''

        probable_state = probable_state.strip()

        # Remove duplicates
        probable_state_list = list(set(probable_state.split()))  # Remove duplicates
        probable_state = " ".join(probable_state_list) # Join back into a string

        # Combine abbreviations and full names into one list
        valid_state_names = list(state_code_name.values())
        valid_state_abbreviations = list(state_code_name.keys())

        # Validate state
        valid_state = False
        # Check if probable_state matches an abbreviation or full state name
        for abbr, full_name in state_code_name.items():
            if probable_state.lower() == abbr.lower(): # Check for abbreviation match
                valid_state = True
                probable_state = abbr # Replace abbreviation with full name
                break
            elif probable_state.lower() == full_name.lower(): # Check for full name match
                valid_state = True
                probable_state = full_name # Ensure the correct full name is assigned
                break
            elif full_name.lower() in probable_state.lower(): # Handle partial match
                valid_state = True
                probable_state = full_name # Assign full name if it's part of probable_state
                break

        return probable_state if valid_state else ''

    def parse_zip_code(self, text):
        """Extract and return the zip code."""
        if not text:
            return ''
        
        address = self.parser.parse_text(text)

        # Below code has been added on May 26 2025 by Harsh to handle multiple zip code
        # It will return only first zip code if there are multiple.
        # Original logic preserved
        zip_code = address.get("zip_code", "")
        
        # If parser extracted multiple zip codes, return only the first one
        if zip_code and len(zip_code.split()) > 1:
            return zip_code.split()[0]
        
        if zip_code:
            return zip_code

        # Fallback: Try to extract using regex from the raw text
        match = re.search(r'\b\d{5}(?:-\d{4})?\b', text)
        if match:
            return match.group(0)

        return ''

    def parse_po_box(self, text):
        """Extract and return the P.O. Box."""
        if not text:
            return ''

        address = self.parser.parse_text(text)
        return address.get("p.o.box", None)

    
    def parse_recipient(self, text):
        """
        Extract and return the recipient's name from the address text.

        Assumes the recipient is the part before the actual address starts, typically the first line.
        """
        if not text:
            return ''
        
        address = self.parser.parse_text(text)
        recipient = address.get("recipient", "")
        
        if not recipient:
            # If no specific recipient field found, extract name from the start of the string
            lines = text.split('\n')
            recipient = lines[0].strip() if lines else ''
        
        return recipient
    
    def parse_state_zip_code(self, text):
        """
        Extract and return the state and zip code from the input text.

        :param text: The input address text.
        :return: A string containing the state and zip code separated by a space.
        """
        if not text:
            return ''

        address = self.parser.parse_text(text)
        
        # Extract state and zip code, and ensure they are strings, not None
        state = self.remove_special_chars(address.get("state", "")).strip() if address.get("state") else ''
        zip_code = self.remove_special_chars(address.get("zip_code", "")).strip() if address.get("zip_code") else ''

        # Validate state
        valid_states_codes = list(state_code_name.keys())
        valid_state = False
        for vs in valid_states_codes:
            if state.lower() in vs.lower():
                valid_state = True
                break
            if vs.lower() in state.lower():
                valid_state = True
                state = vs
                break

        # Combine state and zip code
        state = state if valid_state else ''
        return f"{state} {zip_code}".strip()

    def parse_line1_line2_city(self, text):
        """Extract and return address line 1, line 2, and city, omitting state and ZIP code."""
        if not text:
            return ''

        address = self.parser.parse_text(text)
        line1 = address.get("address_line_1", "")
        line2 = address.get("address_line_2", "")
        city = address.get("city", "")

        if not line1:
            line1 = ''
        if not line2:
            line2 = ''
        if not city:
            city = ''

        if not line1.strip().endswith(',') and line1 != "":
            line1 += ','
        if not line2.strip().endswith(',') and line2 != "":
            line2 += ','

        return f"{line1} {line2} {city},".strip()
        
    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Execute the configured address parsing operations on the given input.

        :param processed_output: Dictionary containing 'value' which is the input address string.
        :param extraction_items: Additional fields for potential extraction if needed.
        :return: OutputFormat with the processed address and related information.
        """
        # Determine which text to use based on from_field or default to processed_output['value']
        text = processed_output.get('value', '')
        if self.from_field and extraction_items:
            for item in extraction_items:
                if item['name'] == self.from_field:
                    text = item['value']
                    break

        if text is None:
            text = ''
            
        # Preprocess the text: join multiline addresses into a single line
        text = ' '.join(text.splitlines()).strip()
        original_text = text
        output_value = ''
        success = False

        # Determine which parsing operation to use based on initialization parameters
        if self.get_line1:
            output_value = self.parse_line1(text)
        elif self.get_line2:
            output_value = self.parse_line2(text)
        elif self.get_line1_and_line2:
            output_value = self.parse_line1_and_2(text)
        elif self.get_city:
            output_value = self.parse_city(text)
        elif self.get_state:
            output_value = self.parse_state(text)
        elif self.get_zip_code:
            output_value = self.parse_zip_code(text)
        elif self.get_po_box:
            output_value = self.parse_po_box(text)
        elif self.get_line1_and_city:
            output_value = self.parse_line1_and_city(text)
        elif self.get_line1_line2_city_state_zip:
            output_value = self.parse_line1_line2_city_state_zip(text)
        elif self.get_line1_line2_city:  
            output_value = self.parse_line1_line2_city(text)
        elif self.get_state_zip_code:  
            output_value = self.parse_state_zip_code(text)  
        elif self.get_recipient:
            output_value = self.parse_recipient(text)
        else:
            raise ValueError("No valid parsing operation specified. Please set one option to True.")

        success = bool(output_value)

        processed_output['value'] = output_value
        processed_output['post_processing_value'] = output_value

        return OutputFormat(item=processed_output, success=success, multi_item=false)


# Example usage:
if __name__ == "__main__":
    input_text = {
        'value': "14855 North 100TH Way Scottsdale, AZ 85260 Scottsdale, AZ 85260"
    }

    extraction_items = [
        {'name': 'address_field', 'value': "14855 North 100TH Way Scottsdale, AZ 85260 Scottsdale, AZ 85260"}
    ]

    # Instantiate the address parser output format for line 1
    address_operations_line1 = AddressParserOutputFormat(from_field='address_field', get_line1=True)

    # Process the input text
    result_line1 = address_operations_line1(processed_output=input_text, extraction_items=extraction_items)

    # Print the result for line 1
    print("Result for Line 1:")
    print(result_line1.item['value'])

    # Instantiate the address parser output format for line 2
    address_operations_line2 = AddressParserOutputFormat(from_field='address_field', get_line2=True)

    # Process the input text
    result_line2 = address_operations_line2(processed_output=input_text, extraction_items=extraction_items)

    # Print the result for line 2
    print("Result for Line 2:")
    print(result_line2.item['value'])

    # Similarly, instantiate and test for other components like city, state, zip_code, and p.o.box