#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""


from docvu_de_core.io import OutputFormat
from docvu_de_core.modules.DateFinder import DateFinder
import re
from datetime import datetime

class DateFinderOutputFormat:
    def __init__(self, return_format="%m/%d/%Y", **kwargs):
        self.return_format = return_format
        self.df = DateFinder(return_format=self.return_format)

    def is_valid_date(self, date_str):
        try:
            # Try to parse the date string using the dd/mm/yyyy format
            datetime.strptime(date_str, self.return_format)
            return True
        except ValueError:
            # If a ValueError is raised, the format is incorrect
            return False

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Execute the configured text processing operations on the given string.

        :param processed_output: Dictionary containing 'value' which is the input string.
        :return: OutputFormat with the processed text and related information.
        """
        text = processed_output['post_processing_value']
        if text is None:
            text = ''
        if isinstance(text, list):
            text = " ".join(text)
        original_text = text
        success = False

        if not self.is_valid_date(text):
            text = self.df.get_dates_in_text(text, return_first=True, return_position_info=False)
            
            if text is None:
                processed_output['value'] = text

            processed_output['post_processing_value'] = text   
            success = True

        return OutputFormat(item=processed_output, success=success, multi_item=False)

# Example usage:
if __name__ == "__main__":
    input_text = "Policy No.: M-9302-00564056 2"

    string_operations = StringOperationsOutputFormat(
        remove_from_beginning=["Policy No."],
        remove_from_beginning_ignore_case=True,
        remove_from_end=["2"],
        remove_from_end_ignore_case=True,
        contains=["M-9302"],
        contains_ignore_case=True,
        remove_special_chars_from_beginning=True,
        remove_special_chars_from_end=True,
        remove_spaces=True,
        starts_with=["Policy"],
        starts_with_ignore_case=True,
        ends_with=["00564056 2"],
        ends_with_ignore_case=True,
        remove_till_from_end=["00564056"],
        remove_till_from_end_ignore_case=True,
        remove_till_return_default_if_not_found=True,
        remove_non_digit=True
    )

    result = string_operations({'value': input_text})
    print("Result:")
    print(result.item['value'])