#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""
from docvu_de_core.io import OutputFormat
from copy import deepcopy

class ConcatExtractionItemsOutputFormat:
    """
    Class to process and format extracted output based on specified extraction items.

    Attributes:
        from_field (str): The field name to extract data from.
        get_field1_and_field2 (bool): Flag to determine if additional processing is required.
    """
    def __init__(self, from_field=None, **kwargs):
        """
        Initializes the ConcatExtractionItemsOutputFormat instance.

        Args:
            from_field (str, optional): The field name to extract data from.
            **kwargs: Additional keyword arguments, such as 'get_field1_and_field2'.
        """
        self.from_field = from_field
        self.get_field1_and_field2 = kwargs.get("get_field1_and_field2", False)

    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Processes the output and returns formatted results.

        Args:
            processed_output (dict): The output dictionary to be processed.
            extraction_items (list, optional): List of extraction items to search for data.
            **kwargs: Additional keyword arguments.

        Returns:
            OutputFormat: The formatted output with the success status.

        Raises:
            ValueError: If no valid parsing operation is specified.
        """
        value = []  # Initialize as an empty list

        # Validate and process extraction items
        if self.from_field and extraction_items:
            for item in extraction_items:
                if item.get("name") == self.from_field:
                    value = deepcopy(item.get("rows", []))

                    # Modify each column's 'id' and 'name' in the extracted rows
                    if value:
                        for entry in value:
                            for column in entry.get("columns", []):
                                column["id"] = 80014060
                                column["name"] = "Endorsement_Type"
                    break

        # Ensure value is always a list
        if not isinstance(value, list):
            value = []

        # Initialize success flag
        success = False

        # Append rows to processed_output based on the flag
        if self.get_field1_and_field2:
            if isinstance(processed_output.get("rows"), list):
                processed_output["rows"].extend(value)
            else:
                processed_output["rows"] = value

            success = True
        else:
            raise ValueError("No valid parsing operation specified. Set 'get_field1_and_field2' to True.")

        return OutputFormat(item=processed_output, success=success, multi_item=False)
