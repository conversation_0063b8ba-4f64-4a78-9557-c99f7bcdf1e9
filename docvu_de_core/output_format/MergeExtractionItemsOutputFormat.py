#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""
from docvu_de_core.io import OutputFormat
from copy import deepcopy


class MergeExtractionItemsOutputFormat:
    """
    Merges values from specified fields in `extraction_items` into a single string,
    separated by a given delimiter, and updates `processed_output` accordingly.
    """

    def __init__(self, **kwargs):
        """
        Args:
            from_fields (List[str]): List of field names to extract values from.
            merge_with (Union[str, List[str]]): Delimiter to use for merging values.
                                                If a list is provided, only the first item is used.
        """
        self.from_fields = kwargs.get("from_fields", [])
        self.merge_with = kwargs.get("merge_with", " ")

        # If merge_with is a non-empty list, take the first element
        if isinstance(self.merge_with, list) and self.merge_with:
            self.merge_with = str(self.merge_with[0])


    def __call__(self, processed_output, extraction_items=None, **kwargs):
        """
        Merges values from `from_fields` in `extraction_items` and updates `processed_output`.

        Args:
            processed_output (dict): Dictionary to be updated with the merged value.
            extraction_items (List[dict]): List of extraction result items.

        Returns:
            OutputFormat: Wrapped output object with updated result and success status.
        """
        try:
            if not isinstance(extraction_items, list) or not self.from_fields:
                return OutputFormat(item=processed_output, success=False, multi_item=False)

            merged_values = []

            for from_field in self.from_fields:
                for item in extraction_items:
                    if not isinstance(item, dict):
                        continue

                    if item.get("name") == from_field:
                        value = deepcopy(item.get("post_processing_value", ""))

                        if isinstance(value, list):
                            # Convert all elements to string before extending
                            merged_values.extend([str(v) for v in value if v is not None])
                        elif value is not None:
                            merged_values.append(str(value))

            if merged_values:
                result_value = self.merge_with.join(merged_values).strip()
                processed_output["post_processing_value"] = result_value
                success = True
            else:
                return OutputFormat(item=processed_output, success=False, multi_item=False)

            return OutputFormat(item=processed_output, success=success, multi_item=False)

        except Exception as e:
            # Log the error or handle as needed
            print(f"[MergeExtractionItemsOutputFormat Error] {e}")
            return OutputFormat(item=processed_output, success=False, multi_item=False)