import requests

class ModelServiceResponse:
    def __init__(self, server_url, end_point):
        self.server_url = server_url
        self.end_point = end_point

    def send_request_to_ner(self, text, label):
        """ Sends a request to the NER API """
        print('Sending request to NER')

        # Prepare payload
        payload = {
            "text": text,
            "label": label
        }

        # Define the API endpoint
        url = f"{self.server_url}/{self.end_point}/"
        try:
            # Send the POST request
            response = requests.post(url, data=payload, timeout=5)
            return response
        except Exception as e:
            print(f"Error while sending request: {e}")
            return None

if __name__ == "__main__":
    # Define the server URL
    server_url = "http://127.0.0.1:8000"
    end_point = "predict-ner"
    # server_url = server_url

    # Initialize the ModelServiceResponse class
    model_infer = ModelServiceResponse(server_url, end_point)

    # Define input data
    field_id = "12345"
    text = "Nova Title Company is located in Texas."
    label = "organization"

    # Send request
    response = model_infer.send_request_to_ner(text, label)
    if response:
        # Print response
        print("Status Code:", response.status_code)
        print("Response JSON:", response.json())
