import os
import math
from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.modules import ExtractLbsdObjects
from docvu_de_core.utils import ElementUtils
import cv2

class CheckForSignaturePostProcessor:
    """
    A post-processor class to detect signatures in images and optionally check their closeness
    to specific bounding box regions.

    Attributes:
        logger (object): Logger instance for logging information and errors.
        check_closeness (bool): Whether to check the proximity of detected signatures to provided elements.
        return_cropped_regions (bool): Whether to return cropped regions of detected signatures.
        distance_threshold (int): Maximum allowed distance between a detected signature and a bounding box to consider them close.
        extract_signature (ExtractSignature): Instance of the ExtractSignature module for signature detection.
        
    """

    def __init__(self, logger, check_closeness=False, return_cropped_regions=False, distance_threshold=100, 
                 sign_bbox_direction="right", vertical_threshold=50, horizontal_threshold=50,
                 signature_present=False,closing_instruction_coborrower_sign=False, class_id=0, class_labels=None):
        """
        Initialize the CheckForSignaturePostProcessor.

        Parameters:
            logger (object): Logger instance for logging.
            check_closeness (bool): Flag to enable closeness checks for detected signatures.
            return_cropped_regions (bool): Flag to enable returning cropped regions of detected signatures.
            distance_threshold (int): Distance threshold for closeness checks.
            class_id (int): The numeric ID representing the object class to detect (default is 0 for "seal").
            class_labels (str): The label/name of the object class to detect (default is "signature").
        """
        self.logger = logger
        self.extract_yolo_objects = ExtractLbsdObjects(logger)
        self.check_closeness = check_closeness
        self.return_cropped_regions = return_cropped_regions
        self.distance_threshold = distance_threshold
        self.sign_bbox_direction = sign_bbox_direction
        self.vertical_threshold = vertical_threshold
        self.horizontal_threshold = horizontal_threshold
        self.signature_present = signature_present
        self.closing_instruction_coborrower_sign = closing_instruction_coborrower_sign
        self.class_id = class_id
        
        # Determine class_labels automatically if not provided
        if class_labels is not None:
            self.class_labels = class_labels
        else:
            # Get class_labels from the label_map in ExtractLbsdObjects
            self.class_labels = None #this line goes to init
            
            # Direct lookup from the label_map dictionary
            clabel = ExtractLbsdObjects.label_map.get(self.class_id)

            if clabel:  # This checks for None and empty string
                self.class_labels = f"{clabel}"
            else:
                self.logger.error(f"class_id '{self.class_id}' not found or is empty in label map")
                self.class_labels = f"signature {self.class_id}"  # fallback

    def bbox_distance(self, bbox1, bbox2):
        """
        Calculate the Euclidean distance between the centers of two bounding boxes.

        Parameters:
            bbox1 (tuple): (x1, y1, x2, y2) coordinates for the first bounding box.
            bbox2 (tuple): (x1, y1, x2, y2) coordinates for the second bounding box.

        Returns:
            int: The Euclidean distance between the centers of the two bounding boxes.
        """
        # Calculate the center of the first bounding box
        center1_x = (bbox1[0] + bbox1[2]) / 2
        center1_y = (bbox1[1] + bbox1[3]) / 2

        # Calculate the center of the second bounding box
        center2_x = (bbox2[0] + bbox2[2]) / 2
        center2_y = (bbox2[1] + bbox2[3]) / 2

        # Compute the Euclidean distance
        distance = math.sqrt((center2_x - center1_x)**2 + (center2_y - center1_y)**2)
        return int(distance)

    def check_bbox_consider(self, text_bbox, sign_bbox):
        consider_bbox = False
        if self.sign_bbox_direction == "left":
            if abs(text_bbox[1] - sign_bbox[1]) <= self.vertical_threshold:
                if sign_bbox[0] < text_bbox[0]:
                    consider_bbox = True
        elif self.sign_bbox_direction == "right":
            if abs(text_bbox[1] - sign_bbox[1]) <= self.vertical_threshold:
                if self.closing_instruction_coborrower_sign:
                    if abs(text_bbox[0] - sign_bbox[0]) > 300:
                        consider_bbox = True
                elif text_bbox[0] < sign_bbox[0]:
                    consider_bbox = True
        elif self.sign_bbox_direction == "up":
            if abs(text_bbox[0] - sign_bbox[0]) <= self.horizontal_threshold:
                if sign_bbox[1] < text_bbox[1]:
                    consider_bbox = True
        elif self.sign_bbox_direction == "down":
            if abs(text_bbox[0] - sign_bbox[0]) <= self.horizontal_threshold:
                if text_bbox[1] < sign_bbox[1]:
                    consider_bbox = True
        
        return consider_bbox
    
    def get_signature_bbox(self, model_bbox):
        """
        Computes the bounding box for a signature based on the model's detected coordinates.

        Parameters:
        - model_bbox (dict): A dictionary containing bounding box coordinates with keys:
            - 'x1' (int): The x-coordinate of the top-left corner.
            - 'y1' (int): The y-coordinate of the top-left corner.
            - 'x2' (int): The x-coordinate of the bottom-right corner.
            - 'y2' (int): The y-coordinate of the bottom-right corner.
        Returns:
        - dict: A dictionary with the computed bounding box:
            - 'x' (int): x-coordinate of the top-left corner.
            - 'y' (int): y-coordinate of the top-left corner.
            - 'width' (int): Width of the bounding box.
            - 'height' (int): Height of the bounding box.
        """
        bbox = {
            'x': model_bbox['x1'],  # Top-left x-coordinate
            'y': model_bbox['y1'],  # Top-left y-coordinate
            'width': model_bbox['x2'] - model_bbox['x1'],  # Compute width
            'height': model_bbox['y2'] - model_bbox['y1']  # Compute height
        }
        return bbox  # Return the formatted bounding box


    def __call__(self, image_path=None, **kwargs):
        """
        Process the provided image to detect signatures and optionally check their closeness to specified elements.

        Parameters:
            image_path (str): Path to the input image for signature detection.
            **kwargs: Additional keyword arguments including elements.

        Returns:
            PostProcessOutputFormat: Contains the result of the signature detection and closeness check.
        """
        # Retrieve elements from keyword arguments
        elements = kwargs.get("elements", [])
        if isinstance(elements, dict):
            elements = [elements]

        value = "No"
        print("default value of signature is {}".format(value))
        success = False
        bbox = None
        line_number = None

        # Ensure an image path is provided
        if not image_path:
            self.logger.error("No image_path provided for signature detection.")
            return PostProcessOutputFormat(
                value="No image path provided.",
                success=False,
                elements=elements, 
                bbox=bbox,
                line_number=line_number
            )

        # Set output directory based on image path
        self.output_dir = os.path.basename(image_path)
        
        # Extract signature details from the image
        object_details = self.extract_yolo_objects.extract_object_details( 
            image_path=image_path,
            output_dir=self.output_dir,
            return_cropped_regions=self.return_cropped_regions,
            class_id = self.class_id,
            class_labels = self.class_labels
        )
 
        # Check closeness if enabled
        if self.check_closeness:
            if object_details.class_labels.count("signature") >= 2:
                value = "Yes"
                success = True
                bbox = self.get_signature_bbox(object_details.bounding_boxes[1])
        
        elif self.signature_present:
            if elements:
                element_bbox, line_number = ElementUtils.get_bbox_and_line_number(elements)

                # Compare distances between detected signatures and provided elements
                for sign_coordinates in object_details.bounding_boxes:
                    sign_bbox = (
                        sign_coordinates['x1'], sign_coordinates['y1'],
                        sign_coordinates['x2'], sign_coordinates['y2']
                    )
                    # distance = self.bbox_distance(element_bbox, sign_bbox)

                    sign_conside_status = self.check_bbox_consider(element_bbox, sign_bbox)

                    if sign_conside_status:
                        value = "Yes"
                        success = True
                        bbox = self.get_signature_bbox(sign_coordinates)
                        
        else:    
            # Check if any signature is detected
            if "signature" in object_details.class_labels:
                value = "Yes"
                success = True
                bbox = self.get_signature_bbox(object_details.bounding_boxes[0])

        print("Updated value of signature is {}".format(value))
        # Return default output format if no signatures are detected
        return PostProcessOutputFormat(
            value=value,
            success=success,
            elements=elements,
            bbox = bbox,
            line_number = line_number

        )