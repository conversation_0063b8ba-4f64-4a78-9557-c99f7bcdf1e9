import os
import math
from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils import ElementUtils
from docvu_de_core.modules import ExtractLbsdObjects

class CheckForSealPostProcessor:
    """
    A post-processor class to detect seals in images and optionally check their closeness
    to specific bounding box regions.

    Attributes:
        logger (object): Logger instance for logging information and errors.
        check_closeness (bool): Whether to check the proximity of detected seals to provided elements.
        return_cropped_regions (bool): Whether to return cropped regions of detected seals.
        distance_threshold (int): Maximum allowed distance between a detected seal and a bounding box to consider them close.
        extract_seal (ExtractSeal): Instance of the ExtractSeal module for seal detection.
    """

    def __init__(self, logger, return_cropped_regions=False, seal_appeared_before=False, class_id=4, class_labels=None):
        """
        Initialize the CheckForSealPostProcessor.

        Parameters:
            logger (object): Logger instance for logging.
            return_cropped_regions (bool): Flag to enable returning cropped regions of detected seals.
            seal_present (bool): Flag indicating if a seal is present.
            class_id (int): The numeric ID representing the object class to detect (default is 4 for "seal").
            class_labels (str): The label/name of the object class to detect (default is "seal").
        """
        self.logger = logger  # Logger instance for error handling and debugging
        self.extract_yolo_objects = ExtractLbsdObjects(logger) # Initialize ExtractSeal module for detection
        self.return_cropped_regions = return_cropped_regions  # Flag for returning cropped seal regions
        self.seal_appeared_before = seal_appeared_before
        self.class_id = class_id
        
        # Determine class_labels automatically if not provided
        if class_labels is not None:
            self.class_labels = class_labels
        else:
            # Get class_labels from the label_map in ExtractLbsdObjects
            self.class_labels = None #this line goes to init
            
            # Direct lookup from the label_map dictionary
            clabel = ExtractLbsdObjects.label_map.get(self.class_id)

            if clabel:  # This checks for None and empty string
                self.class_labels = f"{clabel}"
                
            else:
                self.logger.error(f"class_id '{self.class_id}' not found or is empty in label map")
                self.class_labels = f"seal {self.class_id}"  # fallback 
        
    def convert_bbox_format(self, bbox):
        """
        Convert bounding box from (x1, y1, x2, y2) to (x, y, width, height).
        
        Parameters:
            bbox (dict): Bounding box with keys x1, y1, x2, y2.
        
        Returns:
            dict or None: Transformed bounding box or None if keys are missing.
        """
        if all(key in bbox for key in {"x1", "y1", "x2", "y2"}):
            return {
                "x": bbox["x1"],
                "y": bbox["y1"],
                "width": bbox["x2"] - bbox["x1"],
                "height": bbox["y2"] - bbox["y1"],
            }
        else:
            self.logger.warning("Missing keys in bounding box: %s", bbox)
            return None

    def find_matching_text(self, bboxes, target_bbox):
        """
        Find text from bboxes that matches a given bounding box.

        :param bboxes: List of dictionaries with 'HPOS', 'VPOS', 'END_HPOS', 'END_VPOS', and 'text'.
        :param target_bbox: Tuple (x1, y1, x2, y2) representing the bounding box to match.
        :return: List of matching text strings.
        """
        x1_t, y1_t, x2_t, y2_t = target_bbox
        matching_texts = []

        for box in bboxes:
            x1_b, y1_b = box['HPOS'], box['VPOS']
            x2_b, y2_b = box['END_HPOS'], box['END_VPOS']

            # Check for intersection (overlapping bboxes)
            if not (x2_t < x1_b or x2_b < x1_t or y2_t < y1_b or y2_b < y1_t):
                matching_texts.append(box['text'])

        if matching_texts:
            matching_text = " ".join(matching_texts)
        else:
            matching_text = None

        return matching_text
    
    def __call__(self, image_path=None, **kwargs):
        """
        Process the provided image to detect seals and optionally check their closeness to specified elements.

        Parameters:
            image_path (str): Path to the input image for seal detection.
            **kwargs: Additional keyword arguments including elements.

        Returns:
            PostProcessOutputFormat: Contains the result of the seal detection and closeness check.
        """
        # Retrieve elements from keyword arguments (bounding boxes, text areas, etc.)
        elements = kwargs.get("elements", [])
        if isinstance(elements, dict):  # Convert single element dictionary to list
            elements = [elements]

        # Default values for detection results
        if self.seal_appeared_before:
            value = ""
        else:
            value = "No"  # Default to "No" indicating no seal found
        success = False  # Default to failure
        bbox = None  # Placeholder for bounding box (if needed)
        line_number = None  # Placeholder for line number (if applicable)

        # Ensure an image path is provided, otherwise return an error
        if not image_path:
            self.logger.error("No image_path provided for seal detection.")
            return PostProcessOutputFormat(
                value="No image path provided.",
                success=False,
                elements=elements, 
                bbox=bbox,
                line_number=line_number
            )

        # Set the output directory based on the image file name
        self.output_dir = os.path.basename(image_path)
        
        # Call the ExtractSeal module to detect seals in the image
        object_details = self.extract_yolo_objects.extract_object_details(
            image_path=image_path,
            output_dir=self.output_dir,
            return_cropped_regions=self.return_cropped_regions,
            class_id = self.class_id,
            class_labels = self.class_labels
        )
       
        # If a seal is detected, update the result variables
        if "seal" in object_details.class_labels:
            
            if self.seal_appeared_before:
                seal_bbox = []
                seal_bbox.append(object_details.bounding_boxes[0]['x1'])
                seal_bbox.append(object_details.bounding_boxes[0]['y1'])
                seal_bbox.append(object_details.bounding_boxes[0]['x2'])
                seal_bbox.append(object_details.bounding_boxes[0]['y2'])
                
                matching_text = self.find_matching_text(elements, seal_bbox)
                
                if matching_text:
                    value = matching_text
                    success = True

            else:
                value = "Yes"  # Indicate seal presence
                success = True  # Mark detection as successful
                bbox = self.convert_bbox_format(object_details.bounding_boxes[0])


        # Return the final post-processed result
        return PostProcessOutputFormat(
            value=value,  # Yes or No, based on seal detection
            success=success,  # True if a seal was detected, False otherwise
            elements=elements,  # List of elements (bounding boxes, etc.)
            bbox=bbox,  # Bounding box (if relevant)
            line_number=line_number  # Line number (if applicable)
        )