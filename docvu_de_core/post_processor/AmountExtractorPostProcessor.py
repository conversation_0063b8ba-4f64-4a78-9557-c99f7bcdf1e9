from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils.process_utils import ProcessUtils
from docvu_de_core.utils.element_utils import ElementUtils
from copy import deepcopy
import re

class AmountExtractorPostProcessor:
    def __init__(self, **kwargs):
        """
        Initialize AmountExtractorPostProcessor with various text processing options.

        Supported arguments:
        - remove_spaces: Boolean to remove spaces from the text.
        - extract_second_amount: <PERSON>olean to extract the second amount match if available.
        """
        self.remove_spaces = kwargs.get('remove_spaces', False)
        self.extract_second_amount = kwargs.get("extract_second_amount", False)
        self.process_utils = ProcessUtils()
        self.amount_pattern = r'\$\s?\d{1,3}(?:\s?,\s?\d{3})*(?:\s?\.\s?\d{2})?'

    def __call__(self, **kwargs):
        """
        Execute the configured text processing operations on the given elements.

        :param kwargs: Dictionary containing 'elements' which is a list of text elements.
        :return: PostProcessOutputFormat with the processed text and related information.
        """
        elements = kwargs.get("elements", [])
        success = False
        elements_new = deepcopy(elements)

        if self.remove_spaces:
            elements_new = ElementUtils.remove_spaces(elements_new)

        elements_new, success = self.find_amount_substring_func(elements_new)

        value = ''.join([e["text"] for e in elements_new])
        bbox, line_number = self.process_utils.get_bbox_and_line_number(elements_new)

        return PostProcessOutputFormat(
            bbox=bbox,
            line_number=line_number,
            value=value,
            success=success,
            elements=elements_new,
        )

    def find_amount_substring_func(self, elements):
        # Step 1: Match dollar-prefixed pattern
        elements_new, found = ElementUtils.contains(elements, self.amount_pattern, case_sensitive=True)
        if found:
            return elements_new, True

        # Get the full text for custom regex operations
        full_text = ''.join([e["text"] for e in elements])

        # Step 2: Handle repeating amounts pattern (e.g. "4,620.434,620.434,620.43*******")
        repeating_amount_pattern = r'((\d{1,3}(?:,\d{3})*(?:\.\d{2})))\2*'
        repeating_match = re.search(repeating_amount_pattern, full_text)
        if repeating_match:
            first_amount = repeating_match.group(1)
            match_start = repeating_match.start(1)
            match_end = match_start + len(first_amount)

            matched_elements = []
            char_idx = 0
            for e in elements:
                text_len = len(e["text"])
                if char_idx + text_len > match_start and char_idx < match_end:
                    matched_elements.append(e)
                char_idx += text_len

            return matched_elements, True

        # Step 3: Fallback to plain amount pattern (943.81, 4,620.43 etc.)
        plain_amount_pattern = r'(?<!\d)(\d{1,3}(?:,\d{3})*(?:\.\d{2}))(?![\d,])'
        matches = list(re.finditer(plain_amount_pattern, full_text))

        # Deduplicate values
        seen = set()
        unique_matches = []
        for m in matches:
            val = m.group().replace(",", "")
            if val not in seen:
                seen.add(val)
                unique_matches.append(m)

        match_index = 1 if self.extract_second_amount else 0
        if len(unique_matches) > match_index:
            selected_match = unique_matches[match_index]
            start, end = selected_match.start(), selected_match.end()

            matched_elements = []
            char_idx = 0
            for e in elements:
                text_len = len(e["text"])
                if char_idx + text_len > start and char_idx < end:
                    matched_elements.append(e)
                char_idx += text_len

            return matched_elements, True

        return elements, False

# Example usage
if __name__ == "__main__":
    structured_elements = [
        {'text': '4,620.434,620.434,620.43**********', 'HPOS': 100, 'VPOS': 428, 'WIDTH': 758, 'HEIGHT': 20, 'END_HPOS': 858, 'END_VPOS': 448, 'WORDS': [], 'LINE_NUMBER': 7},
        {'text': '943.81 0.00 0.00, 4,620.43 4,620.43 4,620.43 ******nn,145,390.27 $145,390.27', 'HPOS': 100, 'VPOS': 430, 'WIDTH': 758, 'HEIGHT': 20, 'END_HPOS': 858, 'END_VPOS': 448, 'WORDS': [], 'LINE_NUMBER': 8},
    ]

    amount_extractor = AmountExtractorPostProcessor(remove_spaces=True)
    for el in structured_elements:
        result = amount_extractor(elements=[el])
        print("Extracted Value:", result.value)

        # Debugging raw regex match
        text = 'Amount: $9,158.75'
        match = re.search(r'\$\d{1,3}(?:,\d{3})*(?:\.\d{2})?', text)
        if match:
            amount = match.group()
            print(amount)  # Output: $9,158.75