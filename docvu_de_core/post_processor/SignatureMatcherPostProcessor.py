import os
from docvu_de_core.io.PostProcessOutputFormat import PostProcessOutputFormat
from docvu_de_core.models.ModelManager import ModelManager


class SignatureMatcherPostProcessor:
    def __init__(self, logger):
        self.signature_matcher = ModelManager.image_matcher
        self.logger = logger

    def __call__(self, **kwargs):
        image_path = kwargs.get("image_path")
        if not image_path:
            return PostProcessOutputFormat(
                bbox=None,
                line_number=None,
                value="No image path provided.",
                success=False,
                elements=None
            )

        image_dir = os.path.dirname(image_path)
        if not os.path.isdir(image_dir):
            return PostProcessOutputFormat(
                bbox=None,
                line_number=None,
                value=f"Invalid directory: {image_dir}",
                success=False,
                elements=None
            )

        page_images = sorted(
            [os.path.join(image_dir, f) for f in os.listdir(image_dir) if f.endswith('.jpg')],
            key=lambda x: int(os.path.splitext(os.path.basename(x))[0])
        )

        if len(page_images) < 2:
            return PostProcessOutputFormat(
                bbox=None,
                line_number=None,
                value="Insufficient pages for signature comparison.",
                success=False,
                elements=None
            )

        first_signature = None
        mismatched_pages = []
        matching_status = True

        for page_index, page_image in enumerate(page_images, start=1):
            try:
                signature_embedding = self.signature_matcher.get_embedding(page_image)
                if first_signature is None:
                    first_signature = signature_embedding
                else:
                    match_score = self.signature_matcher.compare(first_signature, signature_embedding)
                    if match_score < 0.9:
                        matching_status = False
                        mismatched_pages.append(page_index)
            except Exception as e:
                self.logger.error(f"Error processing page {page_index}: {e}")
                continue

        value = (
            "All signatures are matching across pages."
            if matching_status
            else f"Signatures mismatched on pages: {', '.join(map(str, mismatched_pages))}"
        )

        return PostProcessOutputFormat(
            bbox=None,
            line_number=None,
            value=value,
            success=True,
            elements={"total_pages": len(page_images), "mismatched_pages": mismatched_pages}
        )