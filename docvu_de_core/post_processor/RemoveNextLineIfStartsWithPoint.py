from docvu_de_core.utils.distance_utils import DistanceUtils
from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils.process_utils import ProcessUtils


class RemoveNextLineIfStartsWithPoint:
    def __init__(self, **kwargs):
        self.process_utils = ProcessUtils()

    def __call__(self, **kwargs):
        elements = kwargs.get("elements", [])
        success = False

        if not elements:
            return PostProcessOutputFormat(
                bbox=None,
                line_number=None,
                value='',
                success=success,
                elements=[],
            )

        last_element = elements[-1]
        preceding_elements = elements[:-1]

        # Check if the last element starts with a number followed by a period
        if last_element["text"].strip().split()[0].startswith(tuple(f"{i}." for i in range(1, 10))):
            if not preceding_elements:  # If preceding_elements is empty, continue without modifying elements
                success = False  # Indicate that no changes were made
            else:
                # Calculate HPOS values
                last_element_hpos = last_element.get("HPOS", float('inf'))
                min_hpos_preceding = min(e.get("HPOS", float('inf')) for e in preceding_elements)

                # Check if last_element HPOS is less than the minimum HPOS of preceding_elements
                if last_element_hpos < min_hpos_preceding:
                    elements = preceding_elements  # Remove last element
                    success = True

        value = ' '.join([e["text"] for e in elements])
        bbox, line_number = self.process_utils.get_bbox_and_line_number(elements)

        return PostProcessOutputFormat(
            bbox=bbox,
            line_number=line_number,
            value=value,
            success=success,
            elements=elements,
        )