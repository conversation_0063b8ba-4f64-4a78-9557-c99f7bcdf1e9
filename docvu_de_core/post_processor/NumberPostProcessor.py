import re
from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils.process_utils import ProcessUtils
from copy import deepcopy

class NumberPostProcessor:
    def __init__(self, **kwargs):
        self.process_utils = ProcessUtils()
        self.extract_flood_certificate_number = kwargs.get("extract_flood_certificate_number", False)

    def __call__(self, **kwargs):
        elements = kwargs.get("elements", [])
        success = False
        elements_new = deepcopy(elements)

        value = ' '.join([e["text"] for e in elements_new])

        if self.extract_flood_certificate_number:
            value = self.extract_flood_certificate_number_func(value)

        bbox, line_number = self.process_utils.get_bbox_and_line_number(elements_new)

        return PostProcessOutputFormat(
            bbox=bbox,
            line_number=line_number,
            value=value,
            success=success,
            elements=elements_new,
        )

    def extract_flood_certificate_number_func(self, text):
        """
        Extracts a flood certificate number from text. Supports:
        - 8 to 10 digit numbers
        - 10-character alphanumeric strings (e.g., 2108B79966)
        """
        if not isinstance(text, str):
            return str(text)

        text = " ".join(text.split())  # Normalize spaces

        # Priority 1: Match 10-character alphanumeric (starts with digits)
        match = re.search(r'\b\d{4}[A-Z]?\d{5}\b', text, re.IGNORECASE)
        if match:
            return match.group(0)

        # Priority 2: Match 8 to 10 digit number
        match = re.search(r'\b\d{8,10}\b', text)
        if match:
            return match.group(0)

        return text  # fallback if no match
