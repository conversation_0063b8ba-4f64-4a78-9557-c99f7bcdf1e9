#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
*************************************************************************
*
*
Confidential Copyright (c) 2024 VISIONET SYSTEMS INC.

All Rights Reserved.

* NOTICE:  All information contained herein is, and remains the property of
   VISIONET SYSTEMS INC and its suppliers, if any.
* The intellectual and technical concepts contained herein are proprietary to
   VISIONET SYSTEMS INC and its suppliers and may be covered by Indian and Foreign Patents,
   patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material is strictly forbidden unless
   prior written permission is obtained from VISIONET SYSTEMS INC.

*************************************************************************
"""

from copy import deepcopy
from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils.process_utils import ProcessUtils
from docvu_de_core.modules import AddressSplitter  # Assuming the AddressParser is imported here
from docvu_de_core.utils import ElementUtils


class AddressSplitterPostProcessor:
    def __init__(self, **kwargs):
        """
        Initialize AddressSplitterPostProcessor with the option to return the first address.

        Supported arguments:
        - return_first: Boolean to return only the first address.
        :param kwargs: Dictionary of processing options.
        """
        self.return_first = kwargs.get('return_first', False)
        self.process_utils = ProcessUtils()
        self.address_parser = AddressSplitter()

    def __call__(self, **kwargs):
        """
        Execute the address splitting process on the given elements.

        :param kwargs: Dictionary containing 'elements' which is a list of text elements.
        :return: PostProcessOutputFormat with the processed addresses and related information.
        """
        elements = kwargs.get("elements", [])
        success = False
        elements_new = deepcopy(elements)

        # Use AddressParser to split the addresses
        addresses_as_elements = self.address_parser.extract_address_components(elements_new)

        if not addresses_as_elements:
            bbox, line_number = ElementUtils.get_bbox_and_line_number(elements)
            value = ' '.join(e['text'] for e in elements)
            return PostProcessOutputFormat(
                    bbox=bbox,
                    line_number=line_number,
                    value=value,
                    success=bool(addresses_as_elements),  # Success is True if addresses were found
                    elements=elements,
            )

        value = None
        bbox, line_number = None, None
        if self.return_first:
            # If return_first is True, return the first address
            addresses_as_elements = addresses_as_elements[0]
            value = " ".join([e["text"] for e in addresses_as_elements])
            # Get bounding box and line number for the first or all addresses
            bbox, line_number = self.process_utils.get_bbox_and_line_number(
                addresses_as_elements)
        else:
            # Reconstruct the final output as a concatenated string
            value = [' '.join([e["text"] for e in addr]) for addr in addresses_as_elements]
            bbox, line_number = [], []
            for ase in addresses_as_elements:
                b, l = self.process_utils.get_bbox_and_line_number(ase)
                # Get bounding box and line number for the first or all addresses
                bbox.append(b)
                line_number.append(l)

        return PostProcessOutputFormat(
            bbox=bbox,
            line_number=line_number,
            value=value,
            success=bool(addresses_as_elements),  # Success is True if addresses were found
            elements=addresses_as_elements,
        )

# Example usage:
if __name__ == "__main__":
    structured_elements = [
        {'text': 'L 15 Villas @ Saxony, 13376 Dorster Street, Fishers, IN 46037', 'HPOS': 480, 'VPOS': 510, 'WIDTH': 764, 'HEIGHT': 22, 'END_HPOS': 1244, 'END_VPOS': 532, 'WORDS': [
            {'text': 'L', 'HPOS': 480.0, 'VPOS': 511.3333333333333, 'WIDTH': 0.0, 'HEIGHT': 21.333333333333332},
            {'text': '15', 'HPOS': 503.99999999999994, 'VPOS': 511.3333333333333, 'WIDTH': 13.333333333333428, 'HEIGHT': 21.333333333333332},
            {'text': 'Villas', 'HPOS': 540.0, 'VPOS': 511.3333333333333, 'WIDTH': 52.66666666666663, 'HEIGHT': 21.333333333333332},
            {'text': '@', 'HPOS': 615.3333333333333, 'VPOS': 510.6666666666667, 'WIDTH': 0.0, 'HEIGHT': 21.333333333333332},
            {'text': 'Saxony,', 'HPOS': 650.6666666666666, 'VPOS': 510.6666666666667, 'WIDTH': 94.0, 'HEIGHT': 21.333333333333332},
            {'text': '13376', 'HPOS': 760.6666666666666, 'VPOS': 511.3333333333333, 'WIDTH': 60.0, 'HEIGHT': 21.333333333333332},
            {'text': 'Dorster', 'HPOS': 844.6666666666667, 'VPOS': 511.3333333333333, 'WIDTH': 81.33333333333326, 'HEIGHT': 21.333333333333332},
            {'text': 'Street,', 'HPOS': 942.6666666666667, 'VPOS': 510.6666666666667, 'WIDTH': 75.33333333333337, 'HEIGHT': 21.333333333333332},
            {'text': 'Fishers,', 'HPOS': 1033.3333333333333, 'VPOS': 511.3333333333333, 'WIDTH': 90.66666666666674, 'HEIGHT': 21.333333333333332},
            {'text': 'IN', 'HPOS': 1140.0, 'VPOS': 511.3333333333333, 'WIDTH': 7.333333333333485, 'HEIGHT': 21.333333333333332},
            {'text': '46037', 'HPOS': 1180.6666666666665, 'VPOS': 511.3333333333333, 'WIDTH': 63.333333333333485, 'HEIGHT': 21.333333333333332}
        ], 'LINE_NUMBER': 9}
    ]

    address_splitter = AddressSplitterPostProcessor(return_first=True)
    result = address_splitter(elements=structured_elements)
    print("Result:")
    print(result)