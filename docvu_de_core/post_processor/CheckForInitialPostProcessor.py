import os
import math
from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.modules import ExtractLbsdObjects
from docvu_de_core.utils import ElementUtils

class CheckForInitialPostProcessor:
    """
    A post-processor class to detect initials in images and optionally check their closeness
    to specific bounding box regions.
    """

    def __init__(self, logger, check_closeness=False, return_cropped_regions=False, class_id=6, class_labels=None):
        """
        Initialize the CheckForInitialPostProcessor.

        Parameters:
            logger (object): Logger instance for logging.
            check_closeness (bool): Whether to check the proximity of detected initials to provided elements.
            return_cropped_regions (bool): Flag to enable returning cropped regions of detected initials.
            initial_present (bool): Flag indicating if an initial is present.
            class_id (int): The numeric ID representing the object class to detect (default is 6 for "seal").
            class_labels (str): The label/name of the object class to detect (default is "initial").
        """
        self.logger = logger  # Logger instance for error handling and debugging
        self.extract_yolo_objects = ExtractLbsdObjects(logger) # Initialize ExtractInitial module for detection
        self.check_closeness = check_closeness
        self.return_cropped_regions = return_cropped_regions  # Flag for returning cropped initial regions
        self.class_id = class_id
  
        # Determine class_labels automatically if not provided
        if class_labels is not None:
            self.class_labels = class_labels
        else:
            # Get class_labels from the label_map in ExtractLbsdObjects
            self.class_labels = None 
            
            # Direct lookup from the label_map dictionary
            clabel = ExtractLbsdObjects.label_map.get(self.class_id)

            if clabel:  # This checks for None and empty string
                self.class_labels = f"{clabel}"
            else:
                self.logger.error(f"class_id '{self.class_id}' not found or is empty in label map")
                self.class_labels = f"initial {self.class_id}"  # fallback
   
    def convert_bbox_format(self, bbox):
        """
        Convert bounding box from (x1, y1, x2, y2) to (x, y, width, height).
        
        Parameters:
            bbox (dict): Bounding box with keys x1, y1, x2, y2.
        
        Returns:
            dict or None: Transformed bounding box or None if keys are missing.
        """
        if all(key in bbox for key in {"x1", "y1", "x2", "y2"}):
            return {
                "x": bbox["x1"],
                "y": bbox["y1"],
                "width": bbox["x2"] - bbox["x1"],
                "height": bbox["y2"] - bbox["y1"],
            }
        else:
            self.logger.warning("Missing keys in bounding box: %s", bbox)
            return None

    def __call__(self, image_path=None, **kwargs):
        """
        Process the provided image to detect initials and optionally check their closeness to specified elements.

        Parameters:
            image_path (str): Path to the input image for initial detection.
            **kwargs: Additional keyword arguments including elements.

        Returns:
            PostProcessOutputFormat: Contains the result of the initial detection and closeness check.
        """
        # Retrieve elements from keyword arguments (bounding boxes, text areas, etc.)
        elements = kwargs.get("elements", [])
        if isinstance(elements, dict):  # Convert single element dictionary to list
            elements = [elements]

        # Default values for detection results
        value = "No"  # Default to "No" indicating no initial found
        success = False  # Default to failure
        bbox = None  # Placeholder for bounding box (if needed)
        line_number = None  # Placeholder for line number (if applicable)

        # Ensure an image path is provided, otherwise return an error
        if not image_path:
            self.logger.error("No image_path provided for initial detection.")
            return PostProcessOutputFormat(
                value="No image path provided.",
                success=False,
                elements=elements, 
                bbox=bbox,
                line_number=line_number
            )

        # Set the output directory based on the image file name
        self.output_dir = os.path.basename(image_path)

        # Call the ExtractInitial module to detect initials in the image details
        object_details = self.extract_yolo_objects.extract_object_details(
            image_path=image_path,
            output_dir=self.output_dir,
            return_cropped_regions=self.return_cropped_regions,
            class_id = self.class_id,
            class_labels = self.class_labels
        )
        
        # Check closeness if enabled
        if self.check_closeness:
            if object_details.class_labels.count("initial") >= 2:
                value = "Yes"
                success = True
                bbox = self.convert_bbox_format(object_details.bounding_boxes[1])

        else:
            # If an initial is detected, update the result variables
            if "initial" in object_details.class_labels:
                value = "Yes"  # Indicate initial presence
                success = True  # Mark detection as successful
                bbox = self.convert_bbox_format(object_details.bounding_boxes[0])
                
        # Return the final post-processed result
        return PostProcessOutputFormat(
            value=value,  # Yes or No, based on initial detection
            success=success,  # True if an initial was detected, False otherwise
            elements=elements,  # List of elements (bounding boxes, etc.)
            bbox=bbox,  # Bounding box (if relevant)
            line_number=line_number  # Line number (if applicable)
        )