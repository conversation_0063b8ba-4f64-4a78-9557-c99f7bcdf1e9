import re
from copy import deepcopy
from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils.process_utils import ProcessUtils

class SsnPostProcessor:
    def __init__(self, **kwargs):
        self.process_utils = ProcessUtils()
        self.extract_ssns = kwargs.get("extract_ssns", False)

    def __call__(self, **kwargs):
        print("3333")

        elements = kwargs.get("elements", [])
        field = kwargs.get("field", {})
        field_name = kwargs.get("field_name", field.get("field_name", ""))

        success = False
        elements_new = deepcopy(elements)

        # Build full text from all elements
        full_text = ' '.join([e.get("text", "") for e in elements_new if isinstance(e, dict)])
        ssns = self.extract_ssns_func(full_text)
        normalized_field = re.sub(r'[^a-z]', '', field_name.lower())

        # Debug print statements
        print(">>> FIELD NAME:", field_name)
        print(">>> NORMALIZED FIELD NAME:", normalized_field)
        print(">>> EXTRACTED SSNs:", ssns)

        value = ""

        if len(ssns) >= 1:
            if "coborrowerssn" in normalized_field:
                value = ssns[1] if len(ssns) > 1 else ssns[0]
            else:
                # fallback: check for presence of co-borrower text
                surrounding_text = full_text.lower()
                if "co-borrower" in surrounding_text or "coborrower" in surrounding_text:
                    value = ssns[1] if len(ssns) > 1 else ssns[0]
                else:
                    value = ssns[0]  # fallback

        if value:
            success = True

        bbox, line_number = self.process_utils.get_bbox_and_line_number(elements_new)

        return PostProcessOutputFormat(
            bbox=bbox,
            line_number=line_number,
            value=value,
            success=success,
            elements=elements_new,
        )

    def extract_ssns_func(self, text):
        """
        Extract all SSNs (format: XXX-XX-XXXX) from the text.
        """
        if not isinstance(text, str):
            return []

        text = " ".join(text.split())  # Normalize whitespace
        ssns = re.findall(r'\b\d{3}-\d{2}-\d{4}\b', text)
        return ssns
