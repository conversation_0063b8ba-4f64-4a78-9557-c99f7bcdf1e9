from docvu_de_core.io import PostProcessOutputFormat
from docvu_de_core.utils.process_utils import ProcessUtils
from docvu_de_core.utils.element_utils import ElementUtils
from copy import deepcopy
import re

class StringOperationsPostProcessor:
    def __init__(self, **kwargs):
        """
        Initialize StringOperations with various text processing options.

        Supported arguments:
        - remove_from_beginning: List of substrings to remove from the beginning of the text.
        - remove_from_end: List of substrings to remove from the end of the text.
        - contains: List of substrings to check if the text contains.
        - remove_special_chars_from_beginning: Boolean to remove special characters from the beginning.
        - remove_special_chars_from_end: Boolean to remove special characters from the end.
        - remove_spaces: <PERSON><PERSON>an to remove spaces from the text.
        - starts_with: List of substrings or regex patterns to check if the text starts with.
        - ends_with: List of substrings or regex patterns to check if the text ends with.
        - remove_till_from_end: List of substrings or regex patterns to remove text till from the end.
        - remove_till_from_beginning: List of substrings or regex patterns to remove text till from the beginning.
        - remove_till_return_default_if_not_found: Boolean to return default elements if remove_till is not found.
        - remove_non_digit: Boolean to remove all non-digit characters from the text.

        :param kwargs: Dictionary of processing options.
        """
        self.remove_from_beginning = kwargs.get('remove_from_beginning', [])
        self.remove_from_end = kwargs.get('remove_from_end', [])
        self.contains = kwargs.get('contains', [])

        self.remove_special_chars_from_beginning = kwargs.get('remove_special_chars_from_beginning', False)
        self.remove_special_chars_from_end = kwargs.get('remove_special_chars_from_end', False)
        self.remove_spaces = kwargs.get('remove_spaces', False)
        self.remove_non_digit = kwargs.get('remove_non_digit', False)

        self.starts_with = kwargs.get('starts_with', [])
        self.remove_till_from_end = kwargs.get('remove_till_from_end', [])
        self.remove_till_from_beginning = kwargs.get('remove_till_from_beginning', [])
        self.remove_till_return_default_if_not_found = kwargs.get('remove_till_return_default_if_not_found', False)

        self.process_utils = ProcessUtils()
        self.ends_with = kwargs.get('ends_with', [])
        self.check_text_present = kwargs.get('check_text_present', False)
        self.cd_coborrower_name = kwargs.get('cd_coborrower_name', False)

        self.operation_order = [key for key in kwargs.keys() if key in [
            'remove_from_beginning', 'remove_from_end', 'contains', 'remove_special_chars_from_beginning',
            'remove_special_chars_from_end', 'remove_spaces', 'starts_with', 'ends_with',
            'remove_till_from_end', 'remove_till_from_beginning', 'remove_non_digit'
        ]]

    def __call__(self, **kwargs):
        """
        Execute the configured text processing operations on the given elements.

        :param kwargs: Dictionary containing 'elements' which is a list of text elements.
        :return: PostProcessOutputFormat with the processed text and related information.
        """
        elements = kwargs.get("elements", [])
        success = False
        elements_new = deepcopy(elements)

        for operation in self.operation_order:
            func = getattr(self, f"{operation}_func", None)
            if func:
                elements_new, success = func(elements_new)
        
        if not self.remove_spaces:
            value = ' '.join([e["text"] for e in elements_new])
        else:
            value = ''.join([e["text"] for e in elements_new])

        if self.check_text_present:
            value, success = self.text_present(elements_new)

        if self.cd_coborrower_name:
            value = self.get_cd_coborrower_name(elements_new)
            if value:
                success = True
        
        bbox, line_number = self.process_utils.get_bbox_and_line_number(elements_new)

        return PostProcessOutputFormat(
            bbox=bbox,
            line_number=line_number,
            value=value,
            success=success,
            elements=elements_new,
        )

    def remove_from_beginning_func(self, elements):
        elements_new = ElementUtils.remove_from_beginning(elements, self.remove_from_beginning)
        success = elements != elements_new
        return elements_new, success

    def remove_from_end_func(self, elements):
        elements_new =  ElementUtils.remove_from_end(elements, self.remove_from_end)
        success = elements != elements_new
        return elements_new, success

    def contains_func(self, elements):
        elements_new, found = ElementUtils.contains(elements, self.contains)
        success = found
        return elements_new, success

    def remove_special_chars_from_beginning_func(self, elements):
        elements_new = ElementUtils.remove_special_chars_from_beginning(elements)
        success = elements != elements_new
        return elements_new, success

    def remove_special_chars_from_end_func(self, elements):
        elements_new = ElementUtils.remove_special_chars_from_end(elements)
        success = elements != elements_new
        return elements_new, success

    def remove_spaces_func(self, elements):
        elements_new = ElementUtils.remove_spaces(elements)
        success = elements != elements_new
        return elements_new, success

    def starts_with_func(self, elements):
        success, _ = ElementUtils.starts_with(elements, self.starts_with)
        elements_new = elements if success else []
        return elements_new, success

    def ends_with_func(self, elements):
        success, _ = ElementUtils.ends_with(elements, self.ends_with)
        elements_new = elements if success else []
        return elements_new, success

    def remove_till_from_end_func(self, elements):
        elements_new = ElementUtils.remove_till_from_end(elements, self.remove_till_from_end)
        success = elements != elements_new
        if self.remove_till_return_default_if_not_found:
            if not elements_new:
                elements_new = elements
        return elements_new, success

    def remove_till_from_beginning_func(self, elements):
        elements_new = ElementUtils.remove_till_from_beginning(elements, self.remove_till_from_beginning)
        success = elements != elements_new
        if self.remove_till_return_default_if_not_found:
            if not elements_new:
                elements_new = elements
        return elements_new, success

    def remove_non_digit_func(self, elements):
        elements_new = ElementUtils.remove_non_digit(elements)
        success = elements != elements_new
        return elements_new, success
    
    def text_present(self, elements):
        value = "Yes"
        success = False

        if len(elements) > 0:
            value = "No"
            success = True
        
        return value, success
    
    def get_cd_coborrower_name(self, elements):
        text = ' '.join(d['text'] for d in elements if 'text' in d)

        # Step 1: Extract text between the two "Date" keywords
        match = re.search(r'Date(.*?)Date', text)
        if match:
            middle_text = match.group(1).strip()
            if middle_text:
                name = self.split_before_repeating_word(middle_text)
                return name
        
        return text
    
    def split_before_repeating_word(self, text):
        words = text.split()
        seen = {}
        for i, word in enumerate(words):
            word_lower = word.lower()
            if word_lower in seen:
                # Start from the word before the second occurrence
                start_index = max(i - 1, 0)
                return ' '.join(words[start_index:i + 1])
            seen[word_lower] = i
        return text  # No repeating word found

# Example usage:
if __name__ == "__main__":
    structured_elements = [
        {'text': 'Address Reference: 9158 Skywood Lane, Juneau, AK 99801', 'HPOS': 100, 'VPOS': 428,
         'WIDTH': 758, 'HEIGHT': 20, 'END_HPOS': 858, 'END_VPOS': 448, 'WORDS': [
            {'text': 'Address', 'HPOS': 100.0, 'VPOS': 428.66666666666663, 'WIDTH': 96.0,
             'HEIGHT': 20.666666666666668},
            {'text': 'Reference:', 'HPOS': 220.66666666666666, 'VPOS': 428.66666666666663,
             'WIDTH': 134.66666666666666, 'HEIGHT': 20.666666666666668},
            {'text': '9158', 'HPOS': 378.6666666666667, 'VPOS': 428.66666666666663, 'WIDTH': 46.0,
             'HEIGHT': 20.666666666666668},
            {'text': 'Skywood', 'HPOS': 448.00000000000006, 'VPOS': 428.0, 'WIDTH': 97.33333333333331,
             'HEIGHT': 20.666666666666668},
            {'text': 'Lane,', 'HPOS': 570.0, 'VPOS': 428.66666666666663, 'WIDTH': 61.33333333333337,
             'HEIGHT': 20.666666666666668},
            {'text': 'Juneau,', 'HPOS': 645.3333333333333, 'VPOS': 428.66666666666663,
             'WIDTH': 92.66666666666674, 'HEIGHT': 20.666666666666668},
            {'text': 'AK', 'HPOS': 749.3333333333334, 'VPOS': 428.66666666666663, 'WIDTH': 21.333333333333258,
             'HEIGHT': 20.666666666666668},
            {'text': '99801', 'HPOS': 795.3333333333334, 'VPOS': 428.66666666666663, 'WIDTH': 63.33333333333337,
             'HEIGHT': 20.666666666666668}
        ], 'LINE_NUMBER': 7},
        # Add more elements if needed
    ]

    remove_list_beginning = ["Address Reference: 9158 Skywood Lane"]
    remove_list_end = ["AK 99801"]
    contains_list = ["Skywood Lane", "Juneau"]

    string_operations = StringOperationsPostProcessor(
        remove_from_beginning=remove_list_beginning,
        remove_from_end=remove_list_end,
        contains=contains_list
    )

    result = string_operations(elements=structured_elements)
    print("Result:")
    print(result)