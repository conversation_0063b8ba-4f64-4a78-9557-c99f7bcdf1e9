import pandas as pd

# Load the original Excel file into a pandas DataFrame
input_file = r'/home/<USER>/docvufileshareuat/Valon/security_instrument/gt_all/gt_forallvariants.xlsx'  # Update this path to your actual input file
df = pd.read_excel(input_file)

# Create a new DataFrame for the transformed structure
transformed_data = []

# Get unique field names dynamically
unique_fields = df['Data Extraction Field Name'].unique()

# Iterate over each unique PDF Name
for pdf_name in df['Document Name'].unique():
    # Filter rows for the current PDF Name
    temp_df = df[df['Document Name'] == pdf_name]

    # Create a new row dictionary with the initial values
    new_row = {
        'form_type': 'security_instrument',  # Set the form type (static or dynamic if needed)
        'file_name': pdf_name.replace('.pdf', '')  # Remove .pdf extension
    }

    # Initialize all unique fields in the new row with None
    for field in unique_fields:
        new_row[field] = None

    # Populate the new row with values based on the FieldName
    for index, row in temp_df.iterrows():
        field_name = row['Data Extraction Field Name']
        if field_name in unique_fields:
            new_row[field_name] = row['Values']

    # Append the new row to the transformed data list
    transformed_data.append(new_row)

# Create a DataFrame from the transformed data
transformed_df = pd.DataFrame(transformed_data)

# Save the transformed data to a new Excel file
output_file = r'/home/<USER>/docvufileshareuat/Valon/security_instrument/gt_all/transformed_gt.xlsx'  # Update this path to your desired output file
transformed_df.to_excel(output_file, index=False)

print(f"Transformation complete. The output is saved to {output_file}.")