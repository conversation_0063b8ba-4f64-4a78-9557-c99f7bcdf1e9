#!/usr/bin/env python3
"""
Test script for the Enhanced Document Classifier
Tests classification of 5-6 document variants with confidence scoring
"""

import json
import os
from docvu_de_core.policy_classifier.EnhancedClassify import EnhancedClassify


def create_test_config():
    """Create a comprehensive test configuration for 6 document variants."""
    return {
        "debug": True,
        "confidence_threshold": 0.5,
        "enable_confidence_scoring": True,
        "case_sensitive": False,
        "partial_match": True,
        "max_upper_block": 3,
        "use_upper_split_percentage": 0.45,
        "max_lines_for_header": 5,
        "default_return": "docvu_de_core/de_config/default_de.json",
        "document_types": {
            "1003_application_form": {
                "return": "docvu_de_core/de_config/1003_application_de.json",
                "header": {
                    "include_strings": [
                        "1003",
                        "Application",
                        "UNIFORM RESIDENTIAL LOAN APPLICATION",
                        "LOAN APPLICATION",
                        "SECTION I",
                        "BORROWER INFORMATION"
                    ],
                    "exclude_strings": [
                        "Closing Disclosure",
                        "ACH Authorization",
                        "Appraisal",
                        "Sign Up For Autopay"
                    ]
                },
                "body": {
                    "include_strings": [
                        "Borrower",
                        "Co-Borrower",
                        "Property Address",
                        "Loan Amount",
                        "Purpose of Loan"
                    ],
                    "exclude_strings": []
                }
            },
            "ach_authorization_form": {
                "return": "docvu_de_core/de_config/ach_authorization_de.json",
                "header": {
                    "include_strings": [
                        "Sign Up For Autopay",
                        "ACH Authorization",
                        "Borrowers Authorization for Autopay",
                        "Autopay",
                        "Spring EQ"
                    ],
                    "exclude_strings": [
                        "1003",
                        "Closing Disclosure",
                        "Appraisal"
                    ]
                },
                "body": {
                    "include_strings": [
                        "authorize",
                        "debit entries",
                        "bank account",
                        "Routing Number",
                        "Account Number",
                        "monthly payments"
                    ],
                    "exclude_strings": []
                }
            },
            "closing_disclosure": {
                "return": "docvu_de_core/de_config/closing_disclosure_de.json",
                "header": {
                    "include_strings": [
                        "Closing Disclosure",
                        "This form is a statement of final loan terms",
                        "Compare this document with your Loan Estimate"
                    ],
                    "exclude_strings": [
                        "1003",
                        "ACH Authorization",
                        "Appraisal",
                        "Autopay"
                    ]
                },
                "body": {
                    "include_strings": [
                        "Closing Information",
                        "Transaction Information",
                        "Loan Information",
                        "Loan Terms",
                        "Projected Payments",
                        "Costs at Closing"
                    ],
                    "exclude_strings": []
                }
            },
            "appraisal_report": {
                "return": "docvu_de_core/de_config/appraisal_report_de.json",
                "header": {
                    "include_strings": [
                        "Appraisal Report",
                        "UNIFORM RESIDENTIAL APPRAISAL REPORT",
                        "APPRAISAL REPORT",
                        "Property Address",
                        "Appraiser"
                    ],
                    "exclude_strings": [
                        "1003",
                        "Closing Disclosure",
                        "ACH Authorization",
                        "Autopay"
                    ]
                },
                "body": {
                    "include_strings": [
                        "Subject Property",
                        "Market Value",
                        "Comparable Sales",
                        "Property Description",
                        "Neighborhood"
                    ],
                    "exclude_strings": []
                }
            },
            "loan_estimate": {
                "return": "docvu_de_core/de_config/loan_estimate_de.json",
                "header": {
                    "include_strings": [
                        "Loan Estimate",
                        "Save this Loan Estimate",
                        "Compare this Loan Estimate"
                    ],
                    "exclude_strings": [
                        "Closing Disclosure",
                        "1003",
                        "ACH Authorization",
                        "Appraisal"
                    ]
                },
                "body": {
                    "include_strings": [
                        "Loan Terms",
                        "Projected Payments",
                        "Costs at Closing",
                        "Annual Percentage Rate",
                        "Total Interest Percentage"
                    ],
                    "exclude_strings": []
                }
            },
            "deed_of_trust": {
                "return": "docvu_de_core/de_config/deed_of_trust_de.json",
                "header": {
                    "include_strings": [
                        "Deed of Trust",
                        "DEED OF TRUST",
                        "Security Instrument",
                        "Trustor",
                        "Trustee",
                        "Beneficiary"
                    ],
                    "exclude_strings": [
                        "1003",
                        "Closing Disclosure",
                        "ACH Authorization",
                        "Loan Estimate"
                    ]
                },
                "body": {
                    "include_strings": [
                        "Property Description",
                        "Legal Description",
                        "Borrower covenants",
                        "Default",
                        "Power of Sale"
                    ],
                    "exclude_strings": []
                }
            }
        }
    }


def test_classifier_with_samples():
    """Test the enhanced classifier with available sample documents."""
    
    print("=" * 60)
    print("ENHANCED DOCUMENT CLASSIFIER TEST")
    print("=" * 60)
    
    # Create classifier with test configuration
    config = create_test_config()
    classifier = EnhancedClassify(config)
    
    # Test files (adjust paths as needed)
    test_files = [
        {
            'path': 'ocr_output/1003_Application_(100)_textract_ocr/combined.json',
            'expected': '1003_application_form',
            'description': '1003 Application Form'
        },
        {
            'path': 'ocr_output/1003_Application_(101)_textract_ocr/combined.json',
            'expected': '1003_application_form',
            'description': '1003 Application Form (Variant 2)'
        },
        {
            'path': 'ocr_output/ACH_Authorization_Form_(1)_textract_ocr/combined.json',
            'expected': 'ach_authorization_form',
            'description': 'ACH Authorization Form'
        },
        {
            'path': 'ocr_output/Closing_Disclosure_(1)_textract_ocr/combined.json',
            'expected': 'closing_disclosure',
            'description': 'Closing Disclosure'
        }
    ]
    
    results = []
    
    for test_file in test_files:
        print(f"\n--- Testing: {test_file['description']} ---")
        print(f"File: {test_file['path']}")
        print(f"Expected: {test_file['expected']}")
        
        if not os.path.exists(test_file['path']):
            print(f"❌ File not found: {test_file['path']}")
            results.append({
                'file': test_file['path'],
                'expected': test_file['expected'],
                'actual': 'FILE_NOT_FOUND',
                'confidence': 0.0,
                'success': False
            })
            continue
        
        try:
            # Run classification
            result = classifier.process_form(test_file['path'])
            
            # Extract results
            classified_as = result.get('classified_as', 'unknown')
            confidence = result.get('confidence', 0.0)
            return_config = result.get('document_type', 'unknown')
            
            # Check if classification is correct
            success = classified_as == test_file['expected']
            
            print(f"✅ Classified as: {classified_as}")
            print(f"📊 Confidence: {confidence:.2f}")
            print(f"📄 Return config: {return_config}")
            print(f"🎯 Success: {'YES' if success else 'NO'}")
            
            if result.get('all_scores'):
                print("📈 All scores:")
                for doc_type, score in result['all_scores'].items():
                    print(f"   {doc_type}: {score:.2f}")
            
            results.append({
                'file': test_file['path'],
                'expected': test_file['expected'],
                'actual': classified_as,
                'confidence': confidence,
                'success': success,
                'return_config': return_config
            })
            
        except Exception as e:
            print(f"❌ Error processing file: {e}")
            results.append({
                'file': test_file['path'],
                'expected': test_file['expected'],
                'actual': 'ERROR',
                'confidence': 0.0,
                'success': False,
                'error': str(e)
            })
    
    # Print summary
    print("\n" + "=" * 60)
    print("CLASSIFICATION SUMMARY")
    print("=" * 60)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['success'])
    
    print(f"Total tests: {total_tests}")
    print(f"Successful: {successful_tests}")
    print(f"Failed: {total_tests - successful_tests}")
    print(f"Success rate: {(successful_tests/total_tests)*100:.1f}%")
    
    print("\nDetailed Results:")
    for i, result in enumerate(results, 1):
        status = "✅" if result['success'] else "❌"
        print(f"{i}. {status} {os.path.basename(result['file'])}")
        print(f"   Expected: {result['expected']}")
        print(f"   Actual: {result['actual']}")
        print(f"   Confidence: {result['confidence']:.2f}")
        if 'error' in result:
            print(f"   Error: {result['error']}")
    
    return results


def save_test_config():
    """Save the test configuration to a file for reuse."""
    config = create_test_config()
    config_path = 'docvu_de_core/de_config/enhanced_multi_variant_config.json'
    
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Test configuration saved to: {config_path}")
    return config_path


if __name__ == '__main__':
    # Save configuration file
    config_path = save_test_config()
    
    # Run tests
    test_results = test_classifier_with_samples()
    
    print(f"\n🔧 Configuration file: {config_path}")
    print("🚀 You can now use this enhanced classifier in your application!")
    print("\nUsage example:")
    print("```python")
    print("from docvu_de_core.policy_classifier.EnhancedClassify import EnhancedClassify")
    print(f"classifier = EnhancedClassify('{config_path}')")
    print("result = classifier.process_form('path/to/your/document.json')")
    print("print(f'Document type: {result[\"classified_as\"]}')") 
    print("print(f'Confidence: {result[\"confidence\"]:.2f}')")
    print("```")
