#!/usr/bin/env python3
"""
Integration script to replace the original Classify.py with EnhancedClassify.py
for handling 5-6 document variants with improved classification logic.
"""

import os
import shutil
import json
from datetime import datetime


def backup_original_classifier():
    """Create a backup of the original Classify.py file."""
    original_path = 'docvu_de_core/policy_classifier/Classify.py'
    backup_path = f'docvu_de_core/policy_classifier/Classify_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.py'
    
    if os.path.exists(original_path):
        shutil.copy2(original_path, backup_path)
        print(f"✅ Original Classify.py backed up to: {backup_path}")
        return backup_path
    else:
        print(f"⚠️  Original file not found: {original_path}")
        return None


def create_enhanced_config():
    """Create the enhanced configuration file for 6 document variants."""
    config = {
        "debug": True,
        "confidence_threshold": 0.6,
        "enable_confidence_scoring": True,
        "case_sensitive": False,
        "partial_match": True,
        "max_upper_block": 3,
        "use_upper_split_percentage": 0.45,
        "max_lines_for_header": 5,
        "max_upper_lines_for_key_search": 8,
        "default_return": "docvu_de_core/de_config/default_de.json",
        "document_types": {
            "1003_application_form": {
                "return": "docvu_de_core/de_config/1003_application_de.json",
                "header": {
                    "include_strings": [
                        "1003",
                        "Application",
                        "UNIFORM RESIDENTIAL LOAN APPLICATION",
                        "LOAN APPLICATION",
                        "SECTION I",
                        "BORROWER INFORMATION"
                    ],
                    "exclude_strings": [
                        "Closing Disclosure",
                        "ACH Authorization",
                        "Appraisal",
                        "Sign Up For Autopay"
                    ]
                },
                "body": {
                    "include_strings": [
                        "Borrower",
                        "Co-Borrower", 
                        "Property Address",
                        "Loan Amount",
                        "Purpose of Loan"
                    ],
                    "exclude_strings": []
                },
                "big_font": {
                    "include_strings": [
                        "1003",
                        "Application",
                        "LOAN APPLICATION"
                    ],
                    "exclude_strings": []
                }
            },
            "ach_authorization_form": {
                "return": "docvu_de_core/de_config/ach_authorization_de.json",
                "header": {
                    "include_strings": [
                        "Sign Up For Autopay",
                        "ACH Authorization",
                        "Borrowers Authorization for Autopay",
                        "Autopay",
                        "Spring EQ"
                    ],
                    "exclude_strings": [
                        "1003",
                        "Closing Disclosure",
                        "Appraisal"
                    ]
                },
                "body": {
                    "include_strings": [
                        "authorize",
                        "debit entries",
                        "bank account",
                        "Routing Number",
                        "Account Number",
                        "monthly payments"
                    ],
                    "exclude_strings": []
                },
                "big_font": {
                    "include_strings": [
                        "Sign Up For Autopay",
                        "Autopay"
                    ],
                    "exclude_strings": []
                }
            },
            "closing_disclosure": {
                "return": "docvu_de_core/de_config/closing_disclosure_de.json",
                "header": {
                    "include_strings": [
                        "Closing Disclosure",
                        "This form is a statement of final loan terms",
                        "Compare this document with your Loan Estimate"
                    ],
                    "exclude_strings": [
                        "1003",
                        "ACH Authorization",
                        "Appraisal",
                        "Autopay"
                    ]
                },
                "body": {
                    "include_strings": [
                        "Closing Information",
                        "Transaction Information",
                        "Loan Information",
                        "Loan Terms",
                        "Projected Payments",
                        "Costs at Closing"
                    ],
                    "exclude_strings": []
                },
                "big_font": {
                    "include_strings": [
                        "Closing Disclosure"
                    ],
                    "exclude_strings": []
                }
            },
            "appraisal_report": {
                "return": "docvu_de_core/de_config/appraisal_report_de.json",
                "header": {
                    "include_strings": [
                        "Appraisal Report",
                        "UNIFORM RESIDENTIAL APPRAISAL REPORT",
                        "APPRAISAL REPORT",
                        "Property Address",
                        "Appraiser"
                    ],
                    "exclude_strings": [
                        "1003",
                        "Closing Disclosure",
                        "ACH Authorization",
                        "Autopay"
                    ]
                },
                "body": {
                    "include_strings": [
                        "Subject Property",
                        "Market Value",
                        "Comparable Sales",
                        "Property Description",
                        "Neighborhood"
                    ],
                    "exclude_strings": []
                }
            },
            "loan_estimate": {
                "return": "docvu_de_core/de_config/loan_estimate_de.json",
                "header": {
                    "include_strings": [
                        "Loan Estimate",
                        "Save this Loan Estimate",
                        "Compare this Loan Estimate"
                    ],
                    "exclude_strings": [
                        "Closing Disclosure",
                        "1003",
                        "ACH Authorization",
                        "Appraisal"
                    ]
                },
                "body": {
                    "include_strings": [
                        "Loan Terms",
                        "Projected Payments",
                        "Costs at Closing",
                        "Annual Percentage Rate",
                        "Total Interest Percentage"
                    ],
                    "exclude_strings": []
                }
            },
            "deed_of_trust": {
                "return": "docvu_de_core/de_config/deed_of_trust_de.json",
                "header": {
                    "include_strings": [
                        "Deed of Trust",
                        "DEED OF TRUST",
                        "Security Instrument",
                        "Trustor",
                        "Trustee",
                        "Beneficiary"
                    ],
                    "exclude_strings": [
                        "1003",
                        "Closing Disclosure",
                        "ACH Authorization",
                        "Loan Estimate"
                    ]
                },
                "body": {
                    "include_strings": [
                        "Property Description",
                        "Legal Description",
                        "Borrower covenants",
                        "Default",
                        "Power of Sale"
                    ],
                    "exclude_strings": []
                }
            }
        }
    }
    
    config_path = 'docvu_de_core/de_config/enhanced_classify_config.json'
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Enhanced configuration created: {config_path}")
    return config_path


def create_wrapper_class():
    """Create a wrapper class that maintains backward compatibility."""
    wrapper_code = '''import json
from typing import List, Tuple, Dict
from docvu_de_core.policy_classifier.EnhancedClassify import EnhancedClassify


class Classify:
    """
    Backward-compatible wrapper for EnhancedClassify.
    Maintains the same interface as the original Classify class.
    """
    
    def __init__(self, config):
        # Initialize the enhanced classifier
        self.enhanced_classifier = EnhancedClassify(config)
        
        # Maintain backward compatibility properties
        self.config = self.enhanced_classifier.config
        self.max_upper_block = self.enhanced_classifier.max_upper_block
        self.use_upper_split_percentage = self.enhanced_classifier.use_upper_split_percentage
        self.max_lines_for_header = self.enhanced_classifier.max_lines_for_header
        self.max_upper_lines_for_key_search = self.enhanced_classifier.max_upper_lines_for_key_search
        self.debug = self.enhanced_classifier.debug

    def sort_list(self, l: List[Tuple]) -> List[Tuple]:
        """Maintain backward compatibility."""
        return self.enhanced_classifier.sort_list(l)

    def get_upper_blocks_data_from_all_pages(self, merged_page_json: str) -> Tuple[
        Dict[int, List[str]], Dict[int, List[int]]]:
        """Maintain backward compatibility."""
        return self.enhanced_classifier.get_upper_blocks_data_from_all_pages(merged_page_json)

    def classify_document(self, block_texts: Dict[int, List[str]], 
                         block_text_heights: Dict[int, List[int]]) -> Dict:
        """
        Enhanced classification with backward compatibility.
        Returns enhanced results but maintains original interface.
        """
        result = self.enhanced_classifier.classify_document(block_texts, block_text_heights)
        
        # For backward compatibility, return the document_type directly if needed
        # You can modify this based on your existing code expectations
        return result

    def process_form(self, combined_json_path: str) -> Dict:
        """Maintain backward compatibility."""
        return self.enhanced_classifier.process_form(combined_json_path)
'''
    
    wrapper_path = 'docvu_de_core/policy_classifier/Classify.py'
    with open(wrapper_path, 'w') as f:
        f.write(wrapper_code)
    
    print(f"✅ Backward-compatible wrapper created: {wrapper_path}")
    return wrapper_path


def test_integration():
    """Test the integration with sample files."""
    print("\n" + "="*50)
    print("TESTING ENHANCED CLASSIFIER INTEGRATION")
    print("="*50)
    
    try:
        # Import the new Classify class
        from docvu_de_core.policy_classifier.Classify import Classify
        
        # Load the enhanced configuration
        config_path = 'docvu_de_core/de_config/enhanced_classify_config.json'
        classifier = Classify(config_path)
        
        # Test with available sample files
        test_files = [
            'ocr_output/ACH_Authorization_Form_(1)_textract_ocr/combined.json',
            'ocr_output/Closing_Disclosure_(1)_textract_ocr/combined.json'
        ]
        
        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"\\n--- Testing: {test_file} ---")
                result = classifier.process_form(test_file)
                print(f"Result: {result.get('classified_as', 'unknown')}")
                print(f"Confidence: {result.get('confidence', 0.0):.2f}")
                print(f"Config: {result.get('document_type', 'unknown')}")
            else:
                print(f"⚠️  Test file not found: {test_file}")
        
        print("\\n✅ Integration test completed successfully!")
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False
    
    return True


def main():
    """Main integration function."""
    print("🚀 ENHANCED DOCUMENT CLASSIFIER INTEGRATION")
    print("="*60)
    
    # Step 1: Backup original classifier
    backup_path = backup_original_classifier()
    
    # Step 2: Create enhanced configuration
    config_path = create_enhanced_config()
    
    # Step 3: Create backward-compatible wrapper
    wrapper_path = create_wrapper_class()
    
    # Step 4: Test integration
    success = test_integration()
    
    # Summary
    print("\\n" + "="*60)
    print("INTEGRATION SUMMARY")
    print("="*60)
    
    if success:
        print("✅ Enhanced classifier successfully integrated!")
        print("\\n📁 Files created/modified:")
        if backup_path:
            print(f"   📄 Backup: {backup_path}")
        print(f"   ⚙️  Config: {config_path}")
        print(f"   🔧 Wrapper: {wrapper_path}")
        print(f"   🧪 Enhanced: docvu_de_core/policy_classifier/EnhancedClassify.py")
        
        print("\\n🎯 Key Features:")
        print("   • Handles 6 document variants")
        print("   • Confidence scoring")
        print("   • Enhanced pattern matching")
        print("   • Backward compatibility")
        print("   • Debug mode with detailed output")
        
        print("\\n📖 Usage:")
        print("   # Your existing code should work without changes")
        print("   from docvu_de_core.policy_classifier.Classify import Classify")
        print(f"   classifier = Classify('{config_path}')")
        print("   result = classifier.process_form('path/to/document.json')")
        
    else:
        print("❌ Integration failed. Please check the errors above.")
        if backup_path:
            print(f"\\n🔄 To restore original: cp {backup_path} docvu_de_core/policy_classifier/Classify.py")


if __name__ == '__main__':
    main()
