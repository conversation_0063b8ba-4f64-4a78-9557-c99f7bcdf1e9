import os
from docvu_de_core.api import *


class Engine:
    def __init__(self, ocr_dir = "./ocr_output",mapper_config_path=None, **kwargs):
        self.api = API(curr_path=os.path.dirname(__file__), ocr_dir=ocr_dir, mapper_config_path=mapper_config_path, **kwargs)
        self.ocr_dir = ocr_dir
        pass

    def classify_policy(self,
                        pdf_data_path = None,
                        ocr_xml_json_path = None,
                        pdf_file_path = None,
                        return_class_id_and_name = False):
        if pdf_data_path is None and ocr_xml_json_path is not None and pdf_file_path is not None:
            combined_json_path, pdf_data_path = self.api._prepare_ocr_json(ocr_xml_json_path, pdf_file_path)
        form_type, search_start_page = self.api._classify(pdf_data_path = pdf_data_path, return_class_id_and_name=return_class_id_and_name)
        print("Form Type = {}".format(form_type))
        return form_type, search_start_page

    def extract_data(self, 
                    pdf_data_path = None,
                    ocr_xml_json_path = None,
                    pdf_file_path = None,
                    clean = False):
        if pdf_data_path is None and ocr_xml_json_path is not None and pdf_file_path is not None:
            combined_json_path, pdf_data_path = self.api._prepare_ocr_json(ocr_xml_json_path, pdf_file_path)
        form_type, search_start_page = self.classify_policy(pdf_data_path)
        de_json = self.api._extract(form_type, search_start_page, pdf_data_path = pdf_data_path)

        if clean:
            self.api.clean()

    def prepare_ocr_json(self, ocr_xml_json_path, pdf_file_path):
        combined_json_path, pdf_data_path = self.api._prepare_ocr_json(ocr_xml_json_path, pdf_file_path)
        return combined_json_path, pdf_data_path

if __name__ == "__main__":
    def get_ocr_pdf_files(pdf_file_path, ocr_file_path):
        pdf_f_names = []
        ocr_f_names = []
        ocr_files = os.listdir(ocr_file_path)
        for dirpath, dirs, files in os.walk(pdf_file_path):
            for filename in files:
                fname = os.path.join(dirpath, filename)
                ocr_filename = filename.replace('.pdf', '.json')
                print(filename, ocr_filename, fname.endswith('.pdf'), ocr_filename in ocr_files)
                if fname.endswith('.pdf') and ocr_filename in ocr_files:
                    pdf_f_names.append(fname)
                    ocr_f_names.append(os.path.join(ocr_file_path, ocr_filename))
        return pdf_f_names, ocr_f_names

    print("Running Locally")
    engine = Engine(ocr_dir = "./ocr_output")
    
    ocr_dir = "/home/<USER>/namrata_clone/namrata_data_management/json/KOFAX_OCR_JSON(LENDER POLICY)"
    pdf_dir = "/home/<USER>/namrata_clone/namrata_data_management/pdfs/LendersPolicies_2kFirstRun/LendersPolicies_2kFirstRun/"
    pdf_f_names, ocr_f_names = get_ocr_pdf_files(pdf_dir, ocr_dir)
    
    print("Processing")
    engine.classify_policy(pdf_file_path = pdf_f_names[0], ocr_xml_json_path = ocr_f_names[0])



