import setuptools
from docvu_de_core.get_updated_version import VersionManager

with open('requirements.txt') as f:
    required = f.read().splitlines()

with open("README.md", "r") as fh:
    long_description = fh.read()

version = VersionManager().get_version()

setuptools.setup(
    name="docvu_de",
    version=version,
    author="visionetsystems.com",
    description="Package to create DE/DI",
    include_package_data=True,
    long_description=long_description,
    long_description_content_type="text/markdown",
    packages=setuptools.find_packages(),
    install_requires=required,
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires='>=3.8',
)