{"max_upper_block": 3, "use_upper_split_percentage": 0.45, "max_lines_for_header": 5, "max_upper_lines_for_key_search": 8, "debug": true, "default_return": "default_document_de.json", "document_types": {"variant_1_old_form": {"return": "variant_1_old_form_de.json", "header": {"include_strings": ["LOAN APPLICATION", "UNIFORM RESIDENTIAL LOAN APPLICATION", "SECTION I - TYPE OF <PERSON><PERSON><PERSON><PERSON><PERSON>"], "exclude_strings": ["REVISED", "NEW VERSION"], "length_comparison": false}, "body": {"include_strings": ["BORROWER INFORMATION", "EMPLOYMENT INFORMATION"], "exclude_strings": [], "length_comparison": false}}, "variant_2_new_form": {"return": "variant_2_new_form_de.json", "header": {"include_strings": ["REVISED LOAN APPLICATION", "NEW VERSION", "UPDATED FORM"], "exclude_strings": ["OLD VERSION"], "length_comparison": false}, "body": {"include_strings": ["PERSONAL INFORMATION", "FINANCIAL INFORMATION"], "exclude_strings": [], "length_comparison": false}, "big_font": {"include_strings": ["LOAN APPLICATION"], "exclude_strings": ["FOOTER", "PAGE"], "height_threshold": 0.6, "num_clusters": 3}}, "variant_3_digital_form": {"return": "variant_3_digital_form_de.json", "header": {"include_strings": ["DIGITAL SUBMISSION", "ELECTRONIC FORM", "E-APPLICATION"], "exclude_strings": [], "length_comparison": false}, "body": {"include_strings": ["DIGITAL SIGNATURE", "ELECTRONIC CONSENT"], "exclude_strings": [], "length_comparison": false}}, "variant_4_short_form": {"return": "variant_4_short_form_de.json", "header": {"include_strings": ["SHORT FORM", "ABBREVIATED APPLICATION", "QUICK APPLICATION"], "exclude_strings": ["FULL FORM", "COMPLETE"], "length_comparison": false}, "footer": {"include_strings": ["SHORT FORM VERSION"], "exclude_strings": [], "length_comparison": false}}, "variant_5_multilingual": {"return": "variant_5_multilingual_de.json", "header": {"include_strings": ["BILINGUAL", "SPANISH VERSION", "MULTILINGUAL FORM", "APLICACIÓN DE PRÉSTAMO"], "exclude_strings": [], "length_comparison": false}, "body": {"include_strings": ["INFORMACIÓN PERSONAL", "INFORMACIÓN FINANCIERA"], "exclude_strings": [], "length_comparison": false}}, "variant_6_state_specific": {"return": "variant_6_state_specific_de.json", "header": {"include_strings": ["CALIFORNIA ADDENDUM", "STATE SPECIFIC", "TEXAS DISCLOSURE", "FLORIDA REQUIREMENTS"], "exclude_strings": [], "length_comparison": false}, "body": {"include_strings": ["STATE REQUIREMENTS", "LOCAL REGULATIONS"], "exclude_strings": [], "length_comparison": false}, "big_font": {"include_strings": ["STATE", "DISCLOSURE"], "exclude_strings": [], "height_threshold": 0.5, "num_clusters": 2}}}}