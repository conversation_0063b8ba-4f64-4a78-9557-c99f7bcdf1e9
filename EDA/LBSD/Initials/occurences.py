"""
This script analyzes character frequencies from an Excel file and generates a histogram.  

### Workflow:  

1. **Load Data**:  
   - Reads an Excel file that contains multiple columns representing individual characters.  

2. **Preprocess Data**:  
   - Defines specific columns containing character data.  
   - Drops missing values and ensures all data is treated as strings.  

3. **Count Character Occurrences**:  
   - Uses a `Counter` to accumulate the frequency of each character across all defined columns.  

4. **Generate and Save Histogram**:  
   - Converts the character count into a pandas Series for structured analysis.  
   - Plots a bar chart to visualize the frequency of characters.  
   - Ensures axis labels, gridlines, and formatting for better readability.  
   - Saves the histogram as an image in a dedicated output folder.  

5. **Display Character Counts**:  
   - Prints the frequency of each character in a structured manner.  

### Output:  
- A histogram image (`combined_alphabet_histogram.png`) saved in the specified output folder.  
- A printed summary of character frequencies in the terminal.  

This script helps in understanding the distribution of character occurrences in the dataset, which can be useful for further data analysis and decision-making.  
"""

import os
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from collections import Counter

# Load the Excel file
file_path = "/home/<USER>/secondry_drive/Shoaib/old_augmentation/only_annotation_files/6-3-25_lbsdsdi_dataset/testing_excels/combined_excel.xlsx"
df = pd.read_excel(file_path)

# Define character columns
character_columns = ["character 1", "character 2", "character 3", "character 4", "character 5"]

# Create a new folder to store histograms
output_folder = "/home/<USER>/secondry_drive/Shoaib/old_augmentation/only_annotation_files/6-3-25_lbsdsdi_dataset/initials/combined_histograms"
os.makedirs(output_folder, exist_ok=True)  # Create folder if it doesn't exist

# Initialize a Counter to hold the frequency of each character
char_counter = Counter()

# Iterate over each character column and update the counter
for col in character_columns:
    # Drop NaN values and convert to string
    characters = df[col].dropna().astype(str)
    # Update the counter with characters from the column
    char_counter.update(characters)

# Convert the counter to a pandas Series for easier plotting
char_counts = pd.Series(char_counter).sort_index()

# Plot histogram
plt.figure(figsize=(10, 6))
ax = char_counts.plot(kind="bar", color="skyblue", edgecolor="black")
ax.set_xlabel("Alphabets")
ax.set_ylabel("Frequency")
ax.set_title("Histogram of Alphabet Frequencies Across All Character Columns")
ax.set_xticklabels(ax.get_xticklabels(), rotation=0)
ax.yaxis.set_major_locator(MaxNLocator(integer=True))  # Ensure y-axis has integer ticks
ax.grid(axis="y", linestyle="--", alpha=0.7)

# Save the histogram inside the folder
save_path = os.path.join(output_folder, "combined_alphabet_histogram.png")
plt.tight_layout()  # Adjust layout to prevent clipping of tick-labels
plt.savefig(save_path)
plt.close()  # Close the plot to prevent overlapping

# Print the count of each alphabet
print("Alphabet Frequencies Across All Character Columns:")
for char, count in char_counts.items():
    print(f"{char}: {count}")

print(f"\nHistogram saved in: {save_path}")

