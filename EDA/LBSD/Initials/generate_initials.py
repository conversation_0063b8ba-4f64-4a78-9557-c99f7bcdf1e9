"""
This script generates images containing handwritten-style initials using randomly selected fonts. 
It follows these steps:

1. **Setup Directories**:  
   - Defines the output directory where generated images will be saved.  
   - Ensures a directory exists for storing downloaded fonts.  

2. **Download Handwritten Fonts**:  
   - Checks if the required fonts are available locally; if not, downloads them from Google Fonts.  
   - Stores the fonts in the predefined font directory.  

3. **Generate Initials**:  
   - Iterates through the English alphabet (A-Z) and creates initials in the format "NA", "NB", ..., "NZ".  
   - Randomly selects a handwritten-style font and font size for each initial.  
   - Creates a blank image and measures text dimensions to generate a tightly bounded canvas.  
   - Applies random rotation and noise to simulate handwritten variations.  
   - Saves the generated image in the output folder with the font name in the filename.  

4. **Output**:  
   - The script generates 26 images, one for each letter from A-Z, with variations in font, size, and rotation.  
   - The saved images are named in the format: `N<letter>_<font_name>.jpg` (e.g., `NA_Pacifico-Regular.jpg`).  
"""

import os
import cv2
import numpy as np
import random
import requests
from PIL import Image, ImageDraw, ImageFont

# Output folder
output_folder = "/home/<USER>/secondry_drive/Shoaib/old_augmentation/only_annotation_files/6-3-25_lbsdsdi_dataset/initials/document_initials/document_initials_K"
os.makedirs(output_folder, exist_ok=True)  # Ensure output folder exists

# Font storage directory
font_dir = os.path.expanduser("~/handwritten_fonts")
os.makedirs(font_dir, exist_ok=True)  # Ensure font directory exists

# Define initials and settings
alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
text_color = (0, 0, 0)  # Black text
bg_color = (255, 255, 255)  # White background

# Handwritten-style fonts and their download URLs (Google Fonts direct links)
font_urls = {
    "Pacifico-Regular.ttf": "https://github.com/google/fonts/raw/main/ofl/pacifico/Pacifico-Regular.ttf",
    "PatrickHand-Regular.ttf": "https://github.com/google/fonts/raw/main/ofl/patrickhand/PatrickHand-Regular.ttf",
    "Comic_Sans_MS.ttf": "https://github.com/google/fonts/raw/main/ofl/comicneue/ComicNeue-Regular.ttf",
    "ComicNeue-Bold.ttf": "https://github.com/google/fonts/raw/main/ofl/comicneue/ComicNeue-Bold.ttf",
    "IndieFlower-Regular.ttf": "https://github.com/google/fonts/raw/main/ofl/indieflower/IndieFlower-Regular.ttf",
}

# Check if fonts exist, if not, download them
for font_name, url in font_urls.items():
    font_path = os.path.join(font_dir, font_name)
    if not os.path.exists(font_path) and url.startswith("http"):
        print(f"⬇️ Downloading missing font: {font_name} ...")
        try:
            response = requests.get(url, stream=True)
            if response.status_code == 200:
                with open(font_path, "wb") as font_file:
                    font_file.write(response.content)
                print(f"✅ {font_name} downloaded and saved to {font_path}")
            else:
                print(f"⚠️ Failed to download {font_name} (HTTP {response.status_code})")
        except Exception as e:
            print(f"❌ Error downloading {font_name}: {e}")

# Update font list after download
handwritten_fonts = {font_name: os.path.join(font_dir, font_name) for font_name in font_urls.keys() if os.path.exists(os.path.join(font_dir, font_name))}

# Ensure at least one valid font is available
if not handwritten_fonts:
    print("❌ No valid fonts found! Exiting script.")
    exit(1)  # Exit script if no fonts are available

# Generate 26 images with handwritten initials
for idx, char in enumerate(alphabet):
    initial = f"N{char.upper()}"  # Example: AY, BY, ..., ZY

    # Pick a random handwritten font
    font_name, font_path = random.choice(list(handwritten_fonts.items()))

    # Select a random font size
    font_size = random.randint(80, 120)

    # Load selected font (fallback to PIL's default if all fails)
    try:
        font = ImageFont.truetype(font_path, font_size)
    except IOError:
        print(f"⚠️ Failed to load {font_path}. Using default PIL font.")
        font = ImageFont.load_default()

    # Create a temporary canvas to measure text size
    temp_img = Image.new("RGB", (1000, 500), bg_color)  # Large enough to fit any text
    draw = ImageDraw.Draw(temp_img)

    # Get text bounding box
    text_bbox = draw.textbbox((0, 0), initial, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]

    # Create final image with tight bounding box
    img_pil = Image.new("RGB", (text_width + 20, text_height + 20), bg_color)  # Add slight padding
    draw = ImageDraw.Draw(img_pil)

    # Apply random rotation (to simulate handwritten variation)
    angle = random.randint(-10, 10)

    # Draw text with random offset
    offset_x = random.randint(2, 8)
    offset_y = random.randint(2, 8)
    draw.text((offset_x, offset_y), initial, font=font, fill=text_color)

    # Rotate the image
    img_pil = img_pil.rotate(angle, expand=True, fillcolor=bg_color)

    # Convert to OpenCV format
    img = np.array(img_pil)

    # Add random noise to simulate ink inconsistencies
    noise = np.random.randint(0, 30, img.shape, dtype='uint8')
    img = cv2.subtract(img, noise)

    # Save the image with font name in the filename
    output_filename = f"{initial}_{font_name.replace('.ttf', '')}.jpg"
    output_path = os.path.join(output_folder, output_filename)
    cv2.imwrite(output_path, img, [cv2.IMWRITE_JPEG_QUALITY, 100])

    print(f"✅ Saved: {output_path} with font {font_name} (Size: {img_pil.size}) and rotation {angle}°")

print(f"✅ Generated 26 images with handwritten-style effects!")
