import fitz  # PyMuPDF
import json


def load_json(json_file):
    with open(json_file, 'r') as file:
        data = json.load(file)
    return data


def draw_boxes_on_pdf(pdf_file, json_data, normalised=True):
    doc = fitz.open(pdf_file)

    # Process FieldData
    for page_data in json_data["Value"]["FieldData"]:
        if page_data["PageNumber"] > 0:  # Check if PageNumber is valid
            draw_box(doc, page_data, normalised=normalised)

    # Process TableData
    for table in json_data["Value"]["TableData"]:
        for row in table["Rows"]:
            for column in row["Columns"]:
                if column["PageNumber"] > 0:  # Check if PageNumber is valid
                    draw_box(doc, column, normalised=normalised)

    doc.save("annotated_output.pdf")
    doc.close()


def draw_box(doc, data, normalised=True):
    page_number = data["PageNumber"] - 1  # fitz uses 0-based index for pages
    coordinates = data["FieldValueCoordinates"]
    if coordinates["Height"] > 0 and coordinates["Width"] > 0:  # Check if the bbox is valid
        page = doc[page_number]
        rect = fitz.Rect(
                coordinates["X"] * page.rect.width if normalised else coordinates["X"],
                coordinates["Y"] * page.rect.height if normalised else coordinates["Y"],
                (coordinates["X"] + coordinates["Width"]) * page.rect.width \
                    if normalised else coordinates["X"] + coordinates["Width"],
                (coordinates["Y"] + coordinates["Height"]) * page.rect.height \
                    if normalised else coordinates["Y"] + coordinates["Height"]
        )
        page.draw_rect(rect, color=(1, 0, 0), width=1.5)  # Draw red box
        text_point = fitz.Point(rect.x0, rect.y0 - 10)  # Position for the text above the box
        page.insert_text(text_point, data["Name"], color=(1, 0, 0), fontsize=11)


if __name__ == '__main__':
    pdf_path = "/Users/<USER>/Datasets/SLS_DE/pdfs/StewartPolicies_4k//ECM_PROD000020251871.pdf"
    json_path = "/Users/<USER>/Repos/SOFTWARE-SERVICE-%20document_extraction/de_results/long_form_json_results/ECM_PROD000020251871.json"

    # Example usage
    json_data = load_json(json_path)
    draw_boxes_on_pdf(pdf_path, json_data)
