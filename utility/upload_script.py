import argparse
from azure.storage.blob import BlobServiceClient
import os

def upload_to_blob(connection_string, container_name, folder_path, overwrite=True, file_type_filter=None):
    """
    Uploads all files in the specified folder to Azure Blob Storage.
    
    Args:
    - connection_string (str): The connection string for the Azure Blob Storage account.
    - container_name (str): The name of the container to upload files to.
    - folder_path (str): The local path of the folder containing files to upload.
    - overwrite (bool): Whether to overwrite existing files. Defaults to True.
    - file_type_filter (str or list, optional): File type(s) to upload (e.g., ".whl" or [".whl", ".txt"]). Defaults to None, which means all files will be uploaded.
    """
    # Debugging: Print connection string, container name, and folder path
    print(f"DEBUG: Connection String: {connection_string}")
    print(f"DEBUG: Container Name: {container_name}")
    print(f"DEBUG: Folder Path: {folder_path}")
    
    # Create the BlobServiceClient object
    try:
        print(f"DEBUG: Connecting to Azure Blob Storage...")
        blob_service_client = BlobServiceClient.from_connection_string(connection_string)
        print("DEBUG: Successfully connected to Azure Blob Storage.")
    except Exception as e:
        print(f"ERROR: Failed to connect to Azure Blob Storage. {str(e)}")
        return
    
    # Get the container client
    try:
        print(f"DEBUG: Getting container client for container: {container_name}")
        container_client = blob_service_client.get_container_client(container_name)
    except Exception as e:
        print(f"ERROR: Failed to get container client for {container_name}. {str(e)}")
        return

    # Ensure the container exists
    try:
        if not container_client.exists():
            print(f"DEBUG: Container {container_name} does not exist. Creating container.")
            container_client.create_container()
        else:
            print(f"DEBUG: Container {container_name} already exists.")
    except Exception as e:
        print(f"ERROR: Failed to check/create container {container_name}. {str(e)}")
        return

    # Initialize counters
    total_files = 0
    uploaded_files = 0
    failed_files = []

    # Iterate over files in the specified folder
    print(f"DEBUG: Looking for files in folder: {folder_path}")
    for root, dirs, files in os.walk(folder_path):
        for file_name in files:
            total_files += 1
            print(f"DEBUG: Processing file: {file_name}")

            # Apply file type filter if provided
            if file_type_filter:
                if isinstance(file_type_filter, str):
                    file_type_filter = [file_type_filter]
                if not any(file_name.endswith(ft) for ft in file_type_filter):
                    print(f"DEBUG: Skipped {file_name} due to file type filter.")
                    continue

            file_path = os.path.join(root, file_name)
            #blob_name = os.path.relpath(file_path, folder_path)
            blob_name = os.path.join("docvu_de", os.path.relpath(file_path, folder_path))  # Update blob_name to include folder

            # Create a blob client for each file
            try:
                print(f"DEBUG: Creating blob client for {blob_name}")
                blob_client = container_client.get_blob_client(blob_name)
            except Exception as e:
                print(f"ERROR: Failed to create blob client for {blob_name}. {str(e)}")
                failed_files.append(file_name)
                continue

            # Attempt to upload the file
            try:
                # Check if blob exists and handle overwrite option
                if not blob_client.exists() or overwrite:
                    print(f"DEBUG: Uploading {file_name} to {container_name}/{blob_name}")
                    with open(file_path, "rb") as data:
                        blob_client.upload_blob(data, overwrite=overwrite)
                    print(f"DEBUG: Uploaded {file_name} to {container_name}/{blob_name}")
                    uploaded_files += 1
                else:
                    print(f"DEBUG: Skipped {file_name} as it already exists in {container_name}/{blob_name}")
            except Exception as e:
                print(f"ERROR: Failed to upload {file_name}. Error: {str(e)}")
                failed_files.append(file_name)

    # Log progress summary
    print(f"DEBUG: Upload complete. Total files: {total_files}, Uploaded: {uploaded_files}, Failed: {len(failed_files)}")
    if failed_files:
        print("DEBUG: Failed files:", failed_files)


def main():
    parser = argparse.ArgumentParser(description='Upload files to Azure Blob Storage.')
    parser.add_argument('--connection_string', type=str, required=True, help='Azure Storage connection string')
    parser.add_argument('--container_name', type=str, required=True, help='Name of the Azure Blob container')
    parser.add_argument('--folder_path', type=str, required=True, help='Path of the folder to upload')
    parser.add_argument('--overwrite', action='store_true', help='Overwrite existing files (default is True)')
    parser.add_argument('--file_type_filter', type=str, nargs='+', help='File types to upload (e.g. .whl)')

    args = parser.parse_args()

    upload_to_blob(args.connection_string, args.container_name, args.folder_path, overwrite=args.overwrite, file_type_filter=args.file_type_filter)


if __name__ == "__main__":
    main()