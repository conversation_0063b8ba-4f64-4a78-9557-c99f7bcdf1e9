import os
import json
import pandas as pd
import time

def extract_flattened_postprocessing_values(json_directory):
    records = []

    for filename in os.listdir(json_directory):
        if not filename.endswith('.json'):
            continue

        filepath = os.path.join(json_directory, filename)
        with open(filepath, 'r') as f:
            try:
                json_data = json.load(f)
            except Exception as e:
                print(f"❌ Error parsing {filename}: {e}")
                continue

        file_row = {"file_name": filename}

        # --- Extract FieldData ---
        for field in json_data.get("Value", {}).get("FieldData", []):
            name = field.get("Name", "")
            post_val = field.get("PostProcessingValue", "") or field.get("Value", "")
            if post_val:
                file_row[name] = post_val

        # --- Extract and merge TableData ---
        for table in json_data.get("Value", {}).get("TableData", []):
            table_name = table.get("Name", "").strip()
            all_values = []

            for row in table.get("Rows", []):
                for col in row.get("Columns", []):
                    val = col.get("PostProcessingValue", "") or col.get("Value", "")
                    if val:
                        all_values.append(val)

            if all_values:
                # ✅ Store all table values as list of values under a single column
                file_row[table_name] = ', '.join(all_values)

        records.append(file_row)

    return pd.DataFrame(records)

if __name__ == '__main__':
    json_directory = "/home/<USER>/docvufileshareuat/Shoaib/Credit Review/9000007 - Alliant Membership Form/de_result"
    save_results_dir = f'/home/<USER>/docvufileshareuat/Shoaib/Credit Review/9000007 - Alliant Membership Form/de_result/excel_alliant/{int(time.time())}'
    os.makedirs(save_results_dir, exist_ok=True)

    df = extract_flattened_postprocessing_values(json_directory)
    output_path = os.path.join(save_results_dir, "DOT_seq.xlsx")
    df.to_excel(output_path, index=False)

    print("✅ Flattened data with single table field and multiple values saved to:", output_path)
