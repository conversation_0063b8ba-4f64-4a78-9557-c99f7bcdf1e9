#!/usr/bin/env python3
"""
Example usage of the enhanced multi-variant document classifier.
Shows how to use the updated Classify.py with existing and new features.
"""

from docvu_de_core.policy_classifier.Classify import Classify
import json


def example_existing_code():
    """Example showing that existing code continues to work unchanged."""
    
    print("=" * 60)
    print("EXISTING CODE COMPATIBILITY")
    print("=" * 60)
    
    # Your existing configuration (unchanged)
    existing_config = {
        "max_upper_block": 3,
        "use_upper_split_percentage": 0.45,
        "debug": True,
        "default_return": "docvu_de_core/de_config/9000001-1003_de.json",
        "document_types": {
            "old_form": {
                "return": "docvu_de_core/de_config/9000001-1003_de.json",
                "header": {
                    "include_strings": ["section 1", "section 2", "section3", "personal information"],
                    "exclude_strings": [],
                    "length_comparison": False
                },
                "body": {
                    "include_strings": ["section 1", "section 2", "section3", "personal information"],
                    "exclude_strings": [],
                    "length_comparison": False
                }
            }
        }
    }
    
    # Your existing code (unchanged)
    classifier = Classify(existing_config)
    
    # Simulate processing a document (your existing process_form call)
    # result = classifier.process_form('path/to/your/document.json')
    
    # For demo, we'll use direct classification
    sample_data = {1: ["section 1", "personal information", "some other text"]}
    height_data = {1: [14, 12, 10]}
    
    result = classifier.classify_document(sample_data, height_data)
    
    print("✅ Your existing code works exactly the same!")
    print(f"Document Type: {result['document_type']}")
    print(f"Starting Page: {result.get('starting_page', 'N/A')}")
    print(f"Total Pages: {result.get('total_pages', 'N/A')}")
    print()


def example_enhanced_features():
    """Example showing new enhanced multi-variant features."""
    
    print("=" * 60)
    print("ENHANCED MULTI-VARIANT FEATURES")
    print("=" * 60)
    
    # Enhanced configuration with 5-6 variants
    enhanced_config = {
        "enable_multi_variant": True,
        "confidence_threshold": 0.7,
        "enable_confidence_scoring": True,
        "case_sensitive_matching": False,
        "partial_string_matching": True,
        "debug": True,
        "default_return": "default_config.json",
        "document_types": {
            "loan_application_v1": {
                "return": "loan_app_v1_config.json",
                "header": {
                    "include_strings": ["1003", "LOAN APPLICATION", "BORROWER"],
                    "exclude_strings": ["REVISED", "NEW VERSION"]
                },
                "body": {
                    "include_strings": ["Property Address", "Loan Amount"],
                    "exclude_strings": []
                }
            },
            "loan_application_v2": {
                "return": "loan_app_v2_config.json", 
                "header": {
                    "include_strings": ["1003", "REVISED", "NEW VERSION"],
                    "exclude_strings": ["SPANISH"]
                },
                "body": {
                    "include_strings": ["Joint Credit", "Co-Borrower"],
                    "exclude_strings": []
                }
            },
            "ach_authorization": {
                "return": "ach_config.json",
                "header": {
                    "include_strings": ["ACH Authorization", "Autopay"],
                    "exclude_strings": ["1003", "LOAN APPLICATION"]
                },
                "body": {
                    "include_strings": ["Bank Account", "Routing Number"],
                    "exclude_strings": []
                }
            },
            "closing_disclosure": {
                "return": "closing_config.json",
                "header": {
                    "include_strings": ["Closing Disclosure", "Final Terms"],
                    "exclude_strings": ["1003", "ACH"]
                },
                "body": {
                    "include_strings": ["Closing Costs", "Cash to Close"],
                    "exclude_strings": []
                }
            },
            "appraisal_report": {
                "return": "appraisal_config.json",
                "header": {
                    "include_strings": ["Appraisal Report", "Property Valuation"],
                    "exclude_strings": ["1003", "Closing"]
                },
                "body": {
                    "include_strings": ["Comparable Sales", "Market Analysis"],
                    "exclude_strings": []
                }
            }
        }
    }
    
    classifier = Classify(enhanced_config)
    
    # Test different document types
    test_documents = [
        {
            "name": "1003 Application V1",
            "data": {1: ["1003", "LOAN APPLICATION", "BORROWER", "Property Address", "Loan Amount"]},
            "heights": {1: [16, 14, 12, 10, 10]}
        },
        {
            "name": "1003 Application V2 (Revised)",
            "data": {1: ["1003", "REVISED", "NEW VERSION", "Joint Credit", "Co-Borrower"]},
            "heights": {1: [16, 14, 12, 10, 10]}
        },
        {
            "name": "ACH Authorization",
            "data": {1: ["ACH Authorization", "Autopay Setup", "Bank Account", "Routing Number"]},
            "heights": {1: [16, 14, 12, 10]}
        },
        {
            "name": "Closing Disclosure",
            "data": {1: ["Closing Disclosure", "Final Terms", "Closing Costs", "Cash to Close"]},
            "heights": {1: [16, 14, 12, 10]}
        },
        {
            "name": "Appraisal Report",
            "data": {1: ["Appraisal Report", "Property Valuation", "Comparable Sales", "Market Analysis"]},
            "heights": {1: [16, 14, 12, 10]}
        }
    ]
    
    for doc in test_documents:
        print(f"🧪 Testing: {doc['name']}")
        print("-" * 40)
        
        result = classifier.classify_document(doc['data'], doc['heights'])
        
        # Enhanced result format
        print(f"📄 Document Type: {result.get('document_type')}")
        print(f"🏷️  Variant Name: {result.get('variant_name')}")
        print(f"📊 Confidence: {result.get('confidence', 0):.2f}")
        print(f"✅ Status: {result.get('classification_status')}")
        
        # Show all variant scores
        if 'all_variant_scores' in result:
            print("📈 All Variant Scores:")
            for variant, score in result['all_variant_scores'].items():
                print(f"   • {variant}: {score:.2f}")
        
        print()


def example_legacy_mode():
    """Example showing explicit legacy mode for backward compatibility."""
    
    print("=" * 60)
    print("EXPLICIT LEGACY MODE")
    print("=" * 60)
    
    # Force legacy mode explicitly
    legacy_config = {
        "use_legacy_mode": True,  # Explicitly enable legacy mode
        "enable_multi_variant": False,  # Disable enhanced features
        "debug": True,
        "default_return": "legacy_default.json",
        "document_types": {
            "standard_form": {
                "return": "standard_form_config.json",
                "header": {
                    "include_strings": ["FORM", "APPLICATION"],
                    "exclude_strings": []
                }
            }
        }
    }
    
    classifier = Classify(legacy_config)
    
    sample_data = {1: ["FORM", "APPLICATION", "some content"]}
    height_data = {1: [14, 12, 10]}
    
    result = classifier.classify_document(sample_data, height_data)
    
    print("✅ Legacy mode explicitly enabled")
    print(f"Document Type: {result['document_type']}")
    print(f"Section: {result.get('section', 'N/A')}")
    print(f"Starting Page: {result.get('starting_page', 'N/A')}")
    print()


if __name__ == '__main__':
    print("🚀 ENHANCED CLASSIFY.PY USAGE EXAMPLES")
    print("Demonstrating backward compatibility and new features")
    print()
    
    # Show that existing code continues to work
    example_existing_code()
    
    # Show new enhanced features
    example_enhanced_features()
    
    # Show explicit legacy mode
    example_legacy_mode()
    
    print("=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print("✅ Existing code works unchanged")
    print("✅ Enhanced multi-variant classification available")
    print("✅ Confidence scoring for better accuracy")
    print("✅ Support for 5-6+ document variants")
    print("✅ Backward compatibility maintained")
    print("✅ Legacy mode available when needed")
    print()
    print("📖 Next Steps:")
    print("1. Test with your existing configurations")
    print("2. Gradually migrate to enhanced configurations")
    print("3. Adjust confidence thresholds based on your data")
    print("4. Add new document variants as needed")
