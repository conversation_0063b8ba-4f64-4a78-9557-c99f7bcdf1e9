import json
import os
import argparse
import pandas as pd  # Ensure this import is necessary, remove if not used
from docvu_de_core.api import *
import datetime  # Ensure this import is necessary, remove if not used
import yaml


class TestLocal:
    def __init__(self):
        self.api = API(
            curr_path = os.path.dirname(__file__), 
            ocr_dir = "./ocr_output" , 
            load_slm = False,
            load_yolo_v8_for_checkbox=True,
            load_yolo_v8_for_lbsd_model=True,
            load_subdivision_model = False,
            overwrite_model = False,
            load_ner = False
        )
        pass

    def extract_data(self, pdf_data_path=None,
                     ocr_xml_json_path=None,
                     pdf_file_path=None,
                     to_extract_field=None,
                     results_dir_path = './de_results',
                     document_id = None,
                     fetch_prev_to_extract_field=False,
                     classification_required=True,
                     separate_table_data=True,
                     client_id = None):
        os.makedirs(results_dir_path, exist_ok=True)
        print('Running extract_data')
        form_type, search_start_page =document_id , 1

        de_results = self.api._extract(
            form_type,
            search_start_page,
            document_id = document_id,
            pdf_data_path=pdf_data_path,
            ocr_xml_json_path=ocr_xml_json_path,
            pdf_file_path=pdf_file_path,
            to_extract_field=to_extract_field,
            return_indexing_info=False,
            fetch_prev_to_extract_field=fetch_prev_to_extract_field,
            separate_table_data=separate_table_data,
            for_testing=True,
            client_id = client_id)

        file_name = os.path.basename(pdf_file_path)
        file_path = os.path.join(results_dir_path, (file_name + '.json') if not file_name.endswith('json') else file_name)
        with open(file_path, 'w', encoding='utf8') as json_file:
            json.dump(de_results, json_file)
        return de_results

def load_config(config_path):
    with open(config_path, 'r') as file:
        return yaml.safe_load(file)
    
def run():
    print("Running Locally")

    config = load_config('./local_test_params_valon.yml')

    settings = config['settings']
    directories = config['directories']

    print(f"Running document extraction with the following settings:")
    for key, value in settings.items():
        print(f"{key}: {value}")
    for key, value in directories.items():
        print(f"{key}: {value}")

    # Extract directories for ocr_data and pdf_data
    ocr_data_dir = directories['ocr_data']
    pdf_data_dir = directories['pdf_data']
    results_data_dir = directories['results_data']
    client_id = settings['client_id'] if 'client_id' in settings else None
    test_local = TestLocal()

    if settings['mode'] == 'single':
        file_name = settings['file_name']
        classification_required = settings['classification_required']
        to_extract_field = settings['to_extract_field']

        policy_path = os.path.join(ocr_data_dir, file_name + ".json")
        if not os.path.exists(policy_path):
            policy_path = os.path.join(ocr_data_dir, file_name + "_textract_ocr.json")
        policy_pdf = os.path.join(pdf_data_dir, file_name + ".pdf")
        print(policy_path, policy_pdf)
        test_local.extract_data(pdf_data_path=None,
                                ocr_xml_json_path=policy_path,
                                pdf_file_path=policy_pdf,
                                results_dir_path =results_data_dir,
                                document_id = str(settings['type']), 
                                to_extract_field=to_extract_field,
                                fetch_prev_to_extract_field=True,
                                classification_required=classification_required,
                                client_id = client_id)
    
    elif settings['mode'] == 'all':
        for pdf_file in os.listdir(pdf_data_dir):
            if pdf_file.endswith('.pdf'):
                print("pdf_file", pdf_file)
                ocr_file = pdf_file.replace('.pdf', '.json')
                policy_path = os.path.join(ocr_data_dir, ocr_file)
                if not os.path.exists(policy_path):
                    ocr_file = pdf_file.replace('.pdf', '_textract_ocr.json')
                    policy_path = os.path.join(ocr_data_dir, ocr_file)
                policy_pdf = os.path.join(pdf_data_dir, pdf_file)
                print("policy_pdf", policy_pdf)

                if os.path.exists(policy_pdf):
                    print(policy_path, policy_pdf)
                    test_local.extract_data(pdf_data_path=None,
                                            ocr_xml_json_path=policy_path,
                                            pdf_file_path=policy_pdf,
                                            results_dir_path =results_data_dir,
                                            document_id = str(settings['type']), 
                                            to_extract_field=settings['to_extract_field'],
                                            fetch_prev_to_extract_field=True,
                                            classification_required=settings['classification_required'],
                                            client_id = client_id)

if __name__ == "__main__":
    run()