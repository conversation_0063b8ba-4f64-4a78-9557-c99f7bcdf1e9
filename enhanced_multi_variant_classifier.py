import json
from typing import List, Tu<PERSON>, Dict, Optional
from docvu_de_core.page_info_parser.BigFontClusterer import BigFontClusterer


class EnhancedMultiVariantClassifier:
    """
    Enhanced classifier that can handle 5-6 document variants with improved
    classification logic and confidence scoring.
    """
    
    def __init__(self, config):
        if isinstance(config, str):
            with open(config, 'r') as f:
                self.config = json.load(f)
        elif isinstance(config, dict):
            self.config = config

        self.max_upper_block = self.config.get('max_upper_block', 3)
        self.use_upper_split_percentage = self.config.get('use_upper_split_percentage', 0.45)
        self.max_lines_for_header = self.config.get('max_lines_for_header', 5)
        self.max_upper_lines_for_key_search = self.config.get('max_upper_lines_for_key_search', 8)
        self.debug = self.config.get('debug', True)
        self.confidence_threshold = self.config.get('confidence_threshold', 0.6)

    def calculate_match_confidence(self, found_matches: List[str], 
                                 include_strings: List[str], 
                                 exclude_strings: List[str]) -> float:
        """Calculate confidence score for matches"""
        if not include_strings:
            return 0.0
            
        # Calculate inclusion score
        inclusion_score = len(found_matches) / len(include_strings)
        
        # Penalty for exclusion matches
        exclusion_penalty = 0.0
        if exclude_strings:
            for exclude_str in exclude_strings:
                if any(exclude_str.lower() in match.lower() for match in found_matches):
                    exclusion_penalty += 0.2
        
        confidence = max(0.0, inclusion_score - exclusion_penalty)
        return min(1.0, confidence)

    def classify_document_with_confidence(self, block_texts: Dict[int, List[str]], 
                                        block_text_height: Dict[int, List[int]]) -> Dict:
        """
        Classify document with confidence scoring for each variant
        """
        variant_scores = {}
        
        for doc_type, sections in self.config['document_types'].items():
            return_value = sections.get('return')
            total_confidence = 0.0
            section_count = 0
            
            for section_name, section_config in sections.items():
                if section_name == 'return':
                    continue
                    
                method = getattr(self, f"check_{section_name}", None)
                if not method:
                    continue
                    
                if section_name == 'big_font':
                    result = self.check_big_font(block_texts, section_config)
                    confidence = 1.0 if result else 0.0
                else:
                    result, confidence = self.check_section_with_confidence(
                        block_texts, block_text_height, section_config
                    )
                
                total_confidence += confidence
                section_count += 1
            
            # Average confidence across all sections
            avg_confidence = total_confidence / section_count if section_count > 0 else 0.0
            variant_scores[doc_type] = {
                'confidence': avg_confidence,
                'return': return_value
            }
        
        # Find best match
        best_variant = max(variant_scores.items(), key=lambda x: x[1]['confidence'])
        
        if best_variant[1]['confidence'] >= self.confidence_threshold:
            return {
                'document_type': best_variant[1]['return'],
                'variant_name': best_variant[0],
                'confidence': best_variant[1]['confidence'],
                'all_scores': variant_scores,
                'classification_status': 'success'
            }
        else:
            return {
                'document_type': self.config.get('default_return'),
                'variant_name': 'default',
                'confidence': 0.0,
                'all_scores': variant_scores,
                'classification_status': 'low_confidence'
            }

    def check_section_with_confidence(self, block_texts: Dict[int, List[str]], 
                                    block_text_height: Dict[int, List[int]], 
                                    config: Dict) -> Tuple[bool, float]:
        """Check section and return confidence score"""
        include_strings = config.get('include_strings', [])
        exclude_strings = config.get('exclude_strings', [])
        
        found_matches = []
        
        for page, text_list in block_texts.items():
            for text in text_list:
                for include_str in include_strings:
                    if include_str.lower() in text.lower():
                        found_matches.append(text)
        
        confidence = self.calculate_match_confidence(found_matches, include_strings, exclude_strings)
        return len(found_matches) > 0, confidence

    def check_header(self, block_texts: Dict[int, List[str]], 
                    block_text_height: Dict[int, List[int]], 
                    config: Dict) -> Tuple[bool, float]:
        return self.check_section_with_confidence(block_texts, block_text_height, config)

    def check_body(self, block_texts: Dict[int, List[str]], 
                  block_text_height: Dict[int, List[int]], 
                  config: Dict) -> Tuple[bool, float]:
        return self.check_section_with_confidence(block_texts, block_text_height, config)

    def check_footer(self, block_texts: Dict[int, List[str]], 
                    block_text_height: Dict[int, List[int]], 
                    config: Dict) -> Tuple[bool, float]:
        return self.check_section_with_confidence(block_texts, block_text_height, config)

    def check_big_font(self, block_texts: Dict[int, List[str]], config: Dict) -> bool:
        """Check for big font matches"""
        big_font_config = config.get('big_font', {})
        height_threshold = big_font_config.get('height_threshold', 0.5)
        num_clusters = big_font_config.get('num_clusters', 3)

        big_font_clusterer = BigFontClusterer(height_threshold=height_threshold, num_clusters=num_clusters)
        
        features = big_font_clusterer.extract_features(block_texts)
        labels = big_font_clusterer.cluster_text_lines(features)
        large_font_texts = big_font_clusterer.extract_large_font_text(block_texts, labels)

        matches = [text for text in large_font_texts if any(
                inc.lower() in text.lower() for inc in big_font_config.get('include_strings', [])
        ) and not any(
                exc.lower() in text.lower() for exc in big_font_config.get('exclude_strings', [])
        )]
        
        return len(matches) > 0


# Usage example for testing multiple variants
if __name__ == '__main__':
    # Load the multi-variant configuration
    classifier = EnhancedMultiVariantClassifier('example_multi_variant_classify_config.json')
    
    # Example usage
    sample_json_path = 'path/to/your/combined.json'
    # result = classifier.process_form(sample_json_path)
    # print(f"Classification Result: {result}")
